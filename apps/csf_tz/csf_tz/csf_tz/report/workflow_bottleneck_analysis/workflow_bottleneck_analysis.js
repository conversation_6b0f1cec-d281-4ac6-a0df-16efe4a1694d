// Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Workflow Bottleneck Analysis"] = {
    "filters": [
        {
            "fieldname": "workflow",
            "label": __("Workflow"),
            "fieldtype": "Link",
            "options": "Workflow"
        },
        {
            "fieldname": "reference_doctype",
            "label": __("Reference DocType"),
            "fieldtype": "Link",
            "options": "DocType"
        },
        {
            "fieldname": "user",
            "label": __("User"),
            "fieldtype": "Link",
            "options": "User"
        },
        {
            "fieldname": "threshold_percentage",
            "label": __("Threshold (% above average)"),
            "fieldtype": "Int",
            "default": 50,
            "description": "Show transitions that exceed average duration by this percentage"
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1),
            "reqd": 0
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today(),
            "reqd": 0
        }
    ]
};