// Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Transition Frequency Analysis"] = {
    "filters": [
        {
            "fieldname": "workflow",
            "label": __("Workflow"),
            "fieldtype": "Link",
            "options": "Workflow"
        },
        {
            "fieldname": "reference_doctype",
            "label": __("Reference DocType"),
            "fieldtype": "Link",
            "options": "DocType"
        },
        {
            "fieldname": "from_state",
            "label": __("From State"),
            "fieldtype": "Data"
        },
        {
            "fieldname": "to_state",
            "label": __("To State"),
            "fieldtype": "Data"
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -3),
            "reqd": 0
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today(),
            "reqd": 0
        }
    ]
};