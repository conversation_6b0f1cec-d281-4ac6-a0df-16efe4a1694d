2025-05-28 10:04:18,550 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-28 10:04:19,322 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-28 10:04:19,331 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-28 10:04:19,338 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-28 10:04:19,342 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-28 10:04:19,347 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-28 10:04:19,350 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-28 10:04:19,353 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-28 10:04:19,356 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-28 10:04:19,358 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-28 10:04:19,361 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-28 10:04:19,366 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-28 10:04:19,370 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-28 10:04:19,374 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-28 10:04:19,377 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-28 10:04:19,381 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-28 10:04:19,385 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-28 10:04:19,389 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-28 10:04:19,392 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-28 10:04:19,395 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-28 10:04:19,400 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-28 10:04:19,403 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-28 10:04:19,407 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-28 10:04:19,410 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-28 10:04:19,413 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-28 10:04:19,417 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-28 10:04:19,420 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-28 10:04:19,424 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-28 10:04:19,428 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-28 10:04:19,431 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-28 10:04:19,435 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-28 10:04:19,439 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-28 10:04:19,442 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-28 10:04:19,445 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-28 10:04:19,449 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-28 10:04:19,456 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-28 10:04:19,460 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-28 10:04:19,463 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-28 10:04:19,470 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-28 10:04:19,473 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-28 10:04:19,476 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-28 10:05:19,518 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-28 10:05:19,522 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-28 10:05:19,525 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-28 10:05:19,527 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-28 10:05:19,529 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-28 10:05:19,532 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-28 10:05:19,534 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-28 10:05:19,536 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-28 10:05:19,538 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-28 10:05:19,540 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-28 10:05:19,541 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-28 10:05:19,543 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-28 10:05:19,545 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-28 10:05:19,546 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-28 10:05:19,548 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-28 10:05:19,550 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-28 10:05:19,552 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-28 10:05:19,553 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-28 10:05:19,555 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-28 10:05:19,557 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-28 10:05:19,558 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-28 10:05:19,560 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-28 10:05:19,562 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-28 10:05:19,563 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-28 10:05:19,565 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-28 10:05:19,566 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-28 10:05:19,568 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-28 10:05:19,569 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-28 10:05:19,571 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-28 10:05:19,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-28 10:05:19,574 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-28 10:05:19,576 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-28 10:05:19,577 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-28 10:05:19,579 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-28 10:05:19,581 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-28 10:05:19,582 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-28 10:05:19,584 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-28 10:05:19,585 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-28 10:05:19,587 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-28 10:05:19,589 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-28 10:05:19,590 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-28 10:05:19,592 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-28 10:08:21,791 ERROR scheduler Exception in Enqueue Events for Site integration
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socket.py", line 863, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.11/socket.py", line 848, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-05-29 14:27:39,451 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:27:39,640 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:27:39,642 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:27:39,644 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:27:39,646 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:27:39,648 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:27:39,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:27:39,652 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:27:39,654 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:27:39,656 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:27:39,658 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:27:39,660 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:27:39,662 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:27:39,664 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:27:39,666 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:27:39,668 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:27:39,671 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:27:39,673 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:27:39,675 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:27:39,677 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:27:39,679 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:27:39,681 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:27:39,683 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:27:39,685 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:27:39,687 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:27:39,689 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:27:39,691 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:27:39,693 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:27:39,695 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:27:39,697 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:27:39,699 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:27:39,701 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:27:39,703 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:27:39,704 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:27:39,706 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:27:39,708 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:27:39,710 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:27:39,712 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:27:39,714 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:27:39,716 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:27:39,718 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:27:39,720 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:28:43,709 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:28:43,712 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:28:43,713 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:28:43,715 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:28:43,716 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:28:43,718 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:28:43,720 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:28:43,721 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:28:43,723 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:28:43,724 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:28:43,726 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:28:43,727 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:28:43,729 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:28:43,730 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:28:43,732 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:28:43,734 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:28:43,735 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:28:43,737 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:28:43,738 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:28:43,740 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:28:43,741 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:28:43,743 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:28:43,746 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:28:43,748 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:28:43,751 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:28:43,753 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:28:43,755 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:28:43,758 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:28:43,760 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:28:43,762 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:28:43,765 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:28:43,767 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:28:43,770 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:28:43,772 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:28:43,775 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:28:43,777 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:28:43,779 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:28:43,821 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:28:43,823 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:28:43,825 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:28:43,826 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:28:43,828 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:29:45,076 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:29:45,079 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:29:45,081 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:29:45,084 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:29:45,087 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:29:45,091 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:29:45,094 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:29:45,096 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:29:45,100 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:29:45,103 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:29:45,107 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:29:45,110 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:29:45,112 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:29:45,115 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:29:45,117 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:29:45,120 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:29:45,123 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:29:45,126 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:29:45,129 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:29:45,131 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:29:45,134 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:29:45,138 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:29:45,142 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:29:45,145 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:29:45,148 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:29:45,151 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:29:45,156 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:29:45,160 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:29:45,163 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:29:45,167 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:29:45,173 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:29:45,177 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:29:45,180 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:29:45,184 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:29:45,187 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:29:45,192 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:29:45,197 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:29:45,203 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:29:45,208 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:29:45,212 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:29:45,215 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:29:45,219 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:30:47,483 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:30:47,485 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:30:47,488 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:30:47,489 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:30:47,491 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:30:47,493 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:30:47,495 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:30:47,498 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:30:47,500 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:30:47,503 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:30:47,506 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:30:47,508 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:30:47,510 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:30:47,513 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:30:47,517 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:30:47,520 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:30:47,522 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:30:47,524 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:30:47,526 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:30:47,528 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:30:47,530 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:30:47,532 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:30:47,534 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:30:47,537 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:30:47,539 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:30:47,541 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:30:47,544 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:30:47,546 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:30:47,550 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:30:47,553 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:30:47,556 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:30:47,558 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:30:47,560 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:30:47,563 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:30:47,565 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:30:47,568 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:30:47,571 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:30:47,573 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:30:47,575 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:30:47,578 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:30:47,580 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:30:47,583 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:31:48,531 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:31:48,535 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:31:48,539 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:31:48,542 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:31:48,545 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:31:48,548 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:31:48,551 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:31:48,554 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:31:48,558 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:31:48,560 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:31:48,563 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:31:48,566 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:31:48,570 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:31:48,573 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:31:48,576 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:31:48,579 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:31:48,581 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:31:48,584 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:31:48,587 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:31:48,590 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:31:48,593 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:31:48,596 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:31:48,598 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:31:48,601 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:31:48,605 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:31:48,609 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:31:48,611 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:31:48,614 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:31:48,616 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:31:48,619 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:31:48,623 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:31:48,626 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:31:48,629 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:31:48,632 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:31:48,635 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:31:48,639 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:31:48,642 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:31:48,645 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:31:48,647 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:31:48,651 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:31:48,654 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:31:48,657 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:32:49,435 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 14:32:49,438 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 14:32:49,440 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:32:49,443 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 14:32:49,445 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 14:32:49,448 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:32:49,450 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:32:49,452 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 14:32:49,454 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:32:49,455 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 14:32:49,457 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 14:32:49,459 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 14:32:49,461 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 14:32:49,463 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:32:49,465 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:32:49,467 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:32:49,469 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 14:32:49,471 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 14:32:49,473 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:32:49,474 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 14:32:49,476 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:32:49,478 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 14:32:49,481 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 14:32:49,482 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 14:32:49,484 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 14:32:49,486 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:32:49,487 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 14:32:49,489 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 14:32:49,491 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 14:32:49,492 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 14:32:49,494 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:32:49,496 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 14:32:49,498 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 14:32:49,500 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 14:32:49,501 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 14:32:49,503 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 14:32:49,505 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 14:32:49,507 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 14:32:49,508 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 14:32:49,511 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 14:32:49,513 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 14:32:49,515 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 14:33:50,365 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 14:33:50,367 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 14:33:50,376 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:33:50,380 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 14:33:50,390 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 14:33:50,394 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 14:33:50,397 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:33:50,407 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 14:33:50,462 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 14:33:50,464 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 14:33:50,482 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 14:33:50,492 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:01:11,028 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 15:01:11,030 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 15:01:11,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:01:11,033 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 15:01:11,035 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:01:11,037 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:01:11,039 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 15:01:11,040 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 15:01:11,043 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 15:01:11,045 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 15:01:11,046 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:01:11,048 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:01:11,050 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 15:01:11,052 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 15:01:11,054 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 15:01:11,056 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 15:01:11,058 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 15:01:11,060 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 15:01:11,062 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 15:01:11,063 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:01:11,065 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 15:01:11,068 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 15:01:11,070 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 15:01:11,071 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:01:11,073 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 15:01:11,075 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:01:11,077 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 15:01:11,078 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 15:01:11,080 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 15:01:11,082 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 15:01:11,084 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 15:01:11,086 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 15:01:11,087 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 15:01:11,090 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 15:01:11,092 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 15:01:11,094 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:01:11,096 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 15:01:11,097 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:01:11,099 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:01:11,102 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 15:01:11,104 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 15:01:11,106 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 15:02:11,555 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:02:11,558 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 15:02:11,561 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 15:02:11,564 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 15:02:11,566 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:02:11,568 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 15:02:11,570 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 15:02:11,573 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:02:11,575 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 15:02:11,576 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:02:11,578 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 15:02:11,580 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:02:12,370 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 15:02:12,379 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:02:12,387 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 15:02:12,393 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:02:12,398 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:02:12,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 15:02:12,405 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 15:02:12,408 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 15:02:12,411 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:02:12,413 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 15:02:12,415 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 15:02:12,417 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 15:02:12,419 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 15:02:12,421 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 15:02:12,422 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 15:02:12,424 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 15:02:12,425 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 15:02:12,427 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 15:02:12,428 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 15:02:12,430 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 15:02:13,898 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 15:02:13,906 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:02:13,913 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 15:02:13,918 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 15:02:13,921 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 15:02:13,924 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 15:02:13,927 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:02:13,930 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 15:02:13,932 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 15:02:13,934 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 15:03:14,690 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 15:03:14,691 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:03:14,693 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 15:03:14,695 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:03:14,697 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 15:03:14,698 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 15:03:14,700 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 15:03:14,701 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:03:14,703 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 15:03:14,705 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 15:03:14,707 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:03:14,708 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 15:03:14,710 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 15:03:14,712 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:03:14,714 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 15:03:14,716 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 15:03:14,717 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 15:03:14,719 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 15:03:14,721 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:03:14,722 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:03:14,724 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 15:03:14,726 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:03:14,728 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 15:03:14,729 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 15:03:14,731 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 15:03:14,733 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:03:14,734 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 15:03:14,736 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 15:03:14,738 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 15:03:14,740 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 15:03:14,742 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 15:03:14,743 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 15:03:14,745 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 15:03:14,747 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 15:03:14,749 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:03:14,750 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 15:03:14,752 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 15:03:14,754 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:03:14,756 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 15:03:14,758 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 15:03:14,760 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 15:03:14,761 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 15:04:16,627 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:04:16,629 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 15:04:16,630 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 15:04:16,632 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 15:04:16,634 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 15:04:16,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 15:04:16,638 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 15:04:16,640 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 15:04:16,641 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 15:04:16,643 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 15:04:16,645 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 15:04:16,646 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 15:04:16,648 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 15:04:16,649 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 15:04:16,651 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:04:16,653 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:04:16,654 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 15:04:16,656 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:04:16,658 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:04:16,659 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:04:16,661 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:04:16,663 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 15:04:16,665 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 15:04:16,666 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 15:04:16,668 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 15:04:16,669 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 15:04:16,671 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 15:04:16,673 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 15:04:16,674 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:04:16,676 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 15:04:16,678 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 15:04:16,679 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 15:04:16,682 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:04:16,683 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 15:04:16,685 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:04:16,687 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 15:04:16,688 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 15:04:16,690 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 15:04:16,692 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:04:16,693 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 15:04:16,695 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 15:04:16,697 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 15:05:17,090 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-05-29 15:05:17,092 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-05-29 15:05:17,094 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:05:17,095 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 15:05:17,097 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-05-29 15:05:17,099 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-05-29 15:05:17,101 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 15:05:17,102 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 15:05:17,104 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:05:17,106 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:05:17,108 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-05-29 15:05:17,109 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 15:05:17,111 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-05-29 15:05:17,113 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-05-29 15:05:17,115 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-05-29 15:05:17,117 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-05-29 15:05:17,118 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:05:17,120 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:05:17,122 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:05:17,123 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-05-29 15:05:17,125 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-05-29 15:05:17,127 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-05-29 15:05:17,129 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-05-29 15:05:17,130 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-05-29 15:05:17,132 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-05-29 15:05:17,134 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-05-29 15:05:17,136 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 15:05:17,137 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:05:17,139 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:05:17,141 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-05-29 15:05:17,143 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 15:05:17,145 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-05-29 15:05:17,147 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:05:17,149 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:05:17,151 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 15:05:17,152 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-05-29 15:05:17,154 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-05-29 15:05:17,156 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:05:17,158 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-05-29 15:05:17,160 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-05-29 15:05:17,162 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-05-29 15:05:17,164 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-05-29 15:06:18,027 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 15:06:18,029 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:06:18,041 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-05-29 15:06:18,043 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-05-29 15:06:18,070 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 15:06:18,075 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 15:06:18,081 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:06:18,082 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-05-29 15:06:18,092 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-05-29 15:06:18,093 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-05-29 15:06:18,095 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 15:06:18,097 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 16:02:00,393 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-05-29 16:02:00,396 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-05-29 16:02:00,398 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-05-29 16:02:00,399 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 16:02:00,401 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-05-29 16:02:00,403 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-05-29 16:02:00,405 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-05-29 16:02:00,407 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-05-29 16:02:00,409 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-05-29 16:02:00,410 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-05-29 16:02:00,412 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-05-29 16:02:00,414 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-05-29 16:02:00,416 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
