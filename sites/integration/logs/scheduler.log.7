2025-06-23 17:01:48,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-23 17:01:48,421 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-23 17:01:48,435 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-23 17:01:48,449 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-23 17:04:34,004 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-23 17:04:34,011 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-23 17:04:34,018 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-23 17:04:34,021 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-23 17:04:34,024 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-23 17:04:34,027 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-23 17:04:34,030 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-23 17:04:34,034 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-23 17:04:34,036 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-23 17:04:34,040 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-23 17:04:34,043 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-23 17:04:34,046 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-23 17:04:34,051 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-23 17:04:34,054 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-23 17:04:34,059 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-23 17:04:34,062 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-23 17:04:34,065 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-23 17:04:34,068 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-23 17:04:34,071 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-23 17:04:34,074 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-23 17:04:34,077 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-23 17:04:34,080 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-23 17:04:34,083 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-23 17:04:34,086 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-23 17:04:34,090 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-23 17:04:34,093 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-23 17:04:34,096 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-23 17:04:34,099 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-23 17:04:34,102 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-23 17:04:34,105 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-23 17:04:34,108 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-23 17:04:34,111 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-23 17:04:34,115 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-23 17:04:34,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-23 17:04:34,125 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-23 17:04:34,129 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-23 17:04:34,134 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-23 17:04:34,139 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-23 17:04:34,142 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-23 17:04:34,145 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-24 09:19:40,989 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-24 09:19:41,002 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-24 09:19:41,008 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-24 09:19:41,016 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-24 09:19:41,020 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-24 09:19:41,022 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-24 09:19:41,025 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-24 09:19:41,032 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-24 09:19:41,034 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-24 09:19:41,058 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-24 09:19:41,082 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-24 09:19:41,084 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-24 11:01:44,068 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-24 11:01:44,077 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-24 11:01:44,084 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-24 11:01:44,092 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-24 11:01:44,101 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-24 11:01:44,109 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-24 11:01:44,126 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-24 11:01:44,133 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-24 11:01:44,140 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-24 11:01:44,148 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-24 11:01:44,158 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-24 11:01:44,169 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-24 11:01:44,177 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-24 11:01:44,185 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-24 11:01:44,193 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-24 11:01:44,201 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-24 11:01:44,209 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-24 11:01:44,216 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-24 11:01:44,225 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-24 11:01:44,233 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-24 11:01:44,241 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-24 11:01:44,264 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-24 11:01:44,272 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-24 11:01:44,279 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-24 11:01:44,288 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-24 11:01:44,295 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-24 11:01:44,304 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-24 11:01:44,312 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-24 11:01:44,323 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-24 11:01:44,332 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-24 11:01:44,340 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-06-24 11:01:44,356 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-24 11:01:44,364 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-24 11:01:44,372 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-24 11:01:44,381 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-24 11:01:44,389 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-24 11:01:44,396 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-24 11:01:44,404 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-24 11:01:44,412 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-06-25 00:01:21,036 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 00:01:21,059 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-25 00:01:21,132 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 00:01:21,147 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-25 00:01:21,150 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-25 00:01:21,169 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-25 00:01:21,180 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-25 00:01:21,214 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-25 00:01:21,249 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-25 00:01:21,315 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-25 00:01:21,319 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-25 00:01:21,341 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-25 00:43:07,576 ERROR scheduler Exception in Enqueue Events for Site integration
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socket.py", line 863, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.11/socket.py", line 848, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-06-25 14:37:09,429 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-25 14:37:09,918 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-25 14:37:11,060 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-25 14:37:15,612 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-25 14:37:18,647 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-25 14:37:18,891 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-25 14:37:18,906 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-25 14:37:18,908 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-25 14:37:18,910 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:37:18,912 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-25 14:37:18,915 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:37:18,917 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-25 14:37:18,920 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:37:18,922 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-25 14:37:18,929 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-25 14:37:18,933 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-25 14:37:18,936 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-25 14:37:18,938 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-25 14:37:18,941 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-25 14:37:18,944 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-25 14:37:18,947 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:37:18,953 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-25 14:37:18,956 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-25 14:37:18,960 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-25 14:37:18,963 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-25 14:37:18,966 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-25 14:37:18,979 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-25 14:37:18,984 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-25 14:37:18,987 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-25 14:37:18,990 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-25 14:37:18,993 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-25 14:37:18,998 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-25 14:37:19,001 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-25 14:37:19,005 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-25 14:37:19,008 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-25 14:37:19,012 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-25 14:37:19,015 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-25 14:37:19,018 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-25 14:39:00,400 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-25 14:39:02,847 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-25 14:39:02,907 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-25 14:39:02,909 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-25 14:39:02,917 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-25 14:39:02,919 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-25 14:39:02,922 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-25 14:39:02,924 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-25 14:39:02,926 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:39:02,928 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-25 14:39:02,931 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-25 14:39:02,933 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-25 14:39:02,936 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-25 14:39:02,939 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-25 14:39:02,941 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-25 14:39:02,944 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-25 14:39:02,947 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-25 14:39:02,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-25 14:39:02,951 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-25 14:39:02,955 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-25 14:39:02,958 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:39:02,960 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-25 14:39:02,963 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-25 14:39:02,965 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-25 14:39:02,967 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-25 14:39:02,970 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-25 14:39:02,972 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-25 14:39:02,975 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-25 14:39:02,977 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-25 14:39:02,980 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-25 14:39:02,982 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-25 14:39:02,985 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-25 14:39:02,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:39:02,990 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-06-25 14:39:02,992 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-25 14:39:02,994 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-25 14:39:02,996 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:39:02,999 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-06-25 14:39:03,001 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-25 14:39:03,003 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-25 14:40:53,611 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-25 14:40:53,621 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-25 14:40:53,623 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-25 14:40:53,625 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-25 14:40:53,627 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-25 14:40:53,629 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-06-25 14:40:53,634 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-25 14:40:53,641 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-25 14:40:53,643 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:40:53,647 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-25 14:40:53,649 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-25 14:40:53,651 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:40:53,653 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-25 14:40:53,660 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-25 14:40:53,665 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-25 14:40:53,667 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-25 14:40:53,671 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-25 14:40:53,676 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-25 14:40:53,684 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-25 14:40:53,686 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-25 14:40:53,691 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-25 14:40:53,696 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-25 14:40:53,698 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-25 14:40:53,700 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-06-25 14:40:53,702 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-26 10:30:03,354 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-26 10:30:03,377 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-26 10:30:03,405 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-26 10:30:03,410 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-26 10:30:03,420 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-26 11:36:05,719 ERROR scheduler Exception in Enqueue Events for Site integration
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socket.py", line 863, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.11/socket.py", line 848, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    def get_connection(self):
            ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    """Return MariaDB connection object."""
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    def create_connection(self):
            ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-06-26 18:34:26,428 ERROR scheduler Exception in Enqueue Events for Site integration
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socket.py", line 863, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.11/socket.py", line 848, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-06-27 12:05:19,915 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-27 12:05:19,918 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-27 12:05:19,925 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-27 12:05:19,937 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-27 12:05:19,942 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-27 12:05:19,946 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-27 12:05:19,958 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-27 12:05:19,962 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-27 12:05:19,971 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-27 12:05:19,980 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-27 12:05:19,989 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-27 12:05:19,999 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-27 12:18:49,081 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-27 12:18:49,115 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-27 12:18:49,121 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-06-27 12:18:49,126 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-27 12:18:49,131 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-27 12:18:49,135 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-06-27 12:18:49,138 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-27 12:18:49,140 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-27 12:18:49,143 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-27 12:18:49,165 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-27 12:18:49,171 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-27 12:18:49,177 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-27 12:18:49,182 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-27 12:18:49,187 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-27 12:18:49,193 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-27 12:18:49,200 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-27 12:18:49,206 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-27 12:18:49,210 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-27 12:18:49,212 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-27 12:18:49,214 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-27 12:18:49,216 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-27 12:18:49,218 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-27 12:18:49,219 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-27 12:18:49,221 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-27 12:18:49,222 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-27 12:18:49,223 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-27 12:18:49,225 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-27 12:18:49,226 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-27 12:18:49,227 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-27 12:18:49,228 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-27 12:18:49,230 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-27 12:18:49,231 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-27 12:18:49,232 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-27 12:18:49,233 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-27 12:18:49,234 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-27 12:18:49,235 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-27 12:18:49,236 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-27 12:18:49,237 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-27 12:18:49,238 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-27 12:18:49,239 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-27 12:18:49,240 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-27 12:18:49,241 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-27 18:23:43,063 ERROR scheduler Exception in Enqueue Events for Site integration
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socket.py", line 863, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.11/socket.py", line 848, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-06-30 10:04:27,676 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-30 10:04:27,679 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 10:04:27,682 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-30 10:04:27,684 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-06-30 10:04:27,686 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 10:04:27,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 10:04:27,690 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-30 10:04:27,692 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 10:04:27,694 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-30 10:04:27,696 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-30 10:04:27,698 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-30 10:04:27,701 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 10:04:27,702 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-30 10:04:27,704 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 10:04:27,706 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-30 10:04:27,707 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-30 10:04:27,709 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-30 10:04:27,711 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-30 10:04:27,714 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-30 10:04:27,717 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-30 10:04:27,720 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-30 10:04:27,722 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-30 10:04:27,724 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-30 10:04:27,726 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 10:04:27,728 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 10:04:27,731 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-30 10:04:27,734 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-30 10:04:27,736 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-30 10:04:27,738 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-06-30 10:04:27,740 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 10:04:27,742 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-30 10:04:27,744 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-30 10:04:27,746 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 10:04:27,749 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-30 10:04:27,752 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 10:04:27,755 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-30 10:04:27,757 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 10:04:27,760 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-30 10:04:27,763 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-30 10:04:27,768 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-30 10:04:27,771 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-30 10:04:27,773 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-30 10:05:30,944 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 10:05:30,950 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 10:05:30,952 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 10:05:30,953 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 10:05:30,956 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 10:05:30,960 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 10:05:30,971 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 10:05:30,979 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 10:05:30,986 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 10:05:30,992 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 10:05:30,996 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 10:05:30,999 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:50:01,662 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 12:50:01,730 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 12:50:01,738 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 12:50:01,745 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 12:50:01,763 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 12:50:01,769 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 12:50:01,775 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:50:01,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:50:01,784 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 12:50:01,801 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 12:50:02,040 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 12:50:02,052 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 12:54:13,236 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:54:13,434 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-30 12:54:13,436 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 12:54:13,437 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:54:13,439 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-30 12:54:13,440 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 12:54:13,441 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 12:54:13,443 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 12:54:13,575 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 12:54:13,579 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-30 12:54:13,582 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-30 12:54:13,589 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-30 12:54:13,591 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-30 12:54:13,595 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 12:54:13,598 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-30 12:54:13,600 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-30 12:54:13,604 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-30 12:54:13,605 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-30 12:54:13,607 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-30 12:54:13,608 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-30 12:54:13,609 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-30 12:54:13,611 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-30 12:54:13,612 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-30 12:54:13,649 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-30 12:54:13,654 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 12:54:13,659 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-30 12:54:13,665 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-30 12:54:13,669 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 12:54:13,672 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-30 12:54:13,674 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-06-30 12:54:13,677 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-30 12:54:13,680 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 12:54:13,682 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-30 12:54:13,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-06-30 12:54:13,687 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-30 12:55:17,291 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 12:55:17,376 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-30 12:55:17,382 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-30 12:55:17,388 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-30 12:55:17,393 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-30 12:55:17,397 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 12:55:17,400 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 12:55:17,403 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:55:17,405 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-30 12:55:17,408 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-30 12:55:17,410 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 12:55:17,411 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 12:55:17,425 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 12:55:17,426 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-06-30 12:55:17,427 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-30 12:55:17,429 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-30 12:55:17,430 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-30 12:55:17,431 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-30 12:55:17,559 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-30 12:55:17,565 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-06-30 12:55:17,571 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 12:55:17,576 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-30 12:55:17,581 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-06-30 12:55:17,585 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-06-30 12:55:17,588 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-06-30 12:55:17,590 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-06-30 12:55:17,592 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-30 12:55:17,595 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 12:55:17,598 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 12:55:17,601 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 12:55:17,603 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-30 12:55:17,604 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-06-30 12:55:17,605 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-30 12:55:17,606 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 12:55:17,608 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-30 12:55:17,609 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-06-30 16:45:36,412 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-06-30 16:45:36,443 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-06-30 16:45:36,445 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-06-30 16:45:36,447 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-06-30 16:45:36,449 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-06-30 16:45:36,451 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-06-30 16:45:36,457 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-06-30 16:45:36,458 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-06-30 16:45:36,465 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-06-30 16:45:36,467 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-06-30 16:45:36,469 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-06-30 16:45:36,479 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-06-30 16:45:36,483 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-06-30 16:45:36,485 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-06-30 16:45:36,487 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-06-30 16:45:36,489 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-06-30 16:45:36,491 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-06-30 16:45:36,493 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-06-30 16:45:36,551 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-06-30 16:45:36,553 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-06-30 16:45:36,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-06-30 16:45:36,557 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 16:45:36,559 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-06-30 16:45:36,563 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-06-30 16:45:36,566 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-06-30 16:45:36,568 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-06-30 16:45:36,570 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-06-30 16:45:36,574 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-06-30 16:45:36,579 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-06-30 16:45:36,583 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-06-30 16:45:36,585 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-07-01 10:50:00,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-01 10:50:00,776 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-07-01 10:50:00,778 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-07-01 10:50:00,780 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-07-01 10:50:00,782 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-07-01 10:50:00,784 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-07-01 10:50:00,786 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-07-01 10:50:00,788 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-07-01 10:50:00,790 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-07-01 10:50:00,792 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-07-01 10:50:00,794 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-07-01 10:50:00,796 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-01 10:50:00,798 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-01 10:50:00,800 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-07-01 10:50:00,802 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-01 10:50:00,804 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-01 10:50:00,805 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-07-01 10:50:00,807 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-07-01 10:50:00,809 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-01 10:50:00,810 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-07-01 10:50:00,812 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-01 10:50:00,814 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-01 10:50:00,816 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-01 10:50:00,818 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-01 10:50:00,820 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-07-01 10:50:00,822 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-07-01 10:50:00,823 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-07-01 10:50:00,826 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-07-01 10:50:00,829 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-07-01 10:50:00,831 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-07-01 10:50:00,834 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-07-01 10:50:00,836 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-07-01 10:50:00,839 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-07-01 10:50:00,842 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-07-01 10:50:00,844 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-07-01 10:50:00,846 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-07-01 10:50:00,848 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-07-01 10:50:00,849 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-01 10:50:00,851 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-07-01 10:50:00,852 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-01 10:50:00,854 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-07-01 10:50:00,856 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-07-01 10:51:01,307 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-01 10:51:01,318 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-01 10:51:01,327 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-01 10:51:01,332 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-01 10:51:01,337 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-01 10:51:01,344 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-01 10:51:01,346 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-01 10:51:01,355 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-01 10:51:01,357 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-01 10:51:01,366 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-01 10:51:01,369 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-01 10:51:01,375 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-04 09:18:11,448 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-04 09:18:11,464 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-04 09:18:11,479 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-04 09:18:11,486 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-04 09:18:11,493 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-04 09:18:11,495 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-04 09:18:11,496 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-04 09:18:11,501 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-04 09:18:11,510 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-04 09:18:11,514 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-04 09:18:11,516 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-04 09:18:11,522 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-04 12:02:04,986 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-07-04 12:02:05,009 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-07-04 12:02:05,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-07-04 12:02:05,038 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-04 12:02:05,040 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-04 12:02:05,041 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-04 12:02:05,045 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-04 12:02:05,048 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-07-04 12:02:05,054 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-07-04 12:02:05,058 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-07-04 12:02:05,060 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-07-04 12:02:05,061 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-07-04 12:03:06,421 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-07-04 12:03:06,471 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-07-04 12:03:06,473 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-07-04 12:03:06,487 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-07-04 12:03:06,496 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
