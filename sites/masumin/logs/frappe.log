2025-02-11 16:52:15,282 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {}
2025-02-11 16:52:16,124 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {}
2025-02-11 16:56:14,240 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doctype': 'Stock Ledger Entry', 'fields': '["`tabStock Ledger Entry`.`name`","`tabStock Ledger Entry`.`owner`","`tabStock Ledger Entry`.`creation`","`tabStock Ledger Entry`.`modified`","`tabStock Ledger Entry`.`modified_by`","`tabStock Ledger Entry`.`_user_tags`","`tabStock Ledger Entry`.`_comments`","`tabStock Ledger Entry`.`_assign`","`tabStock Ledger Entry`.`_liked_by`","`tabStock Ledger Entry`.`docstatus`","`tabStock Ledger Entry`.`idx`","`tabStock Ledger Entry`.`item_code`","`tabStock Ledger Entry`.`warehouse`","`tabStock Ledger Entry`.`posting_date`","`tabStock Ledger Entry`.`voucher_no`","`tabStock Ledger Entry`.`actual_qty`","`tabStock Ledger Entry`.`incoming_rate`"]', 'filters': '[]', 'order_by': '`tabStock Ledger Entry`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1', 'cmd': 'frappe.desk.reportview.get'}
2025-02-11 17:06:56,579 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 17:07:44,156 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 17:19:48,535 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 17:21:00,213 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 17:59:25,061 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:00:21,242 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:00:50,561 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:01:37,293 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:02:34,216 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:02:40,809 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:06:18,380 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-11 18:07:08,864 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:21:48,133 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:22:42,352 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:24:24,121 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:25:23,145 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:25:41,078 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-02-12 11:25:43,051 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'item_code': '4309', 'cmd': 'csf_tz.custom_api.get_item_info'}
2025-04-10 09:41:08,554 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'report_name': 'Employee Salary Register with Monthly Comparisons', 'filters': '{"from_date":"2025-03-10","to_date":"2025-04-10","currency":"TZS","company":"Masumin Printways and Stationers Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-04-10 09:41:08,758 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'report_name': 'Employee Salary Register with Monthly Comparisons', 'filters': '{"from_date":"2025-03-10","to_date":"2025-04-10","currency":"TZS","company":"Masumin Printways and Stationers Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-06-05 13:14:39,800 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'docs': '{"name":"Payment Reconciliation","minimum_invoice_amount":0,"minimum_payment_amount":0,"maximum_invoice_amount":0,"maximum_payment_amount":0,"invoice_limit":50,"payment_limit":50,"doctype":"Payment Reconciliation","allocation":[],"invoices":[],"payments":[],"company":"Masumin Printways and Stationers Limited","party_type":"Supplier","party":"F014","receivable_payable_account":"Creditors - MPASL","__unsaved":1,"docstatus":0,"idx":0}', 'method': 'get_unreconciled_entries', 'cmd': 'run_doc_method'}
2025-06-09 15:24:31,519 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01564","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":0,"workflow_state":null,"title":"AL FAHIDI STATIONARY","naming_series":"MAT-DN-.YYYY.-","customer":"ALF001","tax_id":"*********","customer_name":"AL FAHIDI STATIONARY","posting_date":"2025-06-09","posting_time":"15:24:26.211860","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","amended_from":null,"is_return":0,"issue_credit_note":0,"return_against":null,"po_no":"321","custom_branch":null,"cost_center":null,"project":null,"vehicle":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"pick_list":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":1,"total_net_weight":0,"base_total":1690,"base_net_total":1432.2,"total":1690,"net_total":1432.2,"tax_category":"","taxes_and_charges":"Sales (Cash) - MPASL","shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":257.8,"total_taxes_and_charges":257.8,"base_grand_total":1690,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"","grand_total":1690,"rounding_adjustment":0,"rounded_total":0,"in_words":"","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Cash Sales)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>9818</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 1,432.20\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 257.80\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","customer_address":null,"address_display":null,"contact_person":null,"contact_display":null,"contact_mobile":null,"contact_email":null,"shipping_address_name":null,"shipping_address":null,"dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_contact_person":null,"company_address_display":null,"tc_name":null,"terms":null,"per_billed":0,"status":"Draft","per_installed":0,"installation_status":"Not Installed","per_returned":0,"transporter":null,"driver":null,"lr_no":null,"vehicle_no":null,"transporter_name":null,"driver_name":null,"lr_date":"2025-06-09","po_date":null,"sales_partner":null,"amount_eligible_for_commission":1432.2,"commission_rate":0,"total_commission":0,"auto_repeat":null,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"select_print_heading":null,"language":"en-GB","is_internal_customer":0,"represents_company":null,"inter_company_reference":null,"customer_group":"Commercial","territory":"Tanzania","source":null,"campaign":null,"excise_page":null,"instructions":null,"form_sales_invoice":null,"scan_delivery_note":null,"scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","items":[{"name":"5g51j8te49","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":"6201100010669","has_item_scanned":0,"item_code":"9818","item_name":"Counter Book - 2 Quire - Fivestar ","price_list":null,"customer_item_code":null,"description":"Counter Book - 2 Quire - Fivestar , ,","brand":null,"item_group":"Counter Book","image":"/files/9818_1744211877.webp","qty":1,"custom_bin_location":"1D","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":1,"returned_qty":0,"price_list_rate":1690,"base_price_list_rate":1690,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":1690,"amount":1690,"base_rate":1690,"base_amount":1690,"pricing_rules":"","stock_uom_rate":1690,"is_free_item":0,"grant_commission":1,"net_rate":1432.2,"net_amount":1432.2,"item_tax_template":null,"base_net_rate":1432.2,"base_net_amount":1432.2,"billed_amt":0,"incoming_rate":1024.0113,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Main Master - MPASL","target_warehouse":null,"quality_inspection":null,"against_sales_order":null,"so_detail":null,"against_sales_invoice":null,"si_detail":null,"dn_detail":null,"pick_list_item":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":0,"batch_no":null,"serial_no":null,"actual_batch_qty":0,"actual_qty":3773,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":12629,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"material_request":null,"purchase_order":null,"purchase_order_item":null,"material_request_item":null,"cost_center":"Main - MPASL","vehicle":null,"project":null,"page_break":0,"parent":"MAT-DN-2025-01564","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item","__unsaved":1}],"pricing_rules":[],"taxes":[{"name":"gkniorjp7m","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"On Net Total","row_id":null,"account_head":"VAT Output (Cash Sales) - MPASL","description":"VAT Output (Cash Sales)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","vehicle":null,"rate":18,"account_currency":"TZS","tax_amount":257.8,"total":1690,"tax_amount_after_discount_amount":257.8,"base_tax_amount":257.8,"base_total":1690,"base_tax_amount_after_discount_amount":257.8,"item_wise_tax_detail":"{\\"9818\\":[18,257.8]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01564","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges","__unsaved":1}],"workflow_transition_logs":[],"sales_team":[],"packed_items":[],"__onload":{"make_payment_via_journal_entry":0,"has_unpacked_items":true},"__last_sync_on":"2025-06-09T12:24:26.447Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-09 15:24:56,756 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01564","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":0,"workflow_state":null,"title":"AL FAHIDI STATIONARY","naming_series":"MAT-DN-.YYYY.-","customer":"ALF001","tax_id":"*********","customer_name":"AL FAHIDI STATIONARY","posting_date":"2025-06-09","posting_time":"15:24:26.211860","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","amended_from":null,"is_return":0,"issue_credit_note":0,"return_against":null,"po_no":"321","custom_branch":null,"cost_center":null,"project":null,"vehicle":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"pick_list":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":1,"total_net_weight":0,"base_total":1690,"base_net_total":1432.2,"total":1690,"net_total":1432.2,"tax_category":"","taxes_and_charges":"Sales (Cash) - MPASL","shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":257.8,"total_taxes_and_charges":257.8,"base_grand_total":1690,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"","grand_total":1690,"rounding_adjustment":0,"rounded_total":0,"in_words":"","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Cash Sales)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>9818</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 1,432.20\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 257.80\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","customer_address":null,"address_display":null,"contact_person":null,"contact_display":null,"contact_mobile":null,"contact_email":null,"shipping_address_name":null,"shipping_address":null,"dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_contact_person":null,"company_address_display":null,"tc_name":null,"terms":null,"per_billed":0,"status":"Draft","per_installed":0,"installation_status":"Not Installed","per_returned":0,"transporter":null,"driver":null,"lr_no":null,"vehicle_no":null,"transporter_name":null,"driver_name":null,"lr_date":"2025-06-09","po_date":null,"sales_partner":null,"amount_eligible_for_commission":1432.2,"commission_rate":0,"total_commission":0,"auto_repeat":null,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"select_print_heading":null,"language":"en-GB","is_internal_customer":0,"represents_company":null,"inter_company_reference":null,"customer_group":"Commercial","territory":"Tanzania","source":null,"campaign":null,"excise_page":null,"instructions":null,"form_sales_invoice":null,"scan_delivery_note":null,"scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","items":[{"name":"5g51j8te49","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":"6201100010669","has_item_scanned":0,"item_code":"9818","item_name":"Counter Book - 2 Quire - Fivestar ","price_list":null,"customer_item_code":null,"description":"Counter Book - 2 Quire - Fivestar , ,","brand":null,"item_group":"Counter Book","image":"/files/9818_1744211877.webp","qty":1,"custom_bin_location":"1D","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":1,"returned_qty":0,"price_list_rate":1690,"base_price_list_rate":1690,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":1690,"amount":1690,"base_rate":1690,"base_amount":1690,"pricing_rules":"","stock_uom_rate":1690,"is_free_item":0,"grant_commission":1,"net_rate":1432.2,"net_amount":1432.2,"item_tax_template":null,"base_net_rate":1432.2,"base_net_amount":1432.2,"billed_amt":0,"incoming_rate":1024.0113,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Main Master - MPASL","target_warehouse":null,"quality_inspection":null,"against_sales_order":null,"so_detail":null,"against_sales_invoice":null,"si_detail":null,"dn_detail":null,"pick_list_item":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":0,"batch_no":null,"serial_no":null,"actual_batch_qty":0,"actual_qty":3773,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":12629,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"material_request":null,"purchase_order":null,"purchase_order_item":null,"material_request_item":null,"cost_center":"Main - MPASL","vehicle":null,"project":null,"page_break":0,"parent":"MAT-DN-2025-01564","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item","__unsaved":1}],"pricing_rules":[],"taxes":[{"name":"gkniorjp7m","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"On Net Total","row_id":null,"account_head":"VAT Output (Cash Sales) - MPASL","description":"VAT Output (Cash Sales)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","vehicle":null,"rate":18,"account_currency":"TZS","tax_amount":257.8,"total":1690,"tax_amount_after_discount_amount":257.8,"base_tax_amount":257.8,"base_total":1690,"base_tax_amount_after_discount_amount":257.8,"item_wise_tax_detail":"{\\"9818\\":[18,257.8]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01564","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges","__unsaved":1}],"workflow_transition_logs":[],"sales_team":[],"packed_items":[],"__onload":{"make_payment_via_journal_entry":0,"has_unpacked_items":true},"__last_sync_on":"2025-06-09T12:24:26.447Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-09 15:27:03,853 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01564","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":0,"title":"AL FAHIDI STATIONARY","naming_series":"MAT-DN-.YYYY.-","customer":"ALF001","tax_id":"*********","customer_name":"AL FAHIDI STATIONARY","posting_date":"2025-06-09","posting_time":"15:24:26.21186","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","is_return":0,"issue_credit_note":0,"po_no":"321","currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"total_qty":1,"total_net_weight":0,"base_total":1690,"base_net_total":1432.2,"total":1690,"net_total":1432.2,"tax_category":"","taxes_and_charges":"Sales (Cash) - MPASL","base_total_taxes_and_charges":257.8,"total_taxes_and_charges":257.8,"base_grand_total":1690,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"","grand_total":1690,"rounding_adjustment":0,"rounded_total":0,"in_words":"","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Cash Sales)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>9818</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 1,432.20\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 257.80\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","per_billed":0,"status":"Draft","per_installed":0,"installation_status":"Not Installed","per_returned":0,"lr_date":"2025-06-09","amount_eligible_for_commission":1432.2,"commission_rate":0,"total_commission":0,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"language":"en-GB","is_internal_customer":0,"customer_group":"Commercial","territory":"Tanzania","scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","items":[{"name":"5g51j8te49","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":"6201100010669","has_item_scanned":0,"item_code":"9818","item_name":"Counter Book - 2 Quire - Fivestar ","description":"Counter Book - 2 Quire - Fivestar , ,","item_group":"Counter Book","image":"/files/9818_1744211877.webp","qty":1,"custom_bin_location":"1D","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":1,"returned_qty":0,"price_list_rate":1690,"base_price_list_rate":1690,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":1690,"amount":1690,"base_rate":1690,"base_amount":1690,"pricing_rules":"","stock_uom_rate":1690,"is_free_item":0,"grant_commission":1,"net_rate":1432.2,"net_amount":1432.2,"base_net_rate":1432.2,"base_net_amount":1432.2,"billed_amt":0,"incoming_rate":1024.0113,"weight_per_unit":0,"total_weight":0,"warehouse":"Main Master - MPASL","use_serial_batch_fields":0,"actual_batch_qty":0,"actual_qty":3773,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":12629,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"cost_center":"Main - MPASL","page_break":0,"parent":"MAT-DN-2025-01564","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item"}],"pricing_rules":[],"taxes":[{"name":"gkniorjp7m","owner":"Administrator","creation":"2025-06-09 15:24:26.113803","modified":"2025-06-09 15:24:26.113803","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"On Net Total","account_head":"VAT Output (Cash Sales) - MPASL","description":"VAT Output (Cash Sales)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","rate":18,"account_currency":"TZS","tax_amount":257.8,"total":1690,"tax_amount_after_discount_amount":257.8,"base_tax_amount":257.8,"base_total":1690,"base_tax_amount_after_discount_amount":257.8,"item_wise_tax_detail":"{\\"9818\\":[18,257.8]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01564","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges"}],"workflow_transition_logs":[],"sales_team":[],"packed_items":[],"__onload":{"make_payment_via_journal_entry":0,"has_unpacked_items":true},"__last_sync_on":"2025-06-09T12:26:56.631Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-09 15:28:32,789 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01565","owner":"Administrator","creation":"2025-06-09 15:28:25.680787","modified":"2025-06-09 15:28:25.680787","modified_by":"Administrator","docstatus":0,"idx":0,"workflow_state":null,"title":"AL FAHIDI STATIONARY","naming_series":"MAT-DN-.YYYY.-","customer":"ALF001","tax_id":"*********","customer_name":"AL FAHIDI STATIONARY","posting_date":"2025-06-09","posting_time":"15:28:25.732828","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","amended_from":null,"is_return":0,"issue_credit_note":0,"return_against":null,"po_no":"341e","custom_branch":null,"cost_center":null,"project":null,"vehicle":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"pick_list":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":1,"total_net_weight":0,"base_total":11400,"base_net_total":9661.02,"total":11400,"net_total":9661.02,"tax_category":"","taxes_and_charges":"Sales (Cash) - MPASL","shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":1738.98,"total_taxes_and_charges":1738.98,"base_grand_total":11400,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"","grand_total":11400,"rounding_adjustment":0,"rounded_total":0,"in_words":"","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Cash Sales)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>1342</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 9,661.02\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 1,738.98\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","customer_address":null,"address_display":null,"contact_person":null,"contact_display":null,"contact_mobile":null,"contact_email":null,"shipping_address_name":null,"shipping_address":null,"dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_contact_person":null,"company_address_display":null,"tc_name":null,"terms":null,"per_billed":0,"status":"Draft","per_installed":0,"installation_status":"Not Installed","per_returned":0,"transporter":null,"driver":null,"lr_no":null,"vehicle_no":null,"transporter_name":null,"driver_name":null,"lr_date":"2025-06-09","po_date":null,"sales_partner":null,"amount_eligible_for_commission":9661.02,"commission_rate":0,"total_commission":0,"auto_repeat":null,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"select_print_heading":null,"language":"en-GB","is_internal_customer":0,"represents_company":null,"inter_company_reference":null,"customer_group":"Commercial","territory":"Tanzania","source":null,"campaign":null,"excise_page":null,"instructions":null,"form_sales_invoice":null,"scan_delivery_note":null,"scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","items":[{"name":"hgmjnfrmpt","owner":"Administrator","creation":"2025-06-09 15:28:25.680787","modified":"2025-06-09 15:28:25.680787","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":"6003977000053","has_item_scanned":0,"item_code":"1342","item_name":"Photocopy - A4 - White - Mondi - No.1","price_list":null,"customer_item_code":null,"description":"Photocopy - A4 - White - Mondi - No.1, ,","brand":null,"item_group":"Photocopy Papar","image":"/files/1342_1744211832.jpeg","qty":1,"custom_bin_location":"3D","stock_uom":"Ream","uom":"Ream","conversion_factor":1,"stock_qty":1,"returned_qty":0,"price_list_rate":11400,"base_price_list_rate":11400,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":11400,"amount":11400,"base_rate":11400,"base_amount":11400,"pricing_rules":"","stock_uom_rate":11400,"is_free_item":0,"grant_commission":1,"net_rate":9661.02,"net_amount":9661.02,"item_tax_template":null,"base_net_rate":9661.02,"base_net_amount":9661.02,"billed_amt":0,"incoming_rate":7966.101695,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Main Master - MPASL","target_warehouse":null,"quality_inspection":null,"against_sales_order":null,"so_detail":null,"against_sales_invoice":null,"si_detail":null,"dn_detail":null,"pick_list_item":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":0,"batch_no":null,"serial_no":null,"actual_batch_qty":0,"actual_qty":50,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":17150,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"material_request":null,"purchase_order":null,"purchase_order_item":null,"material_request_item":null,"cost_center":"Main - MPASL","vehicle":null,"project":null,"page_break":0,"parent":"MAT-DN-2025-01565","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item","__unsaved":1}],"pricing_rules":[],"taxes":[{"name":"6mor9hhjm6","owner":"Administrator","creation":"2025-06-09 15:28:25.680787","modified":"2025-06-09 15:28:25.680787","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"On Net Total","row_id":null,"account_head":"VAT Output (Cash Sales) - MPASL","description":"VAT Output (Cash Sales)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","vehicle":null,"rate":18,"account_currency":"TZS","tax_amount":1738.98,"total":11400,"tax_amount_after_discount_amount":1738.98,"base_tax_amount":1738.98,"base_total":11400,"base_tax_amount_after_discount_amount":1738.98,"item_wise_tax_detail":"{\\"1342\\":[18,1738.98]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01565","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges","__unsaved":1}],"workflow_transition_logs":[],"sales_team":[],"packed_items":[],"__onload":{"make_payment_via_journal_entry":0,"has_unpacked_items":true},"__last_sync_on":"2025-06-09T12:28:25.943Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-09 15:36:09,709 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01567","owner":"Administrator","creation":"2025-06-09 15:35:29.945581","modified":"2025-06-09 15:35:29.945581","modified_by":"Administrator","docstatus":0,"idx":0,"title":"AL FAHIDI STATIONARY","naming_series":"MAT-DN-.YYYY.-","customer":"ALF001","tax_id":"*********","customer_name":"AL FAHIDI STATIONARY","posting_date":"2025-06-09","posting_time":"15:35:30.012816","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","is_return":0,"issue_credit_note":0,"po_no":"3t54","currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"total_qty":1,"total_net_weight":0,"base_total":1690,"base_net_total":1432.2,"total":1690,"net_total":1432.2,"tax_category":"","taxes_and_charges":"Sales (Cash) - MPASL","base_total_taxes_and_charges":257.8,"total_taxes_and_charges":257.8,"base_grand_total":1690,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"","grand_total":1690,"rounding_adjustment":0,"rounded_total":0,"in_words":"","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Cash Sales)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>9818</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 1,432.20\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 257.80\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","per_billed":0,"status":"Draft","per_installed":0,"installation_status":"Not Installed","per_returned":0,"lr_date":"2025-06-09","amount_eligible_for_commission":1432.2,"commission_rate":0,"total_commission":0,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"language":"en-GB","is_internal_customer":0,"customer_group":"Commercial","territory":"Tanzania","scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","items":[{"name":"mgqrok3ogr","owner":"Administrator","creation":"2025-06-09 15:35:29.945581","modified":"2025-06-09 15:35:29.945581","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":"6201100010669","has_item_scanned":0,"item_code":"9818","item_name":"Counter Book - 2 Quire - Fivestar ","description":"Counter Book - 2 Quire - Fivestar , ,","item_group":"Counter Book","image":"/files/9818_1744211877.webp","qty":1,"custom_bin_location":"1D","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":1,"returned_qty":0,"price_list_rate":1690,"base_price_list_rate":1690,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":1690,"amount":1690,"base_rate":1690,"base_amount":1690,"pricing_rules":"","stock_uom_rate":1690,"is_free_item":0,"grant_commission":1,"net_rate":1432.2,"net_amount":1432.2,"base_net_rate":1432.2,"base_net_amount":1432.2,"billed_amt":0,"incoming_rate":1024.0113,"weight_per_unit":0,"total_weight":0,"warehouse":"Main Master - MPASL","use_serial_batch_fields":0,"actual_batch_qty":0,"actual_qty":3772,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":12628,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"cost_center":"Main - MPASL","page_break":0,"parent":"MAT-DN-2025-01567","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item"}],"pricing_rules":[],"taxes":[{"name":"v7p9jasvuu","owner":"Administrator","creation":"2025-06-09 15:35:29.945581","modified":"2025-06-09 15:35:29.945581","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"On Net Total","account_head":"VAT Output (Cash Sales) - MPASL","description":"VAT Output (Cash Sales)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","rate":18,"account_currency":"TZS","tax_amount":257.8,"total":1690,"tax_amount_after_discount_amount":257.8,"base_tax_amount":257.8,"base_total":1690,"base_tax_amount_after_discount_amount":257.8,"item_wise_tax_detail":"{\\"9818\\":[18,257.8]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01567","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges"}],"workflow_transition_logs":[],"sales_team":[],"packed_items":[],"__onload":{"make_payment_via_journal_entry":0,"has_unpacked_items":true},"__last_sync_on":"2025-06-09T12:35:41.806Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-13 12:37:45,687 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"MAT-DN-2025-01557","owner":"<EMAIL>","creation":"2025-02-11 08:25:33.772930","modified":"2025-02-11 08:26:39.446307","modified_by":"<EMAIL>","docstatus":1,"idx":0,"workflow_state":"Submitted","title":"OLDSTONE (T) LTD T/A RAMADA RESORT DAR ES SALAAM","naming_series":"MAT-DN-.YYYY.-","customer":"RAM001","tax_id":"*********","customer_name":"OLDSTONE (T) LTD T/A RAMADA RESORT DAR ES SALAAM","posting_date":"2025-02-11","posting_time":"8:25:45.816108","set_posting_time":0,"company":"Masumin Printways and Stationers Limited","is_return":0,"issue_credit_note":0,"po_no":"12i-42063","currency":"TZS","conversion_rate":1,"selling_price_list":"Price List 1","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"set_warehouse":"Main Master - MPASL","total_qty":36,"total_net_weight":0,"base_total":103728.81,"base_net_total":87905.76,"total":103728.81,"net_total":87905.76,"tax_category":"","taxes_and_charges":"Sales (Credit) - MPASL","base_total_taxes_and_charges":15823.04,"total_taxes_and_charges":15823.04,"base_grand_total":103728.81,"base_rounding_adjustment":0,"base_rounded_total":0,"base_in_words":"TZS One Hundred And Three Thousand, Seven Hundred And Twenty Eight and Eighty One Cent only.","grand_total":103728.81,"rounding_adjustment":0,"rounded_total":0,"in_words":"TZS One Hundred And Three Thousand, Seven Hundred And Twenty Eight and Eighty One Cent only.","disable_rounded_total":1,"apply_discount_on":"Grand Total","base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":"<div class=\\"tax-break-up\\" style=\\"overflow-x: auto;\\">\\n\\t<table class=\\"table table-bordered table-hover\\">\\n\\t\\t<thead>\\n\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-left\\">Item</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">Taxable Amount</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<th class=\\"text-right\\">VAT Output (Credit)</th>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\n\\t\\t\\t</tr>\\n\\t\\t</thead>\\n\\t\\t<tbody>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>8204</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 29,301.92\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 5,274.35\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>8175</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 29,301.92\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 5,274.35\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<td>8198</td>\\n\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\tTZS 29,301.92\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t<td class=\\"text-right\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t(18.0%)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tTZS 5,274.35\\n\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\n\\t\\t</tbody>\\n\\t</table>\\n</div>","per_billed":100,"status":"Completed","per_installed":0,"installation_status":"Not Installed","per_returned":0,"lr_date":"2025-02-11","amount_eligible_for_commission":87905.76,"commission_rate":0,"total_commission":0,"letter_head":"Masumin","print_without_amount":0,"group_same_items":0,"language":"en-GB","is_internal_customer":0,"customer_group":"Commercial","territory":"Tanzania","scan_delivery_note_sent":0,"custom_masumin_delivery_status":"Pending","doctype":"Delivery Note","packed_items":[],"pricing_rules":[],"items":[{"name":"1fs8f4bf8m","owner":"<EMAIL>","creation":"2025-02-11 08:25:33.772930","modified":"2025-02-11 08:26:39.416657","modified_by":"<EMAIL>","docstatus":1,"idx":1,"barcode":"4902778913963","has_item_scanned":0,"item_code":"8204","item_name":"Pen - Uniball - Eye - Red - UB-157  - Fine - 0.7mm","description":"Pen - Uniball - Eye - Red - UB-157  - Fine - 0.7mm, Stainless Steel Tip - Water Prof - Fade - Resistan, Pigment ink","item_group":"Undefined","image":"","qty":12,"custom_bin_location":"2U","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":12,"returned_qty":0,"price_list_rate":4000,"base_price_list_rate":4000,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":27.966,"discount_amount":1118.6445,"base_rate_with_margin":0,"rate":2881.3555,"amount":34576.27,"base_rate":2881.36,"base_amount":34576.27,"pricing_rules":"","stock_uom_rate":2881.3555,"is_free_item":0,"grant_commission":1,"net_rate":2441.83,"net_amount":29301.92,"base_net_rate":2441.83,"base_net_amount":29301.92,"billed_amt":34576.27,"incoming_rate":2058,"weight_per_unit":0,"total_weight":0,"warehouse":"Main Master - MPASL","use_serial_batch_fields":0,"actual_batch_qty":0,"actual_qty":298,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":424,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"cost_center":"Main - MPASL","page_break":0,"parent":"MAT-DN-2025-01557","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item"},{"name":"vl996tpl04","owner":"<EMAIL>","creation":"2025-02-11 08:25:33.772930","modified":"2025-02-11 08:26:39.417506","modified_by":"<EMAIL>","docstatus":1,"idx":2,"barcode":"*************","has_item_scanned":0,"item_code":"8175","item_name":"Pen - Uniball - MI-UB187S-  Eye - Blue - 0.7mm ","description":"Pen - Uniball - MI-UB187S-  Eye - Blue - 0.7mm , MI-UB187S - Roller Pen - 0.5mm Line - Needle, Uni-Flow System - Pigment Ink - waterproof","item_group":"Undefined","image":"","qty":12,"custom_bin_location":"2U","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":12,"returned_qty":0,"price_list_rate":3500,"base_price_list_rate":3500,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":17.676,"discount_amount":618.6445,"base_rate_with_margin":0,"rate":2881.3555,"amount":34576.27,"base_rate":2881.36,"base_amount":34576.27,"pricing_rules":"","stock_uom_rate":2881.3555,"is_free_item":0,"grant_commission":1,"net_rate":2441.83,"net_amount":29301.92,"base_net_rate":2441.83,"base_net_amount":29301.92,"billed_amt":34576.27,"incoming_rate":1966.04,"weight_per_unit":0,"total_weight":0,"warehouse":"Main Master - MPASL","use_serial_batch_fields":0,"actual_batch_qty":0,"actual_qty":0,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":46,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"cost_center":"Main - MPASL","page_break":0,"parent":"MAT-DN-2025-01557","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item"},{"name":"q8qclnrai4","owner":"<EMAIL>","creation":"2025-02-11 08:25:33.772930","modified":"2025-02-11 08:26:39.418600","modified_by":"<EMAIL>","docstatus":1,"idx":3,"barcode":"*************","has_item_scanned":0,"item_code":"8198","item_name":"Pen - Uniball - Eye - Black - UB-157 - Fine -0.7mm","description":"<div class=\\"ql-editor read-mode\\"><p>MASAKI</p></div>","item_group":"Undefined","image":"","qty":12,"custom_bin_location":"2U","stock_uom":"Pieces","uom":"Pieces","conversion_factor":1,"stock_qty":12,"returned_qty":0,"price_list_rate":3590,"base_price_list_rate":3590,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":19.739,"discount_amount":708.6445,"base_rate_with_margin":0,"rate":2881.3555,"amount":34576.27,"base_rate":2881.36,"base_amount":34576.27,"pricing_rules":"","stock_uom_rate":2881.3555,"is_free_item":0,"grant_commission":1,"net_rate":2441.83,"net_amount":29301.92,"base_net_rate":2441.83,"base_net_amount":29301.92,"billed_amt":34576.27,"incoming_rate":2450,"weight_per_unit":0,"total_weight":0,"warehouse":"Main Master - MPASL","use_serial_batch_fields":0,"actual_batch_qty":0,"actual_qty":0,"installed_qty":0,"item_tax_rate":"{}","company_total_stock":9,"packed_qty":0,"received_qty":0,"expense_account":"Cost of Goods Sold - MPASL","allow_zero_valuation_rate":0,"cost_center":"Main - MPASL","page_break":0,"parent":"MAT-DN-2025-01557","parentfield":"items","parenttype":"Delivery Note","doctype":"Delivery Note Item"}],"taxes":[{"name":"tgk95iemt4","owner":"<EMAIL>","creation":"2025-02-11 08:25:33.772930","modified":"2025-02-11 08:25:45.778889","modified_by":"<EMAIL>","docstatus":1,"idx":1,"charge_type":"On Net Total","account_head":"VAT Output (Credit Sales) - MPASL","description":"VAT Output (Credit)","included_in_print_rate":1,"included_in_paid_amount":0,"cost_center":"Main - MPASL","rate":18,"account_currency":"TZS","tax_amount":15823.04,"total":103728.8,"tax_amount_after_discount_amount":15823.04,"base_tax_amount":15823.04,"base_total":103728.8,"base_tax_amount_after_discount_amount":15823.04,"item_wise_tax_detail":"{\\"8204\\":[18.0,5274.3456],\\"8175\\":[18.0,5274.3456],\\"8198\\":[18.0,5274.3456]}","dont_recompute_tax":0,"parent":"MAT-DN-2025-01557","parentfield":"taxes","parenttype":"Delivery Note","doctype":"Sales Taxes and Charges"}],"workflow_transition_logs":[],"sales_team":[],"__onload":{"make_payment_via_journal_entry":0},"__last_sync_on":"2025-06-13T09:37:25.911Z","scan_delivery_note":"/files/Screenshot from 2025-06-03 09-48-30.png","__unsaved":1}', 'action': 'Update', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-13 12:38:39,729 ERROR frappe Error while inserting deferred Error Log record: Error Log t6hsk85en2: 'Title' (Module import failed for Workflow Transition Log, the DocType you're trying to open might be deleted.
Error: No module named 'csf_tz.csf_tz.doctype.workflow_transition_log.workflow_transition_log') will get truncated, as max characters allowed is 140
Site: masumin
Form Dict: {}
2025-07-23 15:21:48,758 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"Slhtuytuh","owner":"Administrator","creation":"2025-06-09 16:35:17.598536","modified":"2025-06-30 16:54:26.794646","modified_by":"Administrator","docstatus":0,"idx":0,"script_type":"Scheduler Event","reference_doctype":"Sales Invoice","event_frequency":"Cron","doctype_event":"After Save (Submitted Document)","api_method":"sal_email","allow_guest":0,"disabled":0,"script":"# Scheduled script to send Sales Invoice emails every 5 minutes\\n# This will check all invoices with vfd_verification_url set\\n# and email_sent not checked, then send the email\\n\\n# Get all Sales Invoices with VFD URL but not emailed\\ninvoices = frappe.get_all(\\"Sales Invoice\\",\\n    filters={\\n        \\"vfd_verification_url\\": [\\"!=\\", \\"\\"],\\n        \\"email_sent\\": 0,\\n        \\"docstatus\\": 1  # Only submitted invoices\\n    },\\n    fields=[\\"name\\", \\"customer\\", \\"customer_name\\", \\"posting_date\\", \\"po_no\\"]\\n)\\n\\nfor invoice in invoices:\\n    try:\\n        doc = frappe.get_doc(\\"Sales Invoice\\", invoice.name)\\n        \\n        # Set email subject and message\\n        subject = f\\"MASUMIN - Sales Invoice - {doc.name} - {doc.customer_name} ({doc.posting_date})\\"\\n        \\n        message = f\\"\\"\\"\\n        Dear {doc.customer_name},<br><br>\\n        <span style=\\"color: #4CAF50; font-weight: bold;\\">Thank you for your continued business with MASUMIN PRINTWAYS & STATIONERS LTD.</span><br>\\n        Please find attached your sales invoice referencing {doc.get(\'po_no\', \'N/A\')}.<br><br>\\n        Should you have any questions or require further clarification, feel free to contact our sales Department at:<br>\\n        <b>Email: <EMAIL></b><br>\\n        <b>Mobile: 0626-272706/0626-333000</b>.<br>\\n        We appreciate your partnership and look forward to serving you again.<br><br>\\n\\n        <b>Warm regards,</b><br>\\n        <b>Masumin Printways & Stationers Ltd.</b>\\n        \\"\\"\\"\\n\\n        # Get customer document to access email fields\\n        customer = frappe.get_doc(\\"Customer\\", doc.customer) if doc.customer else None\\n\\n        # Prepare recipients list\\n        recipients = []\\n        if customer:\\n            if customer.procurement_email_id:\\n                recipients.append(customer.procurement_email_id)\\n            if customer.accounts_email_id:\\n                recipients.append(customer.accounts_email_id)\\n\\n        # Generate PDF attachment\\n        pdf_data = frappe.get_print(\\n            doctype=doc.doctype,\\n            name=doc.name,\\n            print_format=\\"Masumin Sales Invoice V2\\", \\n            as_pdf=True\\n        )\\n\\n        # Send email if there are recipients\\n        if recipients:\\n            frappe.sendmail(\\n                recipients=recipients,\\n                subject=subject,\\n                message=message,\\n                reference_doctype=doc.doctype,\\n                reference_name=doc.name,\\n                attachments=[{\\n                    \'fname\': f\\"Sales_Invoice_{doc.name}.pdf\\",\\n                    \'fcontent\': pdf_data\\n                }],\\n                print_letterhead=True,\\n                now=True\\n            )\\n            \\n            # Update the invoice to mark as emailed\\n            frappe.db.set_value(\\"Sales Invoice\\", doc.name, \\"email_sent\\", 1)\\n            frappe.db.set_value(\\"Sales Invoice\\", doc.name, \\"email_sent_date\\", frappe.utils.now_datetime())\\n            frappe.db.commit()\\n            \\n            frappe.logger().info(f\\"Sales Invoice email sent for {doc.name} to {\', \'.join(recipients)}\\")\\n            \\n        else:\\n            frappe.logger().info(f\\"No email addresses found for customer {doc.customer_name} in invoice {doc.name}\\")\\n\\n    except Exception as e:\\n        frappe.log_error(f\\"Failed to process invoice {invoice.name}: {str(e)}\\")\\n        frappe.db.rollback()\\n\\nmsg = f\\"Processed {len(invoices)} invoices\\"\\nfrappe.msgprint(msg)","enable_rate_limit":0,"rate_limit_count":5,"rate_limit_seconds":86400,"doctype":"Server Script","__unsaved":1,"cron_format":"*/5 * * * *"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-23 15:22:20,291 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doc': '{"name":"Slhtuytuh","owner":"Administrator","creation":"2025-06-09 16:35:17.598536","modified":"2025-06-30 16:54:26.794646","modified_by":"Administrator","docstatus":0,"idx":0,"script_type":"Scheduler Event","reference_doctype":"Sales Invoice","event_frequency":"Cron","doctype_event":"After Save (Submitted Document)","api_method":"sal_email","allow_guest":0,"disabled":0,"script":"# Scheduled script to send Sales Invoice emails every 5 minutes\\n# This will check all invoices with vfd_verification_url set\\n# and email_sent not checked, then send the email\\n\\n# Get all Sales Invoices with VFD URL but not emailed\\ninvoices = frappe.get_all(\\"Sales Invoice\\",\\n    filters={\\n        \\"vfd_verification_url\\": [\\"!=\\", \\"\\"],\\n        \\"email_sent\\": 0,\\n        \\"docstatus\\": 1  # Only submitted invoices\\n    },\\n    fields=[\\"name\\", \\"customer\\", \\"customer_name\\", \\"posting_date\\", \\"po_no\\"]\\n)\\n\\nfor invoice in invoices:\\n    try:\\n        doc = frappe.get_doc(\\"Sales Invoice\\", invoice.name)\\n        \\n        # Set email subject and message\\n        subject = f\\"MASUMIN - Sales Invoice - {doc.name} - {doc.customer_name} ({doc.posting_date})\\"\\n        \\n        message = f\\"\\"\\"\\n        Dear {doc.customer_name},<br><br>\\n        <span style=\\"color: #4CAF50; font-weight: bold;\\">Thank you for your continued business with MASUMIN PRINTWAYS & STATIONERS LTD.</span><br>\\n        Please find attached your sales invoice referencing {doc.get(\'po_no\', \'N/A\')}.<br><br>\\n        Should you have any questions or require further clarification, feel free to contact our sales Department at:<br>\\n        <b>Email: <EMAIL></b><br>\\n        <b>Mobile: 0626-272706/0626-333000</b>.<br>\\n        We appreciate your partnership and look forward to serving you again.<br><br>\\n\\n        <b>Warm regards,</b><br>\\n        <b>Masumin Printways & Stationers Ltd.</b>\\n        \\"\\"\\"\\n\\n        # Get customer document to access email fields\\n        customer = frappe.get_doc(\\"Customer\\", doc.customer) if doc.customer else None\\n\\n        # Prepare recipients list\\n        recipients = []\\n        if customer:\\n            if customer.procurement_email_id:\\n                recipients.append(customer.procurement_email_id)\\n            if customer.accounts_email_id:\\n                recipients.append(customer.accounts_email_id)\\n\\n        # Generate PDF attachment\\n        pdf_data = frappe.get_print(\\n            doctype=doc.doctype,\\n            name=doc.name,\\n            print_format=\\"Masumin Sales Invoice V2\\", \\n            as_pdf=True\\n        )\\n\\n        # Send email if there are recipients\\n        if recipients:\\n            frappe.sendmail(\\n                recipients=recipients,\\n                subject=subject,\\n                message=message,\\n                reference_doctype=doc.doctype,\\n                reference_name=doc.name,\\n                attachments=[{\\n                    \'fname\': f\\"Sales_Invoice_{doc.name}.pdf\\",\\n                    \'fcontent\': pdf_data\\n                }],\\n                print_letterhead=True,\\n                now=True\\n            )\\n            \\n            # Update the invoice to mark as emailed\\n            frappe.db.set_value(\\"Sales Invoice\\", doc.name, \\"email_sent\\", 1)\\n            frappe.db.set_value(\\"Sales Invoice\\", doc.name, \\"email_sent_date\\", frappe.utils.now_datetime())\\n            frappe.db.commit()\\n            \\n            frappe.logger().info(f\\"Sales Invoice email sent for {doc.name} to {\', \'.join(recipients)}\\")\\n            \\n        else:\\n            frappe.logger().info(f\\"No email addresses found for customer {doc.customer_name} in invoice {doc.name}\\")\\n\\n    except Exception as e:\\n        frappe.log_error(f\\"Failed to process invoice {invoice.name}: {str(e)}\\")\\n        frappe.db.rollback()\\n\\nmsg = f\\"Processed {len(invoices)} invoices\\"\\nfrappe.msgprint(msg)","enable_rate_limit":0,"rate_limit_count":5,"rate_limit_seconds":86400,"doctype":"Server Script","__unsaved":1,"cron_format":"*/5 * * * *"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-31 13:18:48,215 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doctype': 'Sales Invoice', 'fields': '["`tabSales Invoice`.`name`","`tabSales Invoice`.`owner`","`tabSales Invoice`.`creation`","`tabSales Invoice`.`modified`","`tabSales Invoice`.`modified_by`","`tabSales Invoice`.`_user_tags`","`tabSales Invoice`.`_comments`","`tabSales Invoice`.`_assign`","`tabSales Invoice`.`_liked_by`","`tabSales Invoice`.`docstatus`","`tabSales Invoice`.`idx`","`tabSales Invoice`.`posting_date`","`tabSales Invoice`.`vfd_status`","`tabSales Invoice`.`amount_received`","`tabSales Invoice`.`total`","`tabSales Invoice`.`net_total`","`tabSales Invoice`.`total_taxes_and_charges`","`tabSales Invoice`.`grand_total`","`tabSales Invoice`.`rounding_adjustment`","`tabSales Invoice`.`rounded_total`","`tabSales Invoice`.`total_advance`","`tabSales Invoice`.`outstanding_amount`","`tabSales Invoice`.`discount_amount`","`tabSales Invoice`.`paid_amount`","`tabSales Invoice`.`total_billing_amount`","`tabSales Invoice`.`change_amount`","`tabSales Invoice`.`write_off_amount`","`tabSales Invoice`.`status`","`tabSales Invoice`.`title`","`tabSales Invoice`.`customer`","`tabSales Invoice`.`customer_name`","`tabSales Invoice`.`base_grand_total`","`tabSales Invoice`.`due_date`","`tabSales Invoice`.`company`","`tabSales Invoice`.`currency`","`tabSales Invoice`.`is_return`","`tabSales Invoice`.`_seen`","`tabSales Invoice`.`party_account_currency`"]', 'filters': '[["Sales Invoice","vfd_verification_url","is","set"],["Sales Invoice","posting_time",">",null]]', 'order_by': '`tabSales Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1', 'cmd': 'frappe.desk.reportview.get'}
2025-08-01 11:37:58,586 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:30:36,227 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:33:15,814 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:43:24,901 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:49:20,267 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:50:00,961 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:51:19,096 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 13:23:28,794 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 13:29:45,123 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 14:29:48,866 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 14:33:12,503 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 17:33:20,510 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 17:41:15,815 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 17:46:35,256 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 18:00:33,893 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 18:09:53,774 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-04 11:03:11,656 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
2025-08-04 11:08:19,238 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': 'wefdf@h5.6t', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
2025-08-04 11:08:51,216 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': 'cdvhtrfgwe', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
