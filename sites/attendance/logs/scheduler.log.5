2025-07-04 12:02:05,471 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-04 12:02:05,488 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-04 12:02:05,490 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-04 12:02:05,500 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-04 12:02:05,510 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-04 12:02:05,523 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-04 12:02:05,528 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-04 12:02:05,539 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-04 12:02:05,544 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-04 12:02:05,548 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-04 12:02:05,552 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-04 12:02:05,575 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-04 12:02:05,579 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-04 12:05:12,062 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-04 12:05:12,071 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-04 12:05:12,084 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-04 12:05:12,093 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-04 12:06:13,204 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-04 12:06:13,212 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-04 12:06:13,240 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-04 12:06:13,259 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-04 12:07:16,417 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-04 12:07:16,573 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-04 12:07:16,599 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-04 12:07:16,604 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-04 12:08:20,742 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-04 12:08:20,879 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-04 12:08:20,883 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-04 12:08:20,901 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-04 17:31:12,664 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-04 17:31:12,722 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-04 17:31:12,757 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-04 17:31:12,791 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-08 09:23:11,515 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-08 09:23:11,518 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-08 09:23:11,519 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-07-08 09:23:11,521 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-08 09:23:11,522 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-08 09:23:11,525 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-08 09:23:11,530 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-08 09:23:11,532 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-08 09:23:11,535 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-08 09:23:11,538 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-08 09:23:11,542 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-08 09:23:11,545 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-08 09:23:11,551 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-08 09:23:11,553 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-08 09:23:11,555 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-08 09:23:11,560 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-08 09:23:11,562 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-07-08 09:23:11,564 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-08 09:23:11,568 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-07-08 09:23:11,571 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-08 09:23:11,574 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-08 09:23:11,575 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-08 09:23:11,576 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-08 09:23:11,579 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-08 09:23:11,582 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-08 09:23:11,583 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-07-08 09:23:11,590 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-08 09:23:11,592 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-07-08 09:23:11,595 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-08 09:23:11,605 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-08 09:23:11,608 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-08 09:23:11,610 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-08 09:23:11,621 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-08 09:23:11,624 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-08 09:23:11,626 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-08 09:23:11,635 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-08 09:23:11,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-07-08 09:23:11,637 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-08 09:23:11,644 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-07-09 09:41:47,423 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-09 09:41:47,426 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-09 09:41:47,428 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-09 09:41:47,430 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-09 09:41:47,433 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-09 09:41:47,439 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-09 09:41:47,440 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-09 09:41:47,444 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-09 09:41:47,446 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-09 09:41:47,452 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-09 09:41:47,457 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-09 09:41:47,465 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-09 09:41:47,468 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-09 09:41:47,470 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-09 09:41:47,478 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-09 09:41:47,479 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-09 09:41:47,483 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-09 09:41:47,484 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-09 09:41:47,486 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-09 09:41:47,491 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-09 09:41:47,500 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-09 09:41:47,505 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-09 09:41:47,509 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-09 09:41:47,512 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-09 09:41:47,518 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-09 09:41:47,520 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-09 09:41:47,521 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-09 09:41:47,525 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-09 09:41:47,526 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-09 09:41:47,529 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-09 09:41:47,531 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-09 09:41:47,538 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-11 16:33:35,324 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-11 16:33:35,327 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-11 16:33:35,329 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-11 16:33:35,331 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-11 16:33:35,334 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-11 16:33:35,336 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-11 16:33:35,338 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-11 16:33:35,342 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-11 16:33:35,344 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-11 16:33:35,346 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-11 16:33:35,349 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-11 16:33:35,359 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-11 16:33:35,367 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-11 16:33:35,368 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-11 16:33:35,371 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-11 16:33:35,373 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-11 16:33:35,378 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-11 16:33:35,380 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-11 16:33:35,381 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-11 16:33:35,383 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-11 16:33:35,386 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-11 16:33:35,392 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-11 16:33:35,393 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-11 16:33:35,397 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-11 16:33:35,399 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-11 16:33:35,402 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-11 16:33:35,404 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-11 16:33:35,406 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-11 16:33:35,407 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-11 16:33:35,412 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-11 16:33:35,418 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-11 16:33:35,420 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-11 17:07:32,188 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-11 17:07:32,218 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-11 17:07:32,231 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-11 17:07:32,257 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-11 17:07:32,262 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-11 17:07:32,270 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-11 17:07:32,277 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-11 17:07:32,288 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-11 17:07:32,292 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-11 17:07:32,344 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-11 17:07:32,370 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-11 17:07:32,384 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-11 17:07:32,441 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-15 12:16:07,353 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-15 12:16:07,355 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-15 12:16:07,357 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-07-15 12:16:07,360 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-07-15 12:16:07,363 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-15 12:16:07,366 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-07-15 12:16:07,371 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-15 12:16:07,376 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-15 12:16:07,381 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-07-15 12:16:07,384 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-15 12:16:07,391 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-15 12:16:07,392 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-15 12:16:07,394 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-15 12:16:07,397 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-15 12:16:07,399 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-15 12:16:07,403 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-15 12:16:07,404 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-15 12:16:07,410 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-15 12:16:07,412 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-07-15 12:16:07,418 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-15 12:16:07,427 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-07-15 12:16:07,434 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-15 12:16:07,437 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-15 12:16:07,438 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-15 12:16:07,442 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-15 12:16:07,447 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-15 12:16:07,449 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-15 12:16:07,453 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-15 12:16:07,455 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-15 12:16:07,457 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-15 12:16:07,459 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-15 12:16:07,462 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-15 12:16:07,464 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-15 12:16:07,466 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-15 12:16:07,468 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-15 12:16:07,469 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-15 12:16:07,473 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-07-15 12:16:07,477 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-15 12:16:07,480 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-15 17:01:34,890 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-15 17:01:34,898 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-15 17:01:34,906 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-15 17:01:34,917 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-15 17:01:34,956 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-15 17:01:34,991 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-15 17:01:35,027 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-15 17:01:35,032 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-07-15 17:01:35,040 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-07-15 17:01:35,058 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-15 17:01:35,069 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-15 17:01:35,081 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-15 17:01:35,089 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-15 17:01:35,110 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-15 17:01:35,166 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-07-15 17:01:35,193 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-15 17:01:35,208 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-15 17:01:35,232 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-07-15 17:01:35,238 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-15 17:01:35,245 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-15 17:01:35,265 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-15 17:01:35,273 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-07-15 17:01:35,278 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-15 17:01:35,286 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-15 17:01:35,307 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-15 17:01:35,315 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-15 17:01:35,329 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-15 23:51:40,794 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-15 23:51:40,801 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-15 23:51:40,815 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-15 23:51:40,830 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-15 23:51:40,835 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-15 23:51:40,839 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-15 23:51:40,846 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-15 23:51:40,857 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-15 23:51:40,865 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-15 23:51:40,867 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-15 23:51:40,870 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-15 23:51:40,883 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-15 23:51:40,895 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 00:01:47,923 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-16 00:01:47,944 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 00:01:47,946 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-16 00:01:47,951 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 00:01:47,954 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-16 00:01:47,960 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 00:01:47,963 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-16 00:01:47,964 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 00:01:47,965 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-16 00:01:47,968 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 00:01:47,971 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-16 00:01:47,973 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-16 00:01:47,977 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-16 00:01:47,979 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-16 00:01:47,980 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-16 00:01:47,991 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-16 00:01:48,008 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-16 00:01:48,009 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-16 00:01:48,011 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-16 00:01:48,014 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-16 00:01:48,015 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-16 00:01:48,019 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-16 00:01:48,021 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 00:01:48,022 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 00:01:48,024 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 00:01:48,028 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-16 00:01:48,029 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-16 00:01:48,030 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-16 00:01:48,035 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-16 00:01:48,038 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-16 00:01:48,039 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-16 00:01:48,044 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 09:26:37,925 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 09:26:37,939 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 09:26:37,955 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-16 09:26:37,978 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-16 09:26:37,992 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-16 09:26:37,998 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 09:26:38,021 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 09:26:38,025 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 09:26:38,043 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 09:26:38,055 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 09:26:38,094 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 09:26:38,107 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-16 09:26:38,123 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 14:09:34,153 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 14:09:34,174 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-16 14:09:34,182 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 14:09:34,189 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 14:09:34,224 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-16 14:09:34,339 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 14:09:34,353 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 14:09:34,359 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-16 14:09:34,365 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 14:09:34,390 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 14:09:34,399 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 14:09:34,409 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-16 14:09:34,430 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 15:16:43,836 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-16 15:16:43,863 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-16 15:16:43,868 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-07-16 15:16:43,871 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-07-16 15:16:43,890 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-16 15:16:43,897 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-16 15:16:43,900 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-16 15:16:43,910 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-16 15:16:43,918 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-07-16 17:01:45,450 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-16 17:01:45,457 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 17:01:45,460 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-16 17:01:45,468 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-07-16 17:01:45,474 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-07-16 17:01:45,476 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 17:01:45,480 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-16 17:01:45,482 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-16 17:01:45,487 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-07-16 17:01:45,489 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 17:01:45,491 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-07-16 17:01:45,493 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 17:01:45,496 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-16 17:01:45,502 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-07-16 17:01:45,509 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-16 17:01:45,513 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 17:01:45,517 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-07-16 17:01:45,520 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-16 17:01:45,524 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 17:01:45,529 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 17:01:45,546 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-16 17:01:45,551 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-16 17:01:45,554 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-16 17:01:45,557 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-07-16 17:01:45,560 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-16 17:01:45,569 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-16 17:01:45,576 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-07-16 17:01:45,578 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 17:01:45,586 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-07-16 17:01:45,589 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-16 17:01:45,603 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-07-16 17:01:45,607 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-07-16 17:01:45,608 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 17:01:45,610 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-16 17:01:45,613 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-07-16 17:02:48,249 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-07-16 17:02:48,281 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 17:02:48,282 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-07-16 17:02:48,284 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-16 17:02:48,286 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-16 17:02:48,288 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-16 17:02:48,294 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-16 17:02:48,299 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 17:02:48,303 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-16 17:02:48,310 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-16 17:02:48,312 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-07-16 17:02:48,315 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-16 17:02:48,316 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 17:02:48,320 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-16 17:02:48,324 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 17:02:48,327 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 17:02:48,330 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-07-16 17:02:48,343 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-16 17:02:48,348 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-07-16 17:02:48,351 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-16 17:02:48,355 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-16 17:02:48,363 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-07-16 17:02:48,366 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-07-16 17:02:48,372 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 17:02:48,376 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 17:02:48,387 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 17:02:48,388 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-07-16 17:02:48,396 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-07-16 17:02:48,398 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-07-16 17:02:48,400 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-16 17:02:48,409 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-16 17:02:48,414 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-16 17:02:48,415 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 17:02:48,418 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-07-16 17:02:48,423 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-07-16 17:04:02,949 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 17:04:02,984 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-16 17:04:02,993 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 17:04:02,996 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because2025-07-16 18:10:40,529 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-16 18:10:40,536 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-16 18:10:40,552 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-16 18:10:40,576 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-16 18:10:40,608 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-16 18:10:40,617 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-16 18:10:40,623 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-16 18:10:40,684 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-16 18:10:40,692 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-16 18:10:40,724 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-17 00:01:45,075 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-17 00:01:45,097 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-17 00:01:45,099 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-17 00:01:45,100 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-17 00:01:45,102 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for attendance
2025-07-17 00:01:45,104 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-07-17 00:01:45,106 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-17 00:01:45,107 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-17 00:01:45,110 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for attendance
2025-07-17 00:01:45,114 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-17 00:01:45,115 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for attendance
2025-07-17 00:01:45,116 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for attendance
2025-07-17 00:01:45,118 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-07-17 00:01:45,119 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-17 00:01:45,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-17 00:01:45,126 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-17 00:01:45,127 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-17 00:01:45,128 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-17 00:01:45,130 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for attendance
2025-07-17 00:01:45,131 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-17 00:01:45,133 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-17 00:01:45,135 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-17 00:01:45,137 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-07-17 00:01:45,138 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-07-17 00:01:45,140 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for attendance
2025-07-17 00:01:45,143 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-17 00:01:45,145 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for attendance
2025-07-17 00:01:45,148 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for attendance
2025-07-17 00:01:45,150 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for attendance
2025-07-17 00:01:45,153 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-17 00:01:45,155 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-17 00:01:45,156 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for attendance
2025-07-17 00:01:45,159 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for attendance
2025-07-17 00:01:45,161 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for attendance
2025-07-17 00:01:45,163 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-17 00:01:45,166 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-17 00:01:45,167 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-17 00:01:45,168 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for attendance
2025-07-17 00:01:45,169 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-07-17 00:01:45,171 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for attendance
2025-07-17 00:01:45,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-17 00:01:45,174 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-17 00:01:45,175 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-17 00:01:45,176 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-17 00:01:45,178 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-17 00:01:45,180 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-17 00:01:45,181 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-17 00:01:45,184 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-17 00:01:45,186 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for attendance
2025-07-17 00:01:45,188 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-17 00:01:45,190 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-17 00:01:45,192 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for attendance
2025-07-17 00:01:45,195 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for attendance
2025-07-17 00:01:45,196 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for attendance
2025-07-17 00:01:45,198 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-17 00:01:45,200 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-17 00:01:45,201 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-17 00:01:45,202 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-17 00:01:45,204 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for attendance
2025-07-17 00:01:45,205 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-17 00:01:45,206 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-17 00:01:45,209 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for attendance
2025-07-17 00:01:45,211 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-17 00:01:45,216 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-17 00:01:45,218 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-17 00:02:46,905 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-17 00:02:46,913 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-17 00:02:46,926 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-17 00:02:46,935 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-17 00:02:46,946 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-17 00:02:46,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-17 00:02:46,956 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-17 00:02:46,960 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-17 00:02:46,962 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-17 00:02:46,964 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-17 00:02:46,965 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-17 00:02:46,974 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-17 00:02:46,975 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-17 00:02:46,980 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-17 00:02:46,982 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-17 00:02:46,986 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-17 00:02:46,987 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-17 00:02:46,993 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-17 00:02:46,995 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-17 00:02:46,997 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-17 00:02:46,999 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-17 00:02:47,000 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-17 00:02:47,007 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-17 00:02:47,018 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-17 00:02:47,024 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-17 00:02:47,028 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-17 00:02:47,031 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-17 00:02:47,032 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-17 00:02:47,036 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-17 00:02:47,040 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-17 00:02:47,044 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-17 00:02:47,054 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-17 18:57:55,622 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-17 18:57:55,634 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-17 18:57:55,638 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-17 18:57:55,651 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-17 18:57:55,663 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-17 18:57:55,668 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-17 18:57:55,684 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-17 18:57:55,693 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-17 18:57:55,703 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-17 18:57:55,728 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-17 18:57:55,735 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-18 18:21:47,133 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-07-18 18:21:47,154 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for attendance
2025-07-18 18:21:47,155 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-18 18:21:47,157 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for attendance
2025-07-18 18:21:47,159 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-07-18 18:21:47,161 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-07-18 18:21:47,164 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-07-18 18:21:47,166 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-18 18:21:47,168 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-07-18 18:21:47,170 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for attendance
2025-07-18 18:21:47,171 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-07-18 18:21:47,172 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-07-18 18:21:47,173 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for attendance
2025-07-18 18:21:47,175 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for attendance
2025-07-18 18:21:47,176 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-18 18:21:47,177 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-07-18 18:21:47,178 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for attendance
2025-07-18 18:21:47,179 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for attendance
2025-07-18 18:21:47,180 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-18 18:21:47,181 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for attendance
2025-07-18 18:21:47,182 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-07-18 18:21:47,183 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for attendance
2025-07-18 18:21:47,184 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for attendance
2025-07-18 18:21:47,185 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-07-18 18:21:47,186 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for attendance
2025-07-18 18:21:47,187 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-18 18:21:47,188 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-07-18 18:21:47,190 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-07-18 18:21:47,191 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-07-18 18:21:47,193 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-07-18 18:21:47,195 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-07-18 18:21:47,196 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-18 18:21:47,198 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for attendance
2025-07-18 18:21:47,198 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for attendance
2025-07-18 18:21:47,199 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-18 18:21:47,201 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-07-18 18:21:47,202 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for attendance
2025-07-18 18:21:47,203 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-07-18 18:21:47,204 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for attendance
2025-07-18 18:21:47,205 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for attendance
2025-07-18 18:21:47,206 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-07-18 18:21:47,208 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for attendance
2025-07-18 18:21:47,209 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-07-18 18:21:47,210 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-07-18 18:21:47,211 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-07-18 18:21:47,212 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-18 18:21:47,213 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for attendance
2025-07-18 18:21:47,214 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-18 18:21:47,215 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-07-18 18:21:47,216 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for attendance
2025-07-18 18:21:47,218 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-07-18 18:21:47,219 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-07-18 18:21:47,220 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for attendance
2025-07-18 18:21:47,222 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for attendance
2025-07-18 18:21:47,223 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for attendance
2025-07-18 18:21:47,223 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-07-18 18:21:47,225 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-07-18 18:21:47,227 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for attendance
2025-07-18 18:21:47,228 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for attendance
2025-07-18 18:21:47,229 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for attendance
2025-07-18 18:21:47,643 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-07-18 18:21:47,649 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for attendance
2025-07-18 18:21:47,655 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-07-18 18:21:47,660 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for attendance
2025-07-18 18:21:47,666 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-07-18 18:21:47,670 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for attendance
2025-07-18 18:21:47,673 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-07-18 18:21:47,676 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-07-18 18:21:47,679 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for attendance
2025-07-18 18:21:47,681 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-07-18 18:21:47,682 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-07-18 18:21:47,684 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-18 18:21:47,686 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-07-18 18:21:47,687 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for attendance
2025-07-18 18:21:47,689 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for attendance
2025-07-18 18:21:47,692 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for attendance
2025-07-18 18:21:47,693 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for attendance
2025-07-18 18:21:47,695 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for attendance
2025-07-18 18:21:47,696 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-18 18:21:47,697 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-07-18 18:21:47,699 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for attendance
2025-07-18 18:21:47,700 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-07-18 18:21:47,701 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for attendance
2025-07-18 18:21:47,702 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for attendance
2025-07-18 18:21:47,703 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-07-18 18:21:47,704 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-18 18:21:47,705 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-07-18 18:21:47,706 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-07-18 18:21:47,707 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-18 18:21:47,708 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-07-18 18:21:47,709 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-07-18 18:21:47,710 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for attendance
2025-07-18 18:21:47,711 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for attendance
2025-07-18 18:21:47,712 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for attendance
2025-07-18 18:21:47,713 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for attendance
2025-07-18 18:21:47,714 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-07-19 08:00:47,420 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-07-19 08:00:47,448 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-07-19 08:00:47,461 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-07-19 08:00:47,463 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-07-19 08:00:47,468 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-07-19 08:00:47,470 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-07-19 08:00:47,472 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-07-19 08:00:47,475 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-07-19 08:00:47,478 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-07-19 08:00:47,483 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-07-19 08:00:47,484 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-07-19 08:00:47,486 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-07-19 08:00:47,489 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-07-19 08:00:47,491 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
