2025-06-25 16:43:49,071 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:44:50,143 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:45:50,587 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:46:50,850 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:47:51,879 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:48:52,464 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:49:53,134 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:50:53,383 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:51:54,073 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:52:54,830 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:53:55,292 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:54:55,880 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:55:56,720 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:56:57,375 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:57:58,077 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:58:58,825 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 16:59:59,054 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:00:59,732 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:02:01,345 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:03:02,059 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:04:02,200 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:05:03,216 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:06:03,623 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:07:04,470 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:08:04,696 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:09:05,533 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 17:10:06,038 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:29:02,679 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:30:04,037 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:31:04,115 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:32:04,894 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:33:05,898 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:34:05,652 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:35:06,983 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:36:07,451 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:37:08,622 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:38:09,002 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:39:09,883 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:40:10,196 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:41:11,665 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:42:11,687 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:43:12,439 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:44:14,380 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:45:15,699 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:46:16,324 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:47:16,495 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:48:17,924 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:49:18,793 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:50:20,085 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:51:20,817 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:52:23,841 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:53:24,472 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:54:24,734 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-26 10:55:25,148 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    # enqueue event if last execution is done
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    # if the next scheduled event is before NOW, then its due!
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    if not self.cron_format:
                     ^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
