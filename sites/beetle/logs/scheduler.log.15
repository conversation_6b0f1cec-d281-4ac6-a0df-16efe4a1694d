2025-06-24 16:29:51,321 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:30:53,479 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:31:54,714 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:32:55,247 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:33:56,886 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:34:57,755 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:35:58,942 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:37:00,506 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:38:04,953 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:39:05,378 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:40:11,085 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:41:13,294 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:42:21,113 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:43:23,174 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:44:29,653 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:45:32,701 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:46:36,268 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:47:39,693 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:48:46,320 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:49:50,041 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:50:55,235 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:51:58,131 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:53:00,085 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:54:00,990 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:55:02,152 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:56:03,583 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:57:04,666 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:58:05,048 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 16:59:06,062 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:00:07,018 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:01:07,779 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:02:09,785 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:03:11,040 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:04:11,849 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:05:12,897 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:06:14,844 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:07:14,982 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:08:17,686 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:09:18,406 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:10:20,005 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:11:21,550 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:12:22,554 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:13:23,492 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:14:25,287 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:15:26,120 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:16:27,494 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:17:29,404 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:18:30,371 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:19:30,921 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:20:32,718 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:21:34,377 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:22:35,051 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-24 17:23:36,130 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
