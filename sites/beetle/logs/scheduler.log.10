2025-06-25 10:37:22,378 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 10:38:23,536 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 10:39:23,578 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 10:40:24,679 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 10:41:25,071 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:14:06,569 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:15:07,491 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:16:08,536 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:17:08,558 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:18:09,302 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:19:09,771 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:20:10,010 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:21:10,762 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:22:11,633 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:23:11,999 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:24:12,716 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:25:13,627 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:26:14,073 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:27:15,477 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:28:16,282 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:29:16,633 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:30:17,515 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:31:18,887 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:32:19,890 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:33:20,155 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:34:20,862 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:35:22,070 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:36:22,705 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:37:22,973 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:38:24,131 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:39:24,250 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 11:40:25,896 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:29:42,669 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:30:44,456 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:31:45,173 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:32:46,909 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:33:47,352 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:35:08,015 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:37:19,276 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:39:03,409 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:40:53,738 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:44:12,606 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:45:12,948 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:46:14,998 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:47:15,242 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:48:16,038 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:49:16,846 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:50:17,548 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:51:18,557 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:52:18,609 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:53:19,573 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:54:20,473 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
2025-06-25 14:55:20,885 ERROR scheduler Exception in Enqueue Events for Site beetle
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 102, in enqueue_events_for_site
    enqueue_events(site=site)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 122, in enqueue_events
    if job_type.enqueue():
       ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 72, in enqueue
    if self.is_event_due() or force:
       ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 91, in is_event_due
    return self.get_next_execution() <= (current_time or now_datetime())
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/core/doctype/scheduled_job_type/scheduled_job_type.py", line 128, in get_next_execution
    next_execution = croniter(self.cron_format, last_execution).get_next(datetime)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 190, in __init__
    self.expanded, self.nth_weekday_of_month = self.expand(
                                               ^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 851, in expand
    return cls._expand(expr_format, hash_id=hash_id,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/croniter/croniter.py", line 659, in _expand
    efl = expr_format.lower()
          ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'lower'
