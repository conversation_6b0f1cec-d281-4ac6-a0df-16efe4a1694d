2025-05-23 14:02:49,453 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 14:02:49,487 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 14:02:49,502 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 14:02:49,512 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 14:02:49,520 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 14:03:50,253 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 14:03:50,255 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 14:03:50,267 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 14:03:50,272 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 14:03:50,276 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 14:03:50,283 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 14:03:50,286 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 14:03:50,300 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 14:03:50,307 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 14:03:50,309 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 14:03:50,318 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 14:03:50,323 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 14:03:50,332 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 14:03:50,335 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 14:03:50,345 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 14:03:50,348 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-23 14:03:50,364 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 14:03:50,366 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 14:03:50,371 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 14:03:50,375 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 14:03:50,390 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 14:03:50,393 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 14:03:50,396 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 14:03:50,398 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 14:03:50,403 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 14:03:50,410 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 14:03:50,432 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 14:03:50,437 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 14:03:50,442 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 14:03:50,444 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 14:03:50,451 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 14:03:50,458 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 14:03:50,464 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 14:03:50,466 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 14:04:50,625 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 14:04:50,638 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 14:04:50,650 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 14:04:50,657 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 14:04:50,660 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 14:04:50,685 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 14:04:50,688 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 14:04:50,691 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 14:04:50,704 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 14:04:50,707 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 14:04:50,710 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 14:04:50,713 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-23 14:04:50,716 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 14:04:50,720 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 14:04:50,729 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 14:04:50,732 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 14:04:50,735 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 14:04:50,740 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 14:04:50,745 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 14:04:50,748 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 14:04:50,758 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 14:04:50,762 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 14:04:50,767 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 14:04:50,770 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 14:04:50,787 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-23 14:04:50,801 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 14:04:50,810 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 14:04:50,817 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 14:04:50,823 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 14:04:50,827 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 14:04:50,830 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 14:04:50,837 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 14:04:50,842 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 14:04:50,846 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 14:04:50,862 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 14:05:51,991 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 14:05:51,997 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 14:05:52,000 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-23 14:05:52,004 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 14:05:52,007 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 14:05:52,013 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 14:05:52,020 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 14:05:52,034 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-23 14:05:52,037 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 14:05:52,045 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-23 14:05:52,048 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 14:05:52,052 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 14:05:52,056 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 14:05:52,059 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 14:05:52,062 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 14:05:52,073 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 14:05:52,077 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 14:05:52,090 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 14:05:52,092 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 14:05:52,101 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 14:05:52,104 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 14:05:52,107 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 14:05:52,123 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 14:05:52,125 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 14:05:52,131 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 14:05:52,133 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 14:05:52,140 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 14:05:52,149 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 14:05:52,151 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 14:05:52,155 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 14:05:52,167 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 14:05:52,169 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 14:05:52,172 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 14:05:52,187 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 14:05:52,193 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 14:05:52,206 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 14:06:53,040 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 14:06:53,043 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 14:06:53,057 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 14:06:53,063 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 14:06:53,065 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-23 14:06:53,069 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-23 14:06:53,075 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-23 14:06:53,078 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 14:06:53,083 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 14:06:53,100 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 14:06:53,103 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 14:06:53,106 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 14:06:53,118 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 14:06:53,126 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 14:06:53,133 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 14:06:53,135 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 14:06:53,140 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 14:06:53,142 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 14:06:53,146 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 14:06:53,161 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 14:06:53,166 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 14:06:53,178 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 14:06:53,185 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 14:06:53,191 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 14:06:53,198 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 14:06:53,201 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 14:06:53,203 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 14:06:53,208 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 14:06:53,210 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 14:06:53,218 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-23 14:06:53,222 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 14:06:53,223 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 14:06:53,227 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 15:01:58,371 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 15:01:58,460 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 15:01:58,504 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 15:01:58,517 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 15:01:58,534 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 15:01:58,537 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 15:01:58,541 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 15:01:58,558 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 15:01:58,571 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 15:01:58,575 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 15:01:58,583 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 15:01:58,594 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 15:01:58,611 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 15:01:58,619 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 15:01:58,643 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 15:01:58,652 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 15:01:58,665 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 15:01:58,674 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 15:01:58,683 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 15:01:58,689 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 15:01:58,692 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 15:01:58,697 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 15:01:58,701 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 15:01:58,716 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 15:01:58,748 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 15:01:58,753 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 15:01:58,764 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 15:01:58,768 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 15:01:58,779 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 15:01:58,784 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 15:01:58,792 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 15:01:58,796 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 15:01:58,804 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 15:03:00,334 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 15:03:00,354 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 15:03:00,374 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 15:03:00,379 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 15:03:00,397 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 15:03:00,403 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 15:03:00,421 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 15:03:00,425 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 15:03:00,454 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 15:03:00,466 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 15:03:00,474 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 15:03:00,478 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 15:03:00,490 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 15:03:00,511 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 15:03:00,515 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 15:03:00,520 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 15:03:00,529 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 15:03:00,542 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 15:03:00,551 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 15:03:00,555 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 15:03:00,564 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 15:03:00,569 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 15:03:00,613 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 15:03:00,668 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 15:03:00,676 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 15:03:00,700 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 15:03:00,723 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 15:03:00,741 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 15:03:00,746 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 15:03:00,756 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 15:03:00,769 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 15:03:00,777 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 15:03:00,781 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 15:05:09,295 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 15:05:09,334 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 15:05:09,356 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 15:05:09,423 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 15:05:09,446 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 15:05:09,473 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 15:05:09,482 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 15:05:09,507 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 15:05:09,545 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 15:05:09,623 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 15:05:09,639 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 15:05:09,645 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 15:05:09,672 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 15:05:09,696 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 15:05:09,752 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 15:05:09,771 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 15:05:09,785 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 15:05:09,790 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-23 15:05:09,802 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 15:05:09,807 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 15:05:09,848 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 15:05:09,879 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 15:05:09,904 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 15:05:09,916 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 15:05:09,947 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 15:05:09,987 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 15:05:09,992 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 15:05:10,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 15:05:10,012 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 15:05:10,062 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 15:05:10,091 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-23 15:05:10,129 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 15:05:10,140 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 15:05:10,160 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 15:06:11,293 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-23 15:06:11,363 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-23 15:06:11,375 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-23 15:06:11,401 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-23 15:06:11,416 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-23 15:06:11,449 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-23 15:06:11,473 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-23 15:06:11,503 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-23 15:06:11,528 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-23 15:06:11,554 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-23 15:06:11,561 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-23 15:06:11,570 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-23 15:06:11,600 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-23 15:06:11,626 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-23 15:06:11,650 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-23 15:06:11,700 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-23 15:06:11,734 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-23 15:06:11,760 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-23 15:06:11,789 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-23 15:06:11,820 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-23 15:06:11,845 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-23 15:06:11,857 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-23 15:06:11,867 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-23 15:06:11,886 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-23 15:06:11,892 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-23 15:06:11,898 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-23 15:06:11,908 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-23 15:06:11,923 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-23 15:06:11,947 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-23 15:06:11,958 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-23 15:06:12,018 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-23 15:06:12,024 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-23 15:06:12,030 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-23 15:06:12,049 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-23 15:06:12,055 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-27 10:27:52,640 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:27:52,648 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-27 10:27:52,659 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-27 10:27:52,663 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-27 10:27:52,667 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-27 10:27:52,672 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-27 10:27:52,678 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for wasco
2025-05-27 10:27:52,682 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-27 10:27:52,685 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-27 10:27:52,691 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-27 10:27:52,696 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-27 10:27:52,700 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-27 10:27:52,703 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:27:52,709 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-27 10:27:52,714 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-27 10:27:52,718 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for wasco
2025-05-27 10:27:52,724 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-27 10:27:52,729 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-27 10:27:52,734 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-27 10:27:52,738 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-27 10:27:52,745 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:27:52,749 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-27 10:27:52,755 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-27 10:27:52,759 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-27 10:27:52,763 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-27 10:27:52,766 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-27 10:27:52,770 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-27 10:27:52,774 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-27 10:27:52,777 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-27 10:27:52,781 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:27:52,785 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-27 10:27:52,788 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-27 10:27:52,792 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:27:52,795 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-27 10:27:52,798 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-27 10:27:52,802 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-27 10:27:52,805 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-27 10:27:52,808 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-05-27 10:27:52,812 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-27 10:27:52,815 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-27 10:27:52,818 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-27 10:27:52,821 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-27 10:27:52,824 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-27 10:27:52,828 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-27 10:27:52,831 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-27 10:27:52,834 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-27 10:27:52,837 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-27 10:27:52,841 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-27 10:27:52,844 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-27 10:27:52,847 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-27 10:27:52,853 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-27 10:27:52,856 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-27 10:27:52,859 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-27 10:27:52,863 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-05-27 10:27:52,865 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-27 10:27:52,869 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:27:52,871 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-27 10:27:52,874 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-27 10:27:52,876 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-27 10:27:52,878 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-05-27 10:27:52,880 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:27:52,883 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-27 10:27:52,885 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-27 10:27:52,887 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-27 10:27:52,889 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-27 10:27:52,892 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-27 10:27:52,894 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-27 10:27:52,897 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:27:52,899 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-27 10:27:52,901 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-27 10:27:52,903 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-27 10:27:52,906 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-27 10:27:52,908 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-27 10:27:52,910 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:27:52,911 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-27 10:27:52,913 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-27 10:27:52,915 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-27 10:27:52,917 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-27 10:27:52,918 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-27 10:27:52,920 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-27 10:27:52,922 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:27:52,924 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-27 10:27:52,926 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for wasco
2025-05-27 10:27:52,928 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:27:52,930 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for wasco
2025-05-27 10:27:52,932 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-27 10:27:52,934 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-27 10:27:52,936 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-27 10:27:52,938 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-27 10:27:52,939 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-27 10:27:52,941 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-27 10:27:52,943 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-27 10:27:52,945 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-27 10:27:52,947 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-27 10:27:52,948 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-27 10:27:52,950 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-27 10:27:52,952 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-27 10:27:52,954 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-27 10:27:52,956 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-27 10:27:52,957 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:27:52,959 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-27 10:27:52,963 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-27 10:27:52,965 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-27 10:27:52,967 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-27 10:27:52,970 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-27 10:27:52,972 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-27 10:27:52,973 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-27 10:27:52,975 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for wasco
2025-05-27 10:27:52,978 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-27 10:27:52,980 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-27 10:27:52,983 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-27 10:28:53,265 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-27 10:28:53,272 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-27 10:28:53,274 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-27 10:28:53,276 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:28:53,278 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-05-27 10:28:53,281 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-27 10:28:53,283 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-27 10:28:53,285 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-27 10:28:53,287 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-05-27 10:28:53,289 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for wasco
2025-05-27 10:28:53,291 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-27 10:28:53,293 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-27 10:28:53,295 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for wasco
2025-05-27 10:28:53,297 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-27 10:28:53,299 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:28:53,301 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-27 10:28:53,303 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-27 10:28:53,304 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-27 10:28:53,306 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-27 10:28:53,308 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-27 10:28:53,310 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-27 10:28:53,312 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-27 10:28:53,314 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-27 10:28:53,315 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-27 10:28:53,317 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-27 10:28:53,319 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-27 10:28:53,321 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-27 10:28:53,323 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:28:53,326 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:28:53,329 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-27 10:28:53,331 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-27 10:28:53,333 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-27 10:28:53,336 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-27 10:28:53,338 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-27 10:28:53,340 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-27 10:28:53,343 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-27 10:28:53,346 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-27 10:28:53,348 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-27 10:28:53,351 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-27 10:28:53,355 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-27 10:28:53,357 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-27 10:28:53,359 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-27 10:28:53,362 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-27 10:28:53,364 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-27 10:28:53,366 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-27 10:28:53,368 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-27 10:28:53,371 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:28:53,373 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-05-27 10:28:53,376 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for wasco
2025-05-27 10:28:53,378 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-27 10:28:53,380 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-27 10:28:53,384 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-27 10:28:53,387 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-27 10:28:53,389 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-27 10:28:53,391 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-27 10:28:53,393 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-27 10:28:53,395 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for wasco
2025-05-27 10:28:53,398 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-27 10:28:53,400 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-27 10:28:53,402 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-27 10:28:53,405 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-27 10:28:53,407 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-27 10:28:53,409 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-27 10:28:53,411 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-27 10:28:53,413 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-27 10:28:53,416 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-27 10:28:53,418 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-27 10:28:53,420 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-27 10:28:53,423 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-27 10:28:53,425 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-27 10:28:53,427 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-27 10:28:53,429 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-27 10:28:53,431 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-27 10:28:53,433 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:28:53,436 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-27 10:28:53,438 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-27 10:28:53,440 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-27 10:28:53,442 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-27 10:28:53,446 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-27 10:28:53,448 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-27 10:28:53,450 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:28:53,452 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-27 10:28:53,454 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-27 10:28:53,456 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-27 10:28:53,458 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:28:53,461 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-27 10:28:53,463 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-27 10:28:53,465 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:28:53,467 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:28:53,470 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-27 10:28:53,472 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-27 10:28:53,474 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-27 10:28:53,476 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:28:53,478 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-27 10:28:53,481 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-27 10:28:53,483 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-27 10:28:53,485 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-27 10:28:53,488 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-27 10:28:53,490 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-27 10:28:53,492 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-27 10:28:53,495 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-27 10:28:53,497 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-27 10:28:53,499 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-27 10:28:53,501 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-27 10:28:53,503 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-27 10:28:53,506 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-27 10:28:53,508 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-27 10:28:53,510 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:28:53,512 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-27 10:28:53,515 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-27 10:28:53,517 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for wasco
2025-05-27 10:29:57,593 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-27 10:29:57,662 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-27 10:29:57,670 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-27 10:29:57,674 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-27 10:29:57,679 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-27 10:29:57,684 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-27 10:29:57,687 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for wasco
2025-05-27 10:29:57,690 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-27 10:29:57,693 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-27 10:29:57,697 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:29:57,701 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-27 10:29:57,704 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-27 10:29:57,707 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-27 10:29:57,709 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for wasco
2025-05-27 10:29:57,713 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-27 10:29:57,717 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-05-27 10:29:57,720 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-27 10:29:57,723 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-27 10:29:57,725 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-27 10:29:57,727 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-27 10:29:57,732 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-27 10:29:57,735 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-27 10:29:57,738 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-05-27 10:29:57,740 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-27 10:29:57,742 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-27 10:29:57,744 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-27 10:29:57,746 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:29:57,748 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for wasco
2025-05-27 10:29:57,750 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-27 10:29:57,752 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-27 10:29:57,754 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-27 10:29:57,757 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:29:57,761 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-27 10:29:57,763 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-27 10:29:57,765 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-27 10:29:57,767 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-27 10:29:57,769 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-27 10:29:57,771 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-27 10:29:57,773 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:29:57,775 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-27 10:29:57,777 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-27 10:29:57,778 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-27 10:29:57,780 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for wasco
2025-05-27 10:29:57,782 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-27 10:29:57,784 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-05-27 10:29:57,786 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-27 10:29:57,789 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-27 10:29:57,791 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-27 10:29:57,792 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-27 10:29:57,794 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-27 10:29:57,796 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-27 10:29:57,798 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-27 10:29:57,800 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-27 10:29:57,802 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-27 10:29:57,804 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-27 10:29:57,806 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-27 10:29:57,808 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-27 10:29:57,810 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-27 10:29:57,813 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-27 10:29:57,817 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-27 10:29:57,819 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-27 10:29:57,823 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-27 10:29:57,826 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-27 10:29:57,829 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-27 10:29:57,832 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-27 10:29:57,835 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-27 10:29:57,839 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-27 10:29:57,842 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-27 10:29:57,848 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-27 10:29:57,851 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-27 10:29:57,854 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-27 10:29:57,856 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-27 10:29:57,858 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-27 10:29:57,860 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-27 10:29:57,862 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-27 10:29:57,864 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:29:57,868 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for wasco
2025-05-27 10:29:57,871 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-27 10:29:57,874 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-27 10:29:57,876 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-27 10:29:57,880 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-27 10:29:57,882 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-27 10:29:57,886 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:29:57,888 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-27 10:29:57,890 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-27 10:29:57,892 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-27 10:29:57,894 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-27 10:29:57,897 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-27 10:29:57,903 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-27 10:29:57,905 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-27 10:29:57,907 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-27 10:29:57,909 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-27 10:29:57,912 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-27 10:29:57,917 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-27 10:29:57,920 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-27 10:29:57,922 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-27 10:29:57,924 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-27 10:29:57,926 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-27 10:29:57,929 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-27 10:29:57,932 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-27 10:29:57,935 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-27 10:29:57,937 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-27 10:29:57,939 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-27 10:29:57,942 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-27 10:29:57,944 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-27 10:29:57,947 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-27 10:29:57,950 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-27 10:29:57,955 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-05-27 10:29:57,957 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-27 10:29:57,960 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
