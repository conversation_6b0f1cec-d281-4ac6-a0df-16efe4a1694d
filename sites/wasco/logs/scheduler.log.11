2025-05-29 17:01:58,801 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:01:58,840 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:01:58,865 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:01:58,917 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-29 17:01:58,954 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:01:58,981 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:01:59,009 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:01:59,022 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:01:59,035 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-29 17:01:59,051 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-29 17:01:59,136 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-29 17:01:59,186 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:01:59,245 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-29 17:01:59,275 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:01:59,286 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:01:59,305 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:01:59,355 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-29 17:01:59,369 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:01:59,378 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-29 17:01:59,408 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:01:59,430 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-29 17:01:59,445 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:01:59,533 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:01:59,542 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-29 17:01:59,556 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:01:59,572 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:01:59,613 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:01:59,652 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:01:59,737 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:01:59,763 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:01:59,773 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-29 17:03:00,940 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-29 17:03:00,964 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:03:00,989 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:03:01,005 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:03:01,028 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:03:01,069 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-29 17:03:01,120 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:03:01,132 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:03:01,165 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-29 17:03:01,197 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:03:01,267 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:03:01,284 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:03:01,294 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:03:01,332 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:03:01,349 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-29 17:03:01,381 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:03:01,401 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:03:01,419 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-29 17:03:01,435 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:03:01,466 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:03:01,475 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-29 17:03:01,519 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:03:01,529 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:03:01,540 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-29 17:03:01,581 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-29 17:03:01,602 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:03:01,643 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:03:01,675 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-29 17:03:01,697 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-29 17:03:01,757 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:03:01,780 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-29 17:03:01,813 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-29 17:03:01,824 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:04:08,709 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:04:08,751 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-29 17:04:08,797 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-29 17:04:08,825 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:04:08,912 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-29 17:04:08,955 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:04:08,991 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-29 17:04:09,013 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:04:09,038 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:04:09,051 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:04:09,060 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:04:09,081 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:04:09,117 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:04:09,133 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:04:09,159 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:04:09,184 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:04:09,206 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-29 17:04:09,242 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:04:09,255 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:04:09,264 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-29 17:04:09,278 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:04:09,287 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:04:09,304 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:04:09,313 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-29 17:04:09,362 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-29 17:04:09,378 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:04:09,393 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-29 17:04:09,451 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:04:09,462 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:04:09,510 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-29 17:04:09,525 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-29 17:04:09,584 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-29 17:04:09,601 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:05:11,910 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:05:11,918 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-29 17:05:11,934 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:05:11,979 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:05:11,988 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:05:12,026 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:05:12,070 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:05:12,077 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:05:12,084 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:05:12,131 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-29 17:05:12,140 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:05:12,159 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-29 17:05:12,193 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:05:12,201 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:05:12,209 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-29 17:05:12,236 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:05:12,285 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-29 17:05:12,293 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-29 17:05:12,307 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:05:12,314 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:05:12,359 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-29 17:05:12,392 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:05:12,400 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-29 17:05:12,434 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-29 17:05:12,463 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-29 17:05:12,509 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-29 17:05:12,518 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:05:12,532 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:05:12,560 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-29 17:05:12,585 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:05:12,598 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:05:12,630 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:05:12,728 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:06:17,221 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:06:17,229 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-29 17:06:17,260 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:06:17,303 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-29 17:06:17,315 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:06:17,383 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-29 17:06:17,444 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:06:17,500 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:06:17,511 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-29 17:06:17,537 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-29 17:06:17,554 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:06:17,571 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-29 17:06:17,603 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:06:17,615 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:06:17,627 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-29 17:06:17,650 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-29 17:06:17,708 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-29 17:06:17,721 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-29 17:06:17,732 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:06:17,741 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:06:17,757 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-29 17:06:17,778 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:06:17,804 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:06:17,850 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:06:17,872 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-29 17:06:17,942 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:06:17,953 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:06:17,972 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:06:17,984 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:06:17,994 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-29 17:06:18,041 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:06:18,062 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-29 17:06:18,107 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:06:18,155 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:06:18,200 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:07:20,090 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-29 17:07:20,217 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-29 17:07:20,235 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-29 17:07:20,344 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-29 17:07:20,358 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-29 17:07:20,372 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-29 17:07:20,395 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-29 17:07:20,411 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-29 17:07:20,447 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-29 17:07:20,472 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-29 17:07:20,497 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-29 17:07:20,527 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-29 17:07:20,549 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-29 17:07:20,594 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-29 17:07:20,630 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-29 17:07:20,643 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-29 17:07:20,676 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-29 17:07:20,704 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-29 17:07:20,771 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-29 17:07:20,820 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-29 17:07:20,832 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-29 17:07:20,861 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-29 17:07:20,940 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-29 17:07:20,973 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-29 17:08:23,159 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-29 17:08:23,215 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-29 17:08:23,224 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-29 17:08:23,302 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-29 17:08:23,524 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:55:46,096 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 11:55:46,110 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 11:55:46,126 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 11:55:46,152 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 11:55:46,160 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 11:55:46,164 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 11:55:46,172 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 11:55:46,181 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 11:55:46,185 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 11:55:46,187 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 11:55:46,193 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:55:46,198 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 11:55:46,201 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 11:55:46,203 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 11:55:46,208 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 11:55:46,210 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 11:55:46,212 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 11:55:46,215 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 11:55:46,217 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:55:46,219 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 11:55:46,221 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 11:55:46,224 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 11:55:46,227 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 11:55:46,231 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 11:55:46,235 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 11:55:46,237 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 11:55:46,240 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:55:46,242 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 11:55:46,244 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 11:55:46,247 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 11:55:46,249 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 11:55:46,251 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 11:55:46,254 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 11:55:46,256 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:55:46,259 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 11:55:46,264 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 11:55:46,268 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 11:55:46,271 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 11:55:46,274 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 11:55:46,276 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 11:55:46,279 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 11:55:46,281 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 11:55:46,284 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 11:55:46,286 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 11:55:46,288 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 11:55:46,291 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:55:46,293 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 11:55:46,296 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 11:55:46,298 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 11:55:46,302 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 11:55:46,309 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 11:55:46,312 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 11:55:46,314 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 11:55:46,316 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 11:55:46,322 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 11:55:46,325 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 11:55:46,329 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 11:55:46,333 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 11:55:46,335 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 11:55:46,338 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 11:55:46,343 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 11:55:46,346 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 11:55:46,349 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 11:55:46,351 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 11:55:46,353 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 11:55:46,356 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 11:55:46,358 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 11:55:46,361 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:55:46,363 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 11:55:46,366 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 11:55:46,369 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 11:55:46,371 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 11:55:46,374 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:55:46,377 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 11:55:46,382 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 11:55:46,385 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:55:46,387 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 11:55:46,389 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 11:55:46,392 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 11:55:46,397 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 11:55:46,399 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 11:55:46,402 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 11:55:46,404 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 11:55:46,409 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 11:55:46,414 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 11:55:46,418 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 11:55:46,420 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:55:46,422 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 11:55:46,425 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 11:55:46,427 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 11:55:46,430 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 11:55:46,432 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 11:55:46,434 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 11:55:46,436 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 11:55:46,439 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 11:55:46,442 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 11:55:46,445 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:55:46,447 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 11:55:46,453 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:55:46,457 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 11:55:46,460 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 11:56:47,670 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 11:56:47,946 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 11:56:47,949 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 11:56:47,956 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 11:56:47,960 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 11:56:47,963 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 11:56:47,967 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 11:56:47,971 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 11:56:47,975 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 11:56:47,982 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 11:56:47,986 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 11:56:47,989 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 11:56:47,994 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 11:56:47,997 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 11:56:48,000 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:56:48,003 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 11:56:48,007 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 11:56:48,010 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 11:56:48,016 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 11:56:48,019 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 11:56:48,023 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 11:56:48,028 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 11:56:48,031 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 11:56:48,033 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 11:56:48,036 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 11:56:48,039 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 11:56:48,044 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 11:56:48,047 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 11:56:48,053 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:56:48,061 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 11:56:48,065 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 11:56:48,068 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 11:56:48,074 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 11:56:48,079 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 11:56:48,082 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 11:56:48,084 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 11:56:48,089 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 11:56:48,096 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:56:48,100 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 11:56:48,102 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 11:56:48,106 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 11:56:48,118 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 11:56:48,126 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:56:48,132 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 11:56:48,140 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:56:48,149 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:56:48,159 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 11:56:48,164 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:56:48,169 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 11:56:48,182 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 11:56:48,189 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 11:56:48,195 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 11:56:48,201 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:56:48,208 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 11:56:48,214 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 11:56:48,219 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 11:56:48,224 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 11:56:48,228 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 11:56:48,233 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 11:56:48,237 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 11:56:48,243 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 11:56:48,247 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 11:56:48,251 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 11:56:48,255 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 11:56:48,259 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 11:56:48,263 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 11:56:48,267 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 11:56:48,271 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 11:56:48,276 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 11:56:48,280 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 11:56:48,283 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:56:48,287 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 11:56:48,290 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 11:56:48,295 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 11:56:48,298 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 11:56:48,304 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 11:56:48,313 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 11:56:48,319 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 11:56:48,325 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 11:56:48,328 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 11:56:48,333 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 11:56:48,337 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 11:56:48,340 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 11:56:48,343 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 11:56:48,345 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 11:56:48,348 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 11:56:48,355 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 11:56:48,360 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:56:48,364 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 11:56:48,367 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 11:56:48,372 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 11:56:48,376 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 11:56:48,381 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 11:56:48,385 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 11:56:48,391 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:56:48,396 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 11:56:48,399 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 11:56:48,401 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 11:56:48,405 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 11:56:48,415 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 11:56:48,422 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 11:57:52,612 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 11:57:52,659 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:57:52,675 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 11:57:52,690 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 11:57:52,695 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 11:57:52,700 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 11:57:52,704 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 11:57:52,710 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 11:57:52,713 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 11:57:52,716 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 11:57:52,719 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 11:57:52,721 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:57:52,723 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 11:57:52,725 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 11:57:52,727 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 11:57:52,729 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 11:57:52,733 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 11:57:52,736 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 11:57:52,738 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 11:57:52,739 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 11:57:52,741 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 11:57:52,743 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 11:57:52,745 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 11:57:52,749 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 11:57:52,752 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 11:57:52,753 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:57:52,757 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 11:57:52,759 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 11:57:52,761 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 11:57:52,763 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 11:57:52,766 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 11:57:52,768 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 11:57:52,770 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 11:57:52,773 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 11:57:52,776 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 11:57:52,778 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 11:57:52,780 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 11:57:52,782 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 11:57:52,784 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 11:57:52,786 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 11:57:52,787 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 11:57:52,789 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 11:57:52,792 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:57:52,793 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 11:57:52,795 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 11:57:52,797 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 11:57:52,799 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:57:52,800 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 11:57:52,803 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 11:57:52,806 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 11:57:52,807 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 11:57:52,809 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 11:57:52,811 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 11:57:52,815 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:57:52,817 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:57:52,819 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 11:57:52,821 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 11:57:52,822 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 11:57:52,824 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 11:57:52,827 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 11:57:52,829 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 11:57:52,833 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 11:57:52,834 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 11:57:52,836 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 11:57:52,838 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 11:57:52,840 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 11:57:52,843 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 11:57:52,845 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 11:57:52,847 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 11:57:52,850 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 11:57:52,852 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 11:57:52,854 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:57:52,855 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 11:57:52,857 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 11:57:52,861 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 11:57:52,864 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 11:57:52,868 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 11:57:52,870 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 11:57:52,872 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 11:57:52,874 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 11:57:52,876 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 11:57:52,878 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 11:57:52,880 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 11:57:52,882 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 11:57:52,884 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 11:57:52,886 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:57:52,888 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 11:57:52,891 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:57:52,892 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 11:57:52,894 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 11:57:52,896 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 11:57:52,898 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 11:57:52,900 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 11:57:52,901 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 11:57:52,903 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 11:57:52,905 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 11:57:52,907 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 11:57:52,909 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:57:52,910 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 11:57:52,913 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 11:57:52,915 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 11:58:54,011 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 11:58:54,012 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 11:58:54,014 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 11:58:54,017 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 11:58:54,019 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 11:58:54,020 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 11:58:54,022 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 11:58:54,024 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 11:58:54,026 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 11:58:54,028 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:58:54,031 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 11:58:54,033 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 11:58:54,035 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 11:58:54,037 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 11:58:54,042 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 11:58:54,045 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 11:58:54,047 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 11:58:54,050 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 11:58:54,052 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:58:54,054 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 11:58:54,056 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 11:58:54,058 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 11:58:54,060 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 11:58:54,062 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 11:58:54,067 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 11:58:54,068 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 11:58:54,071 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 11:58:54,073 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 11:58:54,075 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 11:58:54,077 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:58:54,078 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 11:58:54,080 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 11:58:54,082 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 11:58:54,083 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 11:58:54,085 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:58:54,088 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:58:54,089 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 11:58:54,091 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 11:58:54,093 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 11:58:54,094 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 11:58:54,096 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:58:54,098 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 11:58:54,099 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 11:58:54,101 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 11:58:54,103 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 11:58:54,105 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 11:58:54,107 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 11:58:54,109 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 11:58:54,110 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 11:58:54,112 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 11:58:54,114 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 11:58:54,117 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:58:54,120 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:58:54,122 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 11:58:54,124 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 11:58:54,126 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 11:58:54,129 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 11:58:54,131 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 11:58:54,132 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 11:58:54,134 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 11:58:54,136 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 11:58:54,138 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 11:58:54,140 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 11:58:54,142 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 11:58:54,147 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 11:58:54,149 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 11:58:54,150 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 11:58:54,152 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 11:58:54,154 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 11:58:54,157 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 11:58:54,161 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 11:58:54,163 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 11:58:54,164 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 11:58:54,166 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 11:58:54,168 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 11:58:54,169 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:58:54,172 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 11:58:54,174 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 11:58:54,176 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 11:58:54,177 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 11:58:54,179 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 11:58:54,182 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 11:58:54,184 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 11:58:54,187 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 11:58:54,189 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 11:58:54,192 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 11:58:54,194 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 11:58:54,195 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 11:58:54,197 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 11:58:54,199 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:58:54,201 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 11:58:54,202 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 11:58:54,205 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 11:58:54,207 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 11:58:54,209 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 11:58:54,211 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 11:58:54,213 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 11:58:54,215 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 11:58:54,218 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:58:54,220 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 11:58:54,222 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 11:59:54,374 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 11:59:54,378 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 11:59:54,381 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 11:59:54,385 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 11:59:54,388 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 11:59:54,392 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 11:59:54,395 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 11:59:54,398 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 11:59:54,401 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 11:59:54,405 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 11:59:54,408 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
