2025-05-30 11:59:54,411 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 11:59:55,707 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 11:59:55,734 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 11:59:55,740 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 11:59:55,748 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 11:59:55,752 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 11:59:55,756 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 11:59:55,760 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 11:59:55,764 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 11:59:55,768 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 11:59:55,771 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 11:59:55,773 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 11:59:55,775 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 11:59:55,777 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 11:59:55,780 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 11:59:55,782 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 11:59:55,784 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 11:59:55,834 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:59:55,843 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 11:59:55,860 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 11:59:55,870 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 11:59:55,879 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 11:59:55,899 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:59:55,908 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 11:59:55,918 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 11:59:55,928 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 11:59:55,939 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 11:59:55,948 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 11:59:55,958 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 11:59:55,975 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:59:55,984 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 11:59:55,994 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:59:56,004 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 11:59:56,016 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 11:59:56,028 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 11:59:56,045 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 11:59:56,059 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 11:59:56,075 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 11:59:56,085 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 11:59:56,089 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 11:59:56,093 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 11:59:56,097 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 11:59:56,103 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 11:59:56,111 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 11:59:56,115 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 11:59:56,117 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 11:59:56,120 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 11:59:56,123 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 11:59:56,133 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 11:59:56,139 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 11:59:56,143 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 11:59:56,147 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 11:59:56,150 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 11:59:56,154 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 11:59:56,157 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 11:59:56,160 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 11:59:56,164 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 11:59:56,167 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 11:59:56,170 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 11:59:56,172 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 11:59:56,176 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 11:59:56,179 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 11:59:56,181 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 11:59:56,184 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 11:59:56,188 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 11:59:56,190 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 11:59:56,194 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 11:59:56,197 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 11:59:56,199 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 11:59:56,203 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 11:59:56,205 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 11:59:56,206 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 11:59:56,208 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 11:59:56,210 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 11:59:56,213 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 11:59:56,214 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 11:59:56,217 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 11:59:56,219 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 11:59:56,221 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 11:59:56,223 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 11:59:56,227 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 11:59:56,229 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 11:59:56,232 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 11:59:56,235 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 11:59:56,237 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 11:59:56,239 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 11:59:56,241 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 11:59:56,244 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 11:59:56,246 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 11:59:56,248 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:00:57,461 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 12:00:57,464 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:00:57,467 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 12:00:57,470 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 12:00:57,473 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:00:57,475 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 12:00:57,478 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 12:00:57,480 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 12:00:57,483 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 12:00:57,486 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:00:57,489 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 12:00:57,492 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 12:00:57,494 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:00:57,499 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 12:00:57,502 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 12:00:57,505 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 12:00:57,507 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 12:00:57,509 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 12:00:57,513 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:00:57,520 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 12:00:57,523 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 12:00:57,526 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 12:00:57,529 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 12:00:57,532 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:00:57,536 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 12:00:57,542 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 12:00:57,545 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 12:00:57,548 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 12:00:57,552 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 12:00:57,555 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:00:57,558 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 12:00:57,562 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 12:00:57,565 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 12:00:57,568 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 12:00:57,571 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:00:57,574 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:00:57,577 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 12:00:57,582 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 12:00:57,585 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 12:00:57,590 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:00:57,594 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 12:00:57,599 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 12:00:57,602 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 12:00:57,605 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 12:00:57,608 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 12:00:57,611 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:00:57,614 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:00:57,616 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 12:00:57,620 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 12:00:57,624 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 12:00:57,627 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:00:57,632 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 12:00:57,636 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 12:00:57,639 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 12:00:57,643 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:00:57,645 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:00:57,647 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:00:57,653 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 12:00:57,656 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 12:00:57,658 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 12:00:57,661 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 12:00:57,663 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 12:00:57,665 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:00:57,667 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 12:00:57,670 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:00:57,676 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 12:00:57,678 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 12:00:57,681 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:00:57,683 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:00:57,686 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 12:00:57,688 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:00:57,690 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 12:00:57,692 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:00:57,694 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 12:00:57,696 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 12:00:57,698 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:00:57,701 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 12:00:57,703 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 12:00:57,705 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 12:00:57,707 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 12:00:57,711 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:00:57,713 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 12:00:57,716 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 12:00:57,719 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 12:00:57,721 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 12:00:57,723 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 12:00:57,725 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 12:00:57,727 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 12:00:57,729 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 12:00:57,731 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:00:57,734 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:00:57,738 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 12:00:57,740 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 12:00:57,742 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:00:57,744 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 12:00:57,746 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 12:00:57,748 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 12:00:57,750 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 12:00:57,754 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 12:00:57,756 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 12:00:57,758 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 12:02:01,015 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 12:02:01,018 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 12:02:01,021 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 12:02:01,026 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 12:02:01,028 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:02:01,033 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 12:02:01,036 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 12:02:01,038 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:02:01,043 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 12:02:01,047 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 12:02:01,050 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:02:01,054 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 12:02:01,056 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:02:01,061 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:02:01,063 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 12:02:01,066 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 12:02:01,069 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 12:02:01,074 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:02:01,078 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:02:01,081 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 12:02:01,084 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:02:01,087 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 12:02:01,090 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 12:02:01,093 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 12:02:01,096 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 12:02:01,099 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 12:02:01,102 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:02:01,105 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 12:02:01,109 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 12:02:01,112 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 12:02:01,115 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 12:02:01,121 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 12:02:01,124 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 12:02:01,131 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 12:02:01,134 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 12:02:01,137 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:02:01,140 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 12:02:01,143 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 12:02:01,150 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 12:02:01,154 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 12:02:01,157 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 12:02:01,160 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:02:01,164 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 12:02:01,166 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 12:02:01,169 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:02:01,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 12:02:01,175 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 12:02:01,178 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:02:01,181 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 12:02:01,184 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:02:01,187 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:02:01,190 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 12:02:01,192 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 12:02:01,195 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 12:02:01,198 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:02:01,205 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 12:02:01,209 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:02:01,213 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 12:02:01,216 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 12:02:01,220 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 12:02:01,222 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 12:02:01,226 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 12:02:01,691 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 12:02:01,698 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:02:01,700 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 12:02:01,703 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 12:02:01,706 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 12:02:01,710 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 12:02:01,714 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:02:01,717 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:02:01,720 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 12:02:01,724 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 12:02:01,727 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:02:01,731 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 12:02:01,733 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 12:02:01,735 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:02:01,737 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 12:02:01,740 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:02:01,743 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:02:01,748 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 12:02:01,751 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:02:01,754 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 12:02:01,757 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 12:02:01,760 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 12:02:01,763 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 12:02:01,766 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 12:02:01,768 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 12:02:01,773 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 12:02:01,778 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 12:02:01,781 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 12:02:01,786 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:02:01,790 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 12:02:01,793 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 12:02:01,795 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 12:02:01,798 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 12:02:01,801 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:02:01,806 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 12:02:01,809 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 12:02:01,812 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 12:02:01,816 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 12:02:01,818 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 12:03:02,127 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 12:03:02,474 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for wasco
2025-05-30 12:03:02,611 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for wasco
2025-05-30 12:03:02,736 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:03:02,823 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-05-30 12:03:02,963 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:03:03,102 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 12:03:03,239 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for wasco
2025-05-30 12:03:03,586 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 12:03:03,681 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 12:03:03,855 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:03:04,010 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:03:04,146 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 12:03:04,259 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 12:03:04,281 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:03:04,290 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_shift_assignment_for_active_today because it was found in queue for wasco
2025-05-30 12:03:04,299 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for wasco
2025-05-30 12:03:04,308 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for wasco
2025-05-30 12:03:04,317 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for wasco
2025-05-30 12:03:04,323 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for wasco
2025-05-30 12:03:04,621 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:03:04,761 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:03:04,820 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 12:03:04,847 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for wasco
2025-05-30 12:03:04,907 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:03:04,968 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:03:04,990 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for wasco
2025-05-30 12:03:05,133 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for wasco
2025-05-30 12:03:05,333 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:03:05,468 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:03:05,518 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for wasco
2025-05-30 12:03:05,579 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:03:05,695 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:03:05,819 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 12:03:05,915 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for wasco
2025-05-30 12:03:06,104 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 12:03:06,204 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for wasco
2025-05-30 12:03:06,419 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for wasco
2025-05-30 12:03:06,612 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 12:03:07,091 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 12:03:07,317 ERROR scheduler Skipped queueing payware.payware.utils.generate_additional_salary_records because it was found in queue for wasco
2025-05-30 12:03:07,658 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 12:03:07,663 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 12:03:07,668 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:03:07,672 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for wasco
2025-05-30 12:03:07,677 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for wasco
2025-05-30 12:03:07,681 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 12:03:07,685 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for wasco
2025-05-30 12:03:07,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:03:07,691 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for wasco
2025-05-30 12:03:07,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for wasco
2025-05-30 12:03:07,695 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for wasco
2025-05-30 12:03:07,702 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 12:03:07,704 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 12:03:07,705 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for wasco
2025-05-30 12:03:07,708 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for wasco
2025-05-30 12:03:07,710 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:03:07,711 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for wasco
2025-05-30 12:03:07,713 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for wasco
2025-05-30 12:03:07,715 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 12:03:07,718 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:03:07,719 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for wasco
2025-05-30 12:03:07,721 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:03:07,722 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:03:07,724 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for wasco
2025-05-30 12:03:07,726 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:03:07,728 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for wasco
2025-05-30 12:03:07,729 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 12:03:07,731 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for wasco
2025-05-30 12:03:07,733 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 12:03:07,735 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 12:03:07,736 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 12:03:07,738 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 12:03:07,739 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 12:03:07,741 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for wasco
2025-05-30 12:03:07,743 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 12:03:07,745 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 12:03:07,747 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for wasco
2025-05-30 12:03:07,749 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 12:03:07,751 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 12:03:07,752 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for wasco
2025-05-30 12:03:07,754 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for wasco
2025-05-30 12:03:07,756 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for wasco
2025-05-30 12:03:07,758 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-05-30 12:03:07,760 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 12:03:07,761 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:03:07,763 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for wasco
2025-05-30 12:03:07,766 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for wasco
2025-05-30 12:03:07,769 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 12:03:07,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:03:07,773 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 12:03:07,775 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for wasco
2025-05-30 12:03:07,777 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for wasco
2025-05-30 12:03:07,778 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for wasco
2025-05-30 12:03:07,781 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for wasco
2025-05-30 12:03:07,785 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for wasco
2025-05-30 12:03:07,786 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for wasco
2025-05-30 12:03:07,788 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for wasco
2025-05-30 12:03:07,790 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for wasco
2025-05-30 12:03:07,793 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:03:07,797 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for wasco
2025-05-30 12:04:24,786 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:04:24,846 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:04:24,906 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:04:24,934 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:04:24,953 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:04:24,956 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:04:24,998 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:04:25,010 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:04:25,024 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:04:25,032 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:04:25,040 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:04:25,054 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:04:25,066 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:04:25,101 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:04:25,104 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:04:25,109 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:05:31,990 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:05:32,084 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:05:32,400 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:05:32,778 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:05:32,790 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:05:33,041 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:05:33,052 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:05:33,067 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:05:33,074 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:05:33,087 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:05:33,090 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:05:33,096 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:05:33,112 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:05:33,117 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:05:33,133 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:05:33,138 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:05:33,145 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:05:33,149 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:05:33,321 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:06:50,635 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:06:51,056 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:06:51,871 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:06:52,121 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:06:52,124 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:06:52,136 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:06:52,139 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:06:52,172 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:06:53,664 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:06:53,821 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:06:54,119 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:06:54,132 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:06:54,140 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:06:54,153 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:06:54,156 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:06:54,168 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:06:54,171 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:06:54,177 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:06:54,186 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:07:56,352 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:07:56,358 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:07:56,360 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:07:56,371 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:07:56,377 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:07:56,384 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:07:56,388 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:07:56,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:07:56,414 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:07:56,424 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:07:56,427 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:07:56,429 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:07:56,817 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:07:56,838 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:07:56,841 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:07:57,211 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:07:57,217 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:07:57,224 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:07:57,230 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:08:59,871 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:08:59,882 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:08:59,889 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:08:59,910 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:08:59,912 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:08:59,918 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:08:59,924 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:08:59,946 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:08:59,950 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:08:59,962 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:08:59,973 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:08:59,981 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:08:59,984 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:08:59,989 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:08:59,991 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:08:59,995 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:08:59,999 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:09:00,001 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:09:00,009 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:10:00,243 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:10:00,252 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:10:00,257 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:10:00,262 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:10:00,267 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 12:10:00,275 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:10:00,278 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:10:00,281 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:10:00,290 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:10:00,294 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:10:00,296 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:10:00,300 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:10:00,308 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:10:00,310 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:10:00,319 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:10:00,337 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:10:00,359 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:10:00,364 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:10:00,374 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:10:00,381 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:11:02,881 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:11:02,886 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:11:02,897 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:11:02,901 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:11:02,909 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:11:02,917 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:11:02,920 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 12:11:02,921 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 12:11:02,923 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:11:02,926 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:11:02,936 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:11:02,953 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:11:02,966 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:11:02,970 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:11:02,972 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 12:11:02,983 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:11:02,999 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:11:03,004 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 12:11:03,006 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 12:11:03,012 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:11:03,014 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:12:03,319 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:12:03,520 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:12:03,808 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:12:03,830 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:12:03,865 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:12:03,868 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:12:03,874 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:12:03,889 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 12:12:03,898 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:12:03,907 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:12:03,911 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:12:03,913 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:12:03,918 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:12:03,920 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 12:12:03,930 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:12:03,942 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:12:04,189 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:13:07,214 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:13:07,225 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-05-30 12:13:07,275 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-05-30 12:13:07,298 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-05-30 12:13:07,347 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-05-30 12:13:07,377 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-05-30 12:13:07,387 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-05-30 12:13:07,393 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-05-30 12:13:07,398 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-05-30 12:13:07,410 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-05-30 12:13:07,422 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-05-30 12:13:07,426 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-05-30 12:13:07,431 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-05-30 12:13:07,434 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-05-30 12:13:07,457 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-05-30 12:13:07,466 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-05-30 13:01:51,358 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-05-30 13:01:51,360 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-05-30 13:01:51,364 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-05-30 13:01:51,377 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-05-30 13:01:51,383 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 13:01:51,407 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-05-30 13:01:51,419 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-05-30 13:01:51,430 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-05-30 13:01:51,433 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 13:01:51,445 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-05-30 13:01:51,458 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 13:01:51,464 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 13:01:51,469 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 13:01:51,472 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-05-30 13:01:51,494 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 13:01:51,496 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 13:01:51,500 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-05-30 13:01:51,502 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-05-30 13:01:51,504 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-05-30 13:01:51,506 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-05-30 13:01:51,514 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-05-30 13:01:51,516 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 13:01:51,520 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 13:01:51,522 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 13:01:51,524 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 13:01:51,526 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 13:01:51,531 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-05-30 13:01:51,547 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-05-30 13:01:51,552 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-05-30 13:01:51,553 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-05-30 13:01:51,556 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 13:01:51,560 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-05-30 13:01:51,564 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-05-30 13:02:52,110 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-05-30 13:02:52,114 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-05-30 13:02:52,116 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-05-30 13:02:52,117 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-05-30 13:02:52,120 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-05-30 13:02:52,127 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-05-30 13:02:52,131 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-05-30 13:02:52,132 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-05-30 13:02:52,138 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for wasco
2025-05-30 13:02:52,139 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-05-30 13:02:52,141 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-05-30 13:02:52,162 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-05-30 13:02:52,164 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-05-30 13:02:52,178 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
