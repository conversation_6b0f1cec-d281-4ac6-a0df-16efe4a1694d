2025-07-22 10:03:20,883 ERROR frappe Could not take error snapshot: '{' was never closed (hooks.py, line 142)
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:03:20,894 ERROR frappe Failed to run after request hook
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,099 ERROR frappe Could not take error snapshot: '{' was never closed (hooks.py, line 142)
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'fields': '["`tabWorkflow Transition History`.`name`","`tabWorkflow Transition History`.`owner`","`tabWorkflow Transition History`.`creation`","`tabWorkflow Transition History`.`modified`","`tabWorkflow Transition History`.`modified_by`","`tabWorkflow Transition History`.`_user_tags`","`tabWorkflow Transition History`.`_comments`","`tabWorkflow Transition History`.`_assign`","`tabWorkflow Transition History`.`_liked_by`","`tabWorkflow Transition History`.`docstatus`","`tabWorkflow Transition History`.`idx`","`tabWorkflow Transition History`.`user`","`tabWorkflow Transition History`.`previous_state`","`tabWorkflow Transition History`.`current_state`","`tabWorkflow Transition History`.`transition_date`","`tabWorkflow Transition History`.`reference_name`"]', 'filters': '[]', 'order_by': '`tabWorkflow Transition History`.`transition_date` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,099 ERROR frappe Could not take error snapshot: '{' was never closed (hooks.py, line 142)
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'fields': '["`tabWorkflow Transition History`.`name`","`tabWorkflow Transition History`.`owner`","`tabWorkflow Transition History`.`creation`","`tabWorkflow Transition History`.`modified`","`tabWorkflow Transition History`.`modified_by`","`tabWorkflow Transition History`.`_user_tags`","`tabWorkflow Transition History`.`_comments`","`tabWorkflow Transition History`.`_assign`","`tabWorkflow Transition History`.`_liked_by`","`tabWorkflow Transition History`.`docstatus`","`tabWorkflow Transition History`.`idx`","`tabWorkflow Transition History`.`user`","`tabWorkflow Transition History`.`previous_state`","`tabWorkflow Transition History`.`current_state`","`tabWorkflow Transition History`.`transition_date`","`tabWorkflow Transition History`.`reference_name`"]', 'filters': '[]', 'order_by': '`tabWorkflow Transition History`.`transition_date` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,101 ERROR frappe Could not take error snapshot: '{' was never closed (hooks.py, line 142)
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,101 ERROR frappe Could not take error snapshot: '{' was never closed (hooks.py, line 142)
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,116 ERROR frappe Failed to run after request hook
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'fields': '["`tabWorkflow Transition History`.`name`","`tabWorkflow Transition History`.`owner`","`tabWorkflow Transition History`.`creation`","`tabWorkflow Transition History`.`modified`","`tabWorkflow Transition History`.`modified_by`","`tabWorkflow Transition History`.`_user_tags`","`tabWorkflow Transition History`.`_comments`","`tabWorkflow Transition History`.`_assign`","`tabWorkflow Transition History`.`_liked_by`","`tabWorkflow Transition History`.`docstatus`","`tabWorkflow Transition History`.`idx`","`tabWorkflow Transition History`.`user`","`tabWorkflow Transition History`.`previous_state`","`tabWorkflow Transition History`.`current_state`","`tabWorkflow Transition History`.`transition_date`","`tabWorkflow Transition History`.`reference_name`"]', 'filters': '[]', 'order_by': '`tabWorkflow Transition History`.`transition_date` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,116 ERROR frappe Failed to run after request hook
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'fields': '["`tabWorkflow Transition History`.`name`","`tabWorkflow Transition History`.`owner`","`tabWorkflow Transition History`.`creation`","`tabWorkflow Transition History`.`modified`","`tabWorkflow Transition History`.`modified_by`","`tabWorkflow Transition History`.`_user_tags`","`tabWorkflow Transition History`.`_comments`","`tabWorkflow Transition History`.`_assign`","`tabWorkflow Transition History`.`_liked_by`","`tabWorkflow Transition History`.`docstatus`","`tabWorkflow Transition History`.`idx`","`tabWorkflow Transition History`.`user`","`tabWorkflow Transition History`.`previous_state`","`tabWorkflow Transition History`.`current_state`","`tabWorkflow Transition History`.`transition_date`","`tabWorkflow Transition History`.`reference_name`"]', 'filters': '[]', 'order_by': '`tabWorkflow Transition History`.`transition_date` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,123 ERROR frappe Failed to run after request hook
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:08:24,123 ERROR frappe Failed to run after request hook
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'fields': '["`tabPurchase Invoice`.`workflow_state`","`tabPurchase Invoice`.`name`","`tabPurchase Invoice`.`owner`","`tabPurchase Invoice`.`creation`","`tabPurchase Invoice`.`modified`","`tabPurchase Invoice`.`modified_by`","`tabPurchase Invoice`.`_user_tags`","`tabPurchase Invoice`.`_comments`","`tabPurchase Invoice`.`_assign`","`tabPurchase Invoice`.`_liked_by`","`tabPurchase Invoice`.`docstatus`","`tabPurchase Invoice`.`idx`","`tabPurchase Invoice`.`title`","`tabPurchase Invoice`.`posting_date`","`tabPurchase Invoice`.`invoice_type`","`tabPurchase Invoice`.`total`","`tabPurchase Invoice`.`net_total`","`tabPurchase Invoice`.`tax_withholding_net_total`","`tabPurchase Invoice`.`taxes_and_charges_added`","`tabPurchase Invoice`.`taxes_and_charges_deducted`","`tabPurchase Invoice`.`total_taxes_and_charges`","`tabPurchase Invoice`.`grand_total`","`tabPurchase Invoice`.`rounding_adjustment`","`tabPurchase Invoice`.`rounded_total`","`tabPurchase Invoice`.`total_advance`","`tabPurchase Invoice`.`outstanding_amount`","`tabPurchase Invoice`.`discount_amount`","`tabPurchase Invoice`.`paid_amount`","`tabPurchase Invoice`.`write_off_amount`","`tabPurchase Invoice`.`status`","`tabPurchase Invoice`.`supplier`","`tabPurchase Invoice`.`supplier_name`","`tabPurchase Invoice`.`base_grand_total`","`tabPurchase Invoice`.`due_date`","`tabPurchase Invoice`.`company`","`tabPurchase Invoice`.`currency`","`tabPurchase Invoice`.`is_return`","`tabPurchase Invoice`.`release_date`","`tabPurchase Invoice`.`on_hold`","`tabPurchase Invoice`.`represents_company`","`tabPurchase Invoice`.`is_internal_supplier`","`tabPurchase Invoice`.`project`","`tabPurchase Invoice`.`party_account_currency`","project.project_name as project_project_name"]', 'filters': '[]', 'order_by': '`tabPurchase Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/hooks.py", line 142
    doc_events = {
                 ^
SyntaxError: '{' was never closed
2025-07-22 10:52:34,083 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'cmd': 'csf_tz.csf_tz.doctype.workflow_transition_history.workflow_transition_history.get_workflow_history'}
2025-07-22 11:06:57,072 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doctype': 'Purchase Invoice', 'name': 'ACC-PINV-07-2025-00027', 'cmd': 'frappe.client.delete'}
2025-07-22 11:46:56,689 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22"}', 'cmd': 'csf_tz.csf_tz.report.workflow_turnaround_analysis_report.dashboard_helper.get_dashboard_data'}
2025-07-22 11:50:37,919 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22"}', 'cmd': 'csf_tz.csf_tz.report.workflow_turnaround_analysis_report.dashboard_helper.get_dashboard_data'}
2025-07-22 12:36:40,455 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Transition Log"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:36:53,172 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Transition Log"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:36:59,490 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:04,454 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:28,225 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:32,932 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:33,275 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Frequency Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:36,456 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Frequency Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:37:44,667 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Transition Log"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:38:08,396 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Transition Log"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:38:28,459 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:38:42,068 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:39:01,744 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:39:48,869 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:39:53,987 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:39:54,415 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Transition Comments Report"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:52:42,678 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 12:52:46,689 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-22 14:57:34,599 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-22","to_date":"2025-07-22","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-23 09:03:23,677 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doc': '{"docstatus":0,"doctype":"Purchase Invoice","name":"new-purchase-invoice-smlilwfyuu","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"ACC-PINV-.MM.-.YYYY.-","company":"NEELKANTH CHEMICALS LIMITED","posting_date":"2025-08-11","set_posting_time":1,"is_paid":1,"is_return":0,"update_outstanding_for_self":1,"update_billed_amount_in_purchase_order":0,"update_billed_amount_in_purchase_receipt":1,"apply_tds":0,"currency":"TZS","use_transaction_date_exchange_rate":0,"buying_price_list":"Standard Buying","price_list_currency":"TZS","ignore_pricing_rule":0,"update_stock":0,"is_subcontracted":0,"items":[{"docstatus":0,"doctype":"Purchase Invoice Item","name":"new-purchase-invoice-item-dlxqexyhxn","__islocal":1,"__unsaved":1,"owner":"Administrator","qty":1,"conversion_factor":1,"stock_uom":"NOS","margin_type":"","is_free_item":0,"apply_tds":0,"use_serial_batch_fields":0,"is_fixed_asset":0,"enable_deferred_expense":0,"allow_zero_valuation_rate":0,"include_exploded_items":0,"page_break":0,"parent":"new-purchase-invoice-smlilwfyuu","parentfield":"items","parenttype":"Purchase Invoice","idx":1,"item_code":"LABOUR CHARGE","item_name":"SUPER FINE MILL FOUNDATION LABOUR CHARGE","description":"<div class=\\"ql-editor read-mode\\"><p>SUPER FINE MILL FOUNDATION LABOUR CHARGE</p></div>","item_group":"TEMPORARY","image":"","received_qty":0,"rejected_qty":0,"uom":"NOS","stock_qty":1,"price_list_rate":600000,"base_price_list_rate":600000,"margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":1.666667,"discount_amount":10000,"distributed_discount_amount":0,"base_rate_with_margin":0,"rate":590000,"amount":590000,"withholding_tax_rate":0,"base_rate":590000,"base_amount":590000,"stock_uom_rate":590000,"net_rate":590000,"net_amount":590000,"base_net_rate":590000,"base_net_amount":590000,"valuation_rate":0,"sales_incoming_rate":0,"item_tax_amount":0,"landed_cost_voucher_amount":0,"rm_supp_cost":0,"expense_account":"LABOUR CAHRGE - NCL","item_tax_rate":"{}","purchase_order":"NCL-2024-10-00024","po_detail":"k8o7fhja4t","weight_per_unit":0,"total_weight":0,"project":"PROJ-0067","cost_center":"Main - NCL","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"_amended_from":"e7a0t3bhqm","has_margin":false,"child_docname":"new-purchase-invoice-item-dlxqexyhxn"}],"tax_withholding_net_total":0,"taxes":[],"use_company_roundoff_cost_center":0,"disable_rounded_total":0,"apply_discount_on":"Grand Total","tax_withheld_vouchers":[],"pricing_rules":[],"supplied_items":[],"mode_of_payment":"Cash","allocate_advances_automatically":0,"only_include_allocated_payments":0,"advances":[],"advance_tax":[],"ignore_default_payment_terms_template":0,"payment_schedule":[{"docstatus":0,"doctype":"Payment Schedule","name":"new-payment-schedule-deznttrhil","__islocal":1,"__unsaved":1,"owner":"Administrator","discount_type":"Percentage","discounted_amount":0,"parent":"new-purchase-invoice-smlilwfyuu","parentfield":"payment_schedule","parenttype":"Purchase Invoice","idx":1,"due_date":"2025-08-11","invoice_portion":100,"discount":0,"payment_amount":590000,"outstanding":590000,"paid_amount":0,"base_payment_amount":590000,"base_outstanding":590000,"base_paid_amount":0,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"_amended_from":"6fofc5lh0h"}],"status":"Cancelled","party_account_currency":"TZS","is_opening":"No","letter_head":"NCL","group_same_items":0,"on_hold":0,"is_internal_supplier":0,"represents_company":"","is_old_subcontracting_flow":0,"__run_link_triggers":false,"workflow_state":"Draft","title":"VICTOR","supplier":"VICTOR","supplier_name":"VICTOR","posting_time":"19:58:24.484898","due_date":"2025-08-11","invoice_type":"Project","project":"PROJ-0067","conversion_rate":1,"plc_conversion_rate":1,"total_qty":1,"total_net_weight":0,"base_total":590000,"base_net_total":590000,"total":590000,"net_total":590000,"base_tax_withholding_net_total":0,"tax_category":"","base_taxes_and_charges_added":0,"base_taxes_and_charges_deducted":0,"base_total_taxes_and_charges":0,"taxes_and_charges_added":0,"taxes_and_charges_deducted":0,"total_taxes_and_charges":0,"base_grand_total":590000,"base_rounding_adjustment":0,"base_rounded_total":590000,"base_in_words":"","grand_total":590000,"rounding_adjustment":0,"rounded_total":590000,"in_words":"","total_advance":0,"outstanding_amount":0,"base_discount_amount":0,"additional_discount_percentage":0,"discount_amount":0,"base_paid_amount":590000,"cash_bank_account":"Cash - NCL","paid_amount":590000,"write_off_amount":0,"base_write_off_amount":0,"shipping_address":"NEELKANTH CHEMICALS LIMITED-Billing","shipping_address_display":"Neelkanth Chemicals Limited<br>Tanga<br>\\nTanzania<br>\\n","billing_address":"NEELKANTH CHEMICALS LIMITED-Billing","billing_address_display":"Neelkanth Chemicals Limited<br>Tanga<br>\\nTanzania<br>\\n","payment_terms_template":"","per_received":0,"credit_to":"Creditors - NCL","against_expense_account":"LABOUR CAHRGE - NCL","language":"en-GB","supplier_group":"PROJECT","remarks":"No Remarks","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null,"amended_from":"ACC-PINV-07-2025-00021","supplier_address":null,"address_display":null,"dispatch_address":null,"change_amount":0,"base_change_amount":0,"dispatch_address_display":null,"contact_person":null,"contact_display":null,"contact_email":null,"contact_mobile":null,"tax_withholding_category":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-24 14:10:56,962 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24"}', 'file_format_type': 'Excel', 'cmd': 'frappe.desk.query_report.export_query'}
2025-07-24 14:15:44,243 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:16:04,202 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:25:15,569 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:26:02,933 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:12,851 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:34,812 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:35,168 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-31 09:57:37,248 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doc': '{"docstatus":0,"doctype":"Leave Encashment","name":"new-leave-encashment-yvvrshnylf","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"NEELKANTH CHEMICALS LIMITED","status":"Paid","encashment_date":"2024-12-31","currency":"TZS","paid_amount":0,"pay_via_payment_entry":0,"posting_date":"2025-07-31","__run_link_triggers":false,"leave_period":"HR-LPR-2024-00001","employee":"HR-EMP-00126","employee_name":"HENDRY JOSEPH MLANGWA","department":"ELECTRICAL - NCL","leave_type":"Annual Leave","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null,"workflow_state":"Draft","encashment_amount":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-31 10:02:03,644 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doc': '{"docstatus":0,"doctype":"Leave Encashment","name":"new-leave-encashment-gredumpbec","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"NEELKANTH CHEMICALS LIMITED","status":"Paid","encashment_date":"2024-12-31","currency":"TZS","paid_amount":0,"pay_via_payment_entry":0,"posting_date":"2025-07-31","__run_link_triggers":false,"leave_period":"HR-LPR-2024-00001","employee":"HR-EMP-00126","employee_name":"HENDRY JOSEPH MLANGWA","department":"ELECTRICAL - NCL","leave_type":"Annual Leave","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null,"workflow_state":"Draft","encashment_amount":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-08-04 11:11:20,517 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-08-04 11:20:06,321 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:34:27,930 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Bottleneck Analysis', 'filters': '{"threshold_percentage":50,"from_date":"2025-07-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:34:47,816 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Bottleneck Analysis', 'filters': '{"threshold_percentage":50,"from_date":"2025-07-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:41:25,749 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Completion Summary', 'filters': '{"status":"Completed","from_date":"2025-05-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:44:18,858 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Transition Frequency Analysis', 'filters': '{"from_date":"2025-05-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
