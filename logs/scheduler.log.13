2025-08-04 10:05:36,704 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 10:05:36,721 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for masumin
2025-08-04 10:05:36,722 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 10:05:36,726 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 10:05:36,733 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for masumin
2025-08-04 10:05:36,738 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for masumin
2025-08-04 10:05:36,739 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 10:05:36,741 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for masumin
2025-08-04 10:05:36,742 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for masumin
2025-08-04 10:05:36,745 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for masumin
2025-08-04 10:05:36,748 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for masumin
2025-08-04 10:05:36,752 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for masumin
2025-08-04 10:05:36,755 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 10:05:36,758 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 10:05:36,761 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for masumin
2025-08-04 10:05:36,763 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for masumin
2025-08-04 10:05:36,769 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for masumin
2025-08-04 10:05:36,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for masumin
2025-08-04 10:05:36,771 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:06:38,172 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for masumin
2025-08-04 10:06:38,177 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for masumin
2025-08-04 10:06:38,178 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for masumin
2025-08-04 10:06:38,180 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 10:06:38,184 ERROR scheduler Skipped queueing teststythgj_cron because it was found in queue for masumin
2025-08-04 10:06:38,187 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for masumin
2025-08-04 10:06:38,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:06:38,193 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 10:06:38,194 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for masumin
2025-08-04 10:06:38,195 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for masumin
2025-08-04 10:06:38,199 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for masumin
2025-08-04 10:06:38,201 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 10:06:38,203 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 10:06:38,204 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:06:38,205 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for masumin
2025-08-04 10:06:38,208 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 10:06:38,215 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for masumin
2025-08-04 10:06:38,217 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 10:06:38,218 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for masumin
2025-08-04 10:06:38,219 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for masumin
2025-08-04 10:06:38,220 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for masumin
2025-08-04 10:06:38,228 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for masumin
2025-08-04 10:06:38,231 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for masumin
2025-08-04 10:06:38,234 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for masumin
2025-08-04 10:06:38,236 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for masumin
2025-08-04 10:06:38,238 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for masumin
2025-08-04 10:06:38,239 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for masumin
2025-08-04 10:06:38,242 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for masumin
2025-08-04 10:06:38,244 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for masumin
2025-08-04 10:06:38,247 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 10:06:38,249 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for masumin
2025-08-04 10:06:38,250 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 10:06:38,253 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for masumin
2025-08-04 10:06:38,255 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 10:06:38,258 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 10:06:38,262 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for masumin
2025-08-04 10:06:38,264 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 10:06:38,265 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 10:06:38,266 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:06:38,268 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for masumin
2025-08-04 10:06:38,271 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 10:06:38,274 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:07:38,287 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 10:07:38,290 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for masumin
2025-08-04 10:07:38,293 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for masumin
2025-08-04 10:07:38,294 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 10:07:38,300 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for masumin
2025-08-04 10:07:38,305 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for masumin
2025-08-04 10:07:38,308 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for masumin
2025-08-04 10:07:38,310 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 10:07:38,319 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:07:38,320 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for masumin
2025-08-04 10:07:38,322 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 10:07:38,323 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for masumin
2025-08-04 10:07:38,328 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for masumin
2025-08-04 10:07:38,331 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for masumin
2025-08-04 10:07:38,332 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for masumin
2025-08-04 10:07:38,337 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for masumin
2025-08-04 10:07:38,338 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for masumin
2025-08-04 10:07:38,340 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 10:07:38,342 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 10:07:38,344 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 10:07:38,347 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for masumin
2025-08-04 10:07:38,349 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for masumin
2025-08-04 10:07:38,351 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:07:38,353 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:07:38,354 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for masumin
2025-08-04 10:07:38,358 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 10:07:38,359 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 10:07:38,360 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for masumin
2025-08-04 10:07:38,362 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:07:38,364 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 10:07:38,367 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for masumin
2025-08-04 10:07:38,370 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 10:07:38,372 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 10:07:38,375 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for masumin
2025-08-04 10:07:38,381 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for masumin
2025-08-04 10:07:38,384 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for masumin
2025-08-04 10:07:38,391 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for masumin
2025-08-04 10:07:38,392 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for masumin
2025-08-04 10:07:38,394 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 10:08:39,830 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for masumin
2025-08-04 10:08:39,836 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for masumin
2025-08-04 10:08:39,841 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:08:39,851 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for masumin
2025-08-04 10:08:39,852 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for masumin
2025-08-04 10:08:39,856 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for masumin
2025-08-04 10:08:39,864 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for masumin
2025-08-04 10:08:39,867 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 10:08:39,870 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for masumin
2025-08-04 10:08:39,888 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for masumin
2025-08-04 10:08:39,889 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for masumin
2025-08-04 10:08:39,895 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 10:08:39,897 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:08:39,899 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 10:08:39,905 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for masumin
2025-08-04 10:08:39,908 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for masumin
2025-08-04 10:08:39,911 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 10:08:39,913 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 10:08:39,916 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 10:08:39,920 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for masumin
2025-08-04 10:08:39,922 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 10:08:39,923 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 10:08:39,926 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 10:08:39,932 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 10:08:39,942 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for masumin
2025-08-04 10:08:39,948 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for masumin
2025-08-04 10:08:39,952 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for masumin
2025-08-04 10:08:39,956 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for masumin
2025-08-04 10:08:39,959 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for masumin
2025-08-04 10:08:39,963 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for masumin
2025-08-04 10:08:39,964 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for masumin
2025-08-04 10:08:39,967 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for masumin
2025-08-04 10:08:39,969 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 10:08:39,977 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for masumin
2025-08-04 10:08:39,982 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 10:08:39,987 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 10:08:39,989 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for masumin
2025-08-04 10:08:39,991 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:08:39,999 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:09:40,840 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for masumin
2025-08-04 10:09:40,847 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 10:09:40,849 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:09:40,854 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 10:09:40,857 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for masumin
2025-08-04 10:09:40,860 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for masumin
2025-08-04 10:09:40,864 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 10:09:40,867 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for masumin
2025-08-04 10:09:40,869 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 10:09:40,870 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for masumin
2025-08-04 10:09:40,872 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 10:09:40,873 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for masumin
2025-08-04 10:09:40,875 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for masumin
2025-08-04 10:09:40,878 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for masumin
2025-08-04 10:09:40,881 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for masumin
2025-08-04 10:09:40,883 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 10:09:40,884 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for masumin
2025-08-04 10:09:40,888 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 10:09:40,889 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for masumin
2025-08-04 10:09:40,891 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for masumin
2025-08-04 10:09:40,893 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for masumin
2025-08-04 10:09:40,894 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for masumin
2025-08-04 10:09:40,896 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for masumin
2025-08-04 10:09:40,899 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for masumin
2025-08-04 10:09:40,901 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for masumin
2025-08-04 10:09:40,903 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for masumin
2025-08-04 10:09:40,905 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 10:09:40,906 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 10:09:40,907 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:09:40,908 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 10:09:40,910 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for masumin
2025-08-04 10:09:40,911 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 10:09:40,913 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for masumin
2025-08-04 10:09:40,914 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 10:09:40,915 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for masumin
2025-08-04 10:09:40,916 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for masumin
2025-08-04 10:09:40,917 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for masumin
2025-08-04 10:09:40,918 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for masumin
2025-08-04 10:09:40,922 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for masumin
2025-08-04 10:09:40,926 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 10:09:40,927 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for masumin
2025-08-04 10:09:40,936 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for masumin
2025-08-04 10:09:40,941 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for masumin
2025-08-04 10:09:40,943 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for masumin
2025-08-04 11:01:20,281 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 11:01:20,295 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 11:01:20,298 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 11:01:20,303 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 11:01:20,307 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 11:01:20,314 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 11:01:20,342 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 11:01:20,354 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 11:01:20,366 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 11:01:20,383 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 11:01:20,390 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 11:01:20,399 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 11:01:20,400 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
