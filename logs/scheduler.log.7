2025-08-04 10:05:37,408 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for attendance
2025-08-04 10:05:37,410 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-08-04 10:05:37,412 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 10:05:37,414 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 10:05:37,417 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for attendance
2025-08-04 10:05:37,418 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 10:05:37,420 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for attendance
2025-08-04 10:05:37,421 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for attendance
2025-08-04 10:05:37,422 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-08-04 10:05:37,423 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-08-04 10:05:37,425 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-08-04 10:05:37,426 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-08-04 10:05:37,427 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for attendance
2025-08-04 10:05:37,428 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for attendance
2025-08-04 10:05:37,430 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-08-04 10:05:37,431 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-08-04 10:05:37,433 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 10:05:37,434 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for attendance
2025-08-04 10:05:37,435 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for attendance
2025-08-04 10:05:37,437 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-08-04 10:05:37,438 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.send_exceptional_report because it was found in queue for attendance
2025-08-04 10:05:37,439 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for attendance
2025-08-04 10:05:37,440 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-08-04 10:05:37,442 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-08-04 10:05:37,443 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-08-04 10:05:37,444 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for attendance
2025-08-04 10:05:37,446 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-08-04 10:05:37,447 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for attendance
2025-08-04 10:05:37,450 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-08-04 10:05:37,451 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for attendance
2025-08-04 10:05:37,453 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 10:05:37,455 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for attendance
2025-08-04 10:05:37,456 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for attendance
2025-08-04 10:05:37,458 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-08-04 10:05:37,460 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for attendance
2025-08-04 10:05:37,462 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-08-04 10:05:37,463 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for attendance
2025-08-04 10:05:37,465 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for attendance
2025-08-04 10:05:37,467 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for attendance
2025-08-04 10:05:37,469 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-08-04 10:05:37,470 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 10:05:37,472 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for attendance
2025-08-04 10:05:37,474 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for attendance
2025-08-04 10:05:37,475 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 10:05:37,476 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-08-04 10:05:37,478 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 10:05:37,479 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for attendance
2025-08-04 10:05:37,481 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-08-04 10:05:37,483 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 10:05:37,484 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for attendance
2025-08-04 10:05:37,485 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-08-04 10:05:37,486 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for attendance
2025-08-04 10:05:37,488 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-08-04 10:05:37,489 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for attendance
2025-08-04 10:05:37,490 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-08-04 10:05:37,492 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-08-04 10:05:37,493 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-08-04 10:05:37,494 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:05:37,496 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-08-04 10:05:37,498 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-08-04 10:05:37,499 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-08-04 10:05:37,501 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-08-04 10:05:37,503 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-08-04 10:05:37,504 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:05:37,506 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-08-04 10:05:37,507 ERROR scheduler Skipped queueing csf_tz.custom_api.make_stock_reconciliation_for_all_pending_material_request because it was found in queue for attendance
2025-08-04 10:05:37,512 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-08-04 10:05:37,513 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for attendance
2025-08-04 10:05:37,516 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for attendance
2025-08-04 10:05:37,518 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for attendance
2025-08-04 10:05:37,519 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-08-04 10:05:37,521 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for attendance
2025-08-04 10:05:37,522 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for attendance
2025-08-04 10:05:37,523 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-08-04 10:05:37,525 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for attendance
2025-08-04 10:05:37,527 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-08-04 10:05:37,529 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 10:05:37,530 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:05:37,533 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-08-04 10:05:37,535 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for attendance
2025-08-04 10:05:37,536 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for attendance
2025-08-04 10:05:37,538 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for attendance
2025-08-04 10:05:37,540 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for attendance
2025-08-04 10:05:37,542 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-08-04 10:05:37,544 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for attendance
2025-08-04 10:05:37,545 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:05:37,546 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 10:05:37,548 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-08-04 10:05:37,551 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_weekly because it was found in queue for attendance
2025-08-04 10:05:37,553 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for attendance
2025-08-04 10:05:37,554 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-08-04 10:05:37,556 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for attendance
2025-08-04 10:05:37,557 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-08-04 10:05:37,558 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-08-04 10:05:37,559 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for attendance
2025-08-04 10:05:37,561 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 10:05:37,562 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-08-04 10:05:37,563 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-08-04 10:05:37,565 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for attendance
2025-08-04 10:05:37,566 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-08-04 10:05:37,568 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-08-04 10:05:37,570 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 10:05:37,571 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for attendance
2025-08-04 10:05:37,573 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-08-04 10:05:37,574 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for attendance
2025-08-04 10:05:37,575 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-08-04 10:06:37,777 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-08-04 10:06:37,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:06:37,782 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 10:06:37,785 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-08-04 10:06:37,786 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 10:06:37,791 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-08-04 10:06:37,792 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 10:06:37,797 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-08-04 10:06:37,799 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-08-04 10:06:37,800 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-08-04 10:06:37,802 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-08-04 10:06:37,806 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-08-04 10:06:37,807 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 10:06:37,810 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 10:06:37,813 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-08-04 10:06:37,816 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-08-04 10:06:37,818 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-08-04 10:06:37,821 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 10:06:37,826 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-08-04 10:06:37,828 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-08-04 10:06:37,830 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 10:06:37,834 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 10:06:37,836 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:06:37,837 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:06:37,840 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-08-04 10:06:37,841 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 10:06:37,844 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-08-04 10:06:37,845 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 10:06:37,848 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 10:06:37,849 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-08-04 10:06:37,851 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 10:06:37,852 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 10:06:37,856 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:06:37,857 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-08-04 10:06:37,859 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-08-04 10:06:37,860 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-08-04 10:06:37,862 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-08-04 10:06:37,865 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-08-04 10:06:37,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-08-04 10:07:38,516 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-08-04 10:07:38,518 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-08-04 10:07:38,519 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 10:07:38,522 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 10:07:38,530 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 10:07:38,533 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-08-04 10:07:38,538 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:07:38,539 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-08-04 10:07:38,542 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-08-04 10:07:38,543 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-08-04 10:07:38,544 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-08-04 10:07:38,545 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-08-04 10:07:38,547 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-08-04 10:07:38,549 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-08-04 10:07:38,551 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:07:38,553 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 10:07:38,560 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-08-04 10:07:38,561 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-08-04 10:07:38,562 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 10:07:38,564 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 10:07:38,570 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 10:07:38,574 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 10:07:38,580 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-08-04 10:07:38,581 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 10:07:38,582 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-08-04 10:07:38,585 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:07:38,587 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-08-04 10:07:38,589 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-08-04 10:07:38,590 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-08-04 10:07:38,593 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-08-04 10:07:38,594 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:07:38,595 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 10:07:38,597 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-08-04 10:07:38,600 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-08-04 10:07:38,608 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 10:07:38,609 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 10:07:38,611 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-08-04 10:07:38,614 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-08-04 10:07:38,617 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 10:08:40,034 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 10:08:40,037 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:08:40,040 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-08-04 10:08:40,048 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 10:08:40,052 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-08-04 10:08:40,055 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-08-04 10:08:40,058 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 10:08:40,070 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-08-04 10:08:40,073 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 10:08:40,079 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-08-04 10:08:40,086 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-08-04 10:08:40,091 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 10:08:40,097 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-08-04 10:08:40,101 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-08-04 10:08:40,103 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 10:08:40,109 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-08-04 10:08:40,118 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-08-04 10:08:40,120 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 10:08:40,136 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-08-04 10:08:40,144 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-08-04 10:08:40,149 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 10:08:40,155 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-08-04 10:08:40,164 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-08-04 10:08:40,166 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 10:08:40,168 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-08-04 10:08:40,172 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-08-04 10:08:40,174 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-08-04 10:08:40,180 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:08:40,188 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-08-04 10:08:40,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:08:40,192 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-08-04 10:08:40,195 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 10:08:40,204 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-08-04 10:08:40,208 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:08:40,212 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-08-04 10:08:40,222 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 10:08:40,224 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-08-04 10:08:40,226 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 10:08:40,233 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 10:09:40,733 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 10:09:40,734 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 10:09:40,735 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 10:09:40,737 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for attendance
2025-08-04 10:09:40,738 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for attendance
2025-08-04 10:09:40,740 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 10:09:40,743 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for attendance
2025-08-04 10:09:40,746 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 10:09:40,748 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for attendance
2025-08-04 10:09:40,749 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-08-04 10:09:40,750 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for attendance
2025-08-04 10:09:40,756 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for attendance
2025-08-04 10:09:40,758 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for attendance
2025-08-04 10:09:40,760 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for attendance
2025-08-04 10:09:40,762 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-08-04 10:09:40,767 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for attendance
2025-08-04 10:09:40,769 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for attendance
2025-08-04 10:09:40,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for attendance
2025-08-04 10:09:40,771 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for attendance
2025-08-04 10:09:40,775 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for attendance
2025-08-04 10:09:40,780 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for attendance
2025-08-04 10:09:40,781 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 10:09:40,782 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:09:40,784 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 10:09:40,787 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for attendance
2025-08-04 10:09:40,789 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:09:40,790 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 10:09:40,794 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for attendance
2025-08-04 10:09:40,796 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for attendance
2025-08-04 10:09:40,798 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for attendance
2025-08-04 10:09:40,801 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 10:09:40,803 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 10:09:40,806 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for attendance
2025-08-04 10:09:40,808 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for attendance
2025-08-04 10:09:40,809 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 10:09:40,810 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for attendance
2025-08-04 10:09:40,812 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-08-04 10:09:40,817 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for attendance
2025-08-04 10:09:40,821 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-08-04 10:09:40,826 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for attendance
2025-08-04 10:09:40,827 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 10:09:40,829 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 10:09:40,831 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for attendance
2025-08-04 11:01:20,494 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 11:01:20,509 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 11:01:20,526 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 11:01:20,528 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 11:01:20,529 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 11:01:20,531 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 11:01:20,535 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 11:01:20,543 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 11:01:20,559 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 11:01:20,561 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 11:01:20,573 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 11:01:20,585 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 11:01:20,587 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 11:16:45,184 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-08-04 11:16:45,213 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-08-04 11:16:45,235 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-08-04 11:16:45,243 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-08-04 11:16:45,258 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-08-04 11:16:45,266 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-08-04 11:16:45,292 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-08-04 11:16:45,328 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-08-04 11:16:45,351 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-08-04 11:16:46,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:16:46,043 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 11:16:46,049 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 11:16:46,055 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:16:46,060 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 11:16:46,065 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 11:16:46,072 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:16:46,074 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 11:16:46,089 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 11:16:46,094 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 11:16:46,116 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 11:16:46,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:16:46,176 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for certifications
2025-08-04 11:16:46,190 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for certifications
2025-08-04 11:25:10,146 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for masumin
2025-08-04 11:25:10,226 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for masumin
2025-08-04 11:25:10,246 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for masumin
2025-08-04 11:25:10,272 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for masumin
2025-08-04 11:25:10,331 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for masumin
2025-08-04 11:25:10,590 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-08-04 11:25:10,599 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 11:25:10,601 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-08-04 11:25:10,603 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-08-04 11:25:10,606 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 11:25:10,608 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 11:25:10,610 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-08-04 11:25:10,612 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-08-04 11:25:10,614 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-08-04 11:25:10,617 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-08-04 11:25:10,620 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-08-04 11:25:10,622 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-08-04 11:25:10,625 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-08-04 11:25:10,628 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-08-04 11:25:10,631 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-08-04 11:25:10,634 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-08-04 11:25:10,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:25:10,638 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-08-04 11:25:10,641 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:25:10,643 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 11:25:10,646 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-08-04 11:25:10,648 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-08-04 11:25:10,650 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-08-04 11:25:10,653 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 11:25:10,655 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-08-04 11:25:10,657 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-08-04 11:25:10,659 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-08-04 11:25:10,661 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-08-04 11:25:10,664 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-08-04 11:25:10,666 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-08-04 11:25:10,670 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-08-04 11:25:10,672 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 11:25:10,678 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:25:10,680 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-08-04 11:25:10,682 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-08-04 11:25:10,684 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-08-04 11:25:10,686 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:25:10,688 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-08-04 11:25:10,690 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 11:25:10,693 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-08-04 11:25:10,696 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-08-04 11:25:10,699 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 11:25:10,743 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 11:25:10,773 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 11:25:10,831 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 11:25:10,866 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 11:25:11,155 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for thps.or.tz
2025-08-04 11:25:11,166 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for thps.or.tz
2025-08-04 11:25:11,232 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for thps.or.tz
2025-08-04 11:31:17,544 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for masumin
2025-08-04 11:31:17,593 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for masumin
2025-08-04 11:31:17,608 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for masumin
2025-08-04 11:31:17,624 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for masumin
2025-08-04 11:31:17,631 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for masumin
2025-08-04 11:31:17,642 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for masumin
2025-08-04 11:31:17,650 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for masumin
2025-08-04 11:31:17,656 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for masumin
2025-08-04 11:31:17,665 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for masumin
2025-08-04 11:31:17,667 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for masumin
2025-08-04 11:31:17,670 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for masumin
2025-08-04 11:31:17,687 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for masumin
2025-08-04 11:31:21,999 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 11:31:23,030 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:31:23,371 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:31:25,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:31:28,716 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 11:31:29,428 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 11:31:31,329 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 11:31:41,847 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 11:32:38,060 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 11:32:38,093 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 11:32:38,103 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 11:32:38,105 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:46:50,161 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-08-04 11:46:50,166 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 11:46:50,170 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-08-04 11:46:50,172 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-08-04 11:46:50,174 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-08-04 11:46:50,176 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:46:50,179 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-08-04 11:46:50,184 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 11:46:50,186 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 11:46:50,188 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-08-04 11:46:50,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 11:46:50,192 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 11:46:50,193 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-08-04 11:46:50,195 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-08-04 11:46:50,197 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-08-04 11:46:50,199 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-08-04 11:46:50,201 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-08-04 11:46:50,204 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-08-04 11:46:50,206 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-08-04 11:46:50,211 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:46:50,213 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 11:46:50,214 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-08-04 11:46:50,219 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 11:46:50,222 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 11:46:50,228 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-08-04 11:46:50,233 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-08-04 11:46:50,239 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-08-04 11:46:50,242 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 11:46:50,244 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-08-04 11:46:50,247 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 11:46:50,249 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-08-04 12:01:03,082 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-08-04 12:01:03,108 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-08-04 12:01:03,112 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 12:01:03,124 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-08-04 12:01:03,126 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-08-04 12:01:03,129 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-08-04 12:01:03,132 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-08-04 12:01:03,136 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-08-04 12:01:03,138 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-08-04 12:01:03,141 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-08-04 12:01:03,143 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 12:01:03,152 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 12:01:03,160 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-08-04 12:01:03,162 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-08-04 12:01:03,176 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 12:01:03,186 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-08-04 12:01:03,197 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-08-04 12:01:03,210 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 12:01:03,217 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-08-04 12:01:03,233 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-08-04 12:01:03,243 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-08-04 12:01:03,248 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-08-04 12:01:03,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-08-04 12:01:03,261 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 12:01:03,272 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-08-04 12:01:03,275 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-08-04 12:01:03,278 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 12:01:03,283 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-08-04 12:01:03,288 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-08-04 12:01:03,293 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-08-04 12:01:03,299 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-08-04 12:01:03,303 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-08-04 12:01:03,306 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-08-04 12:01:03,312 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 12:01:03,320 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-08-04 12:01:03,327 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-08-04 12:01:03,335 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-08-04 12:01:03,339 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-08-04 12:01:03,401 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for thps.or.tz
2025-08-04 12:01:03,408 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for thps.or.tz
2025-08-04 12:01:03,414 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for thps.or.tz
2025-08-04 12:01:03,418 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for thps.or.tz
2025-08-04 12:01:03,424 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for thps.or.tz
2025-08-04 12:01:03,433 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for thps.or.tz
2025-08-04 12:01:03,446 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for thps.or.tz
2025-08-04 12:01:03,456 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for thps.or.tz
2025-08-04 12:01:03,461 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for thps.or.tz
2025-08-04 12:01:03,465 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for thps.or.tz
2025-08-04 12:01:03,471 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for thps.or.tz
2025-08-04 12:01:03,481 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for thps.or.tz
2025-08-04 12:01:03,497 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for thps.or.tz
2025-08-04 12:01:03,500 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for thps.or.tz
2025-08-04 12:01:03,505 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for thps.or.tz
2025-08-04 12:01:03,510 ERROR scheduler Skipped queueing birthday_wishes_hourly because it was found in queue for thps.or.tz
2025-08-04 12:01:03,522 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for thps.or.tz
2025-08-04 12:01:03,524 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for thps.or.tz
2025-08-04 12:01:03,527 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for thps.or.tz
2025-08-04 12:01:03,534 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for thps.or.tz
2025-08-04 12:01:03,540 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for thps.or.tz
2025-08-04 12:01:03,543 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for thps.or.tz
2025-08-04 12:01:03,546 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for thps.or.tz
2025-08-04 12:01:03,555 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for thps.or.tz
2025-08-04 12:01:03,558 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for thps.or.tz
2025-08-04 12:01:03,576 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for thps.or.tz
2025-08-04 12:01:03,584 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for thps.or.tz
2025-08-04 12:01:03,592 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for thps.or.tz
2025-08-04 12:01:03,595 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for thps.or.tz
2025-08-04 12:01:03,619 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for thps.or.tz
2025-08-04 12:01:03,625 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for thps.or.tz
2025-08-04 12:01:03,645 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for thps.or.tz
2025-08-04 12:01:03,649 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for thps.or.tz
2025-08-04 12:01:03,658 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for thps.or.tz
2025-08-04 12:01:03,667 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for thps.or.tz
2025-08-04 12:01:03,671 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for thps.or.tz
2025-08-04 12:01:03,743 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for certifications
2025-08-04 12:01:03,750 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for certifications
2025-08-04 12:01:03,753 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for certifications
2025-08-04 12:01:03,765 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for certifications
2025-08-04 12:01:03,771 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for certifications
2025-08-04 12:01:03,780 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for certifications
2025-08-04 12:01:03,784 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for certifications
2025-08-04 12:01:03,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for certifications
2025-08-04 12:01:03,792 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for certifications
2025-08-04 12:01:03,802 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for certifications
2025-08-04 12:01:03,812 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for certifications
2025-08-04 12:01:03,817 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for certifications
2025-08-04 12:01:03,826 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for certifications
2025-08-04 12:01:03,838 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for certifications
2025-08-04 12:01:03,841 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for certifications
2025-08-04 12:01:03,849 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for certifications
2025-08-04 12:01:03,854 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for certifications
2025-08-04 12:01:03,876 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for certifications
2025-08-04 12:01:03,945 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 12:01:03,968 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 12:01:03,975 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 12:01:03,997 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 12:01:04,011 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 12:01:04,024 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 12:01:04,038 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 12:01:04,054 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for masumin
2025-08-04 12:01:04,063 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 12:01:04,083 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 12:01:04,102 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 12:01:04,105 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 12:01:04,135 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 12:01:04,142 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 12:01:04,202 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 12:01:04,210 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-08-04 12:01:04,213 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-08-04 12:01:04,217 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-08-04 12:01:04,220 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 12:01:04,230 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-08-04 12:01:04,236 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-08-04 12:01:04,242 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-08-04 12:01:04,245 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 12:01:04,255 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 12:01:04,262 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-08-04 12:01:04,269 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 12:01:04,279 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 12:01:04,283 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-08-04 12:01:04,287 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-08-04 12:01:04,290 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for attendance
2025-08-04 12:01:04,295 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-08-04 12:01:04,301 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-08-04 12:01:04,304 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 12:01:04,306 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-08-04 12:01:04,315 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-08-04 12:01:04,322 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 12:01:04,331 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-08-04 12:01:04,346 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 12:01:04,351 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 12:01:04,365 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-08-04 12:01:04,370 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-08-04 12:01:04,376 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-08-04 12:01:04,382 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 12:01:04,385 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 12:01:04,405 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 12:01:04,410 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-08-04 12:01:04,414 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-08-04 12:01:04,423 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-08-04 12:01:04,428 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-08-04 12:01:04,432 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-08-04 12:01:04,437 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-08-04 12:01:04,472 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for beetle
2025-08-04 12:01:04,475 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for beetle
2025-08-04 12:01:04,486 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for beetle
2025-08-04 12:01:04,489 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for beetle
2025-08-04 12:01:04,501 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for beetle
2025-08-04 12:01:04,510 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for beetle
2025-08-04 12:01:04,520 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for beetle
2025-08-04 12:01:04,525 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 12:01:04,528 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 12:01:04,536 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for beetle
2025-08-04 12:01:04,541 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 12:01:04,543 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for beetle
2025-08-04 12:01:04,545 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 12:01:04,548 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 12:01:04,553 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 12:01:04,572 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for beetle
2025-08-04 12:01:04,577 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for beetle
2025-08-04 12:01:04,588 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 12:01:04,603 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for beetle
2025-08-04 12:01:04,611 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for beetle
2025-08-04 12:01:04,616 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 12:01:04,622 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 12:01:04,627 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for beetle
2025-08-04 12:01:04,630 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 12:01:04,635 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for beetle
2025-08-04 12:01:04,644 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for beetle
2025-08-04 12:01:04,647 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for beetle
2025-08-04 12:01:04,650 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for beetle
2025-08-04 12:01:04,665 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for beetle
2025-08-04 12:01:04,668 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for beetle
2025-08-04 12:01:04,679 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for beetle
2025-08-04 12:01:04,684 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 12:01:04,688 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for beetle
2025-08-04 12:01:04,693 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for beetle
2025-08-04 12:01:04,707 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for beetle
2025-08-04 12:01:04,720 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for beetle
2025-08-04 12:01:04,724 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 12:01:04,762 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 12:01:04,775 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 12:01:04,778 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 12:01:04,783 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 12:01:04,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 12:01:04,792 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 12:01:04,820 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 12:01:04,823 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
