2025-08-04 12:02:05,595 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 12:02:05,599 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 12:02:05,616 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 12:02:05,621 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 12:02:05,631 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 12:02:05,652 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 12:02:05,667 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 12:02:05,672 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 12:02:05,677 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 12:02:05,685 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 12:02:05,693 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 12:02:05,699 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 12:03:05,858 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 12:03:05,860 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 12:03:05,866 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 12:03:05,875 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 12:03:05,899 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 12:03:05,911 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 12:03:05,915 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 12:03:05,919 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 12:03:05,952 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 12:03:05,958 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 12:03:05,961 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 12:03:05,969 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for beetle
2025-08-04 12:03:05,970 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
