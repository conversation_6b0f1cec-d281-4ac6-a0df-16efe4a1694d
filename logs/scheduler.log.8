2025-08-04 10:05:37,261 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for beetle
2025-08-04 10:05:37,264 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 10:05:37,266 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 10:05:37,267 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 10:05:37,274 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-08-04 10:05:37,277 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:05:37,278 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 10:05:37,282 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for beetle
2025-08-04 10:05:37,284 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for beetle
2025-08-04 10:05:37,287 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-08-04 10:05:37,289 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for beetle
2025-08-04 10:05:37,291 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for beetle
2025-08-04 10:05:37,293 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-08-04 10:05:37,295 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for beetle
2025-08-04 10:05:37,296 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 10:05:37,298 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-08-04 10:05:37,300 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-08-04 10:05:37,306 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 10:05:37,308 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-08-04 10:05:37,309 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for beetle
2025-08-04 10:05:37,312 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 10:05:37,315 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for beetle
2025-08-04 10:05:37,317 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for beetle
2025-08-04 10:05:37,319 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for beetle
2025-08-04 10:05:37,321 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-08-04 10:05:37,326 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for beetle
2025-08-04 10:05:37,329 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-08-04 10:05:37,332 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 10:05:37,337 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:05:37,338 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-08-04 10:05:37,342 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for beetle
2025-08-04 10:05:37,344 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for beetle
2025-08-04 10:05:37,346 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 10:05:37,348 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-08-04 10:05:37,350 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-08-04 10:05:37,353 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for beetle
2025-08-04 10:05:37,359 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 10:05:37,360 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-08-04 10:05:37,362 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-08-04 10:05:37,367 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-08-04 10:05:37,372 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:05:37,375 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for beetle
2025-08-04 10:05:37,376 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for beetle
2025-08-04 10:05:37,378 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 10:05:37,381 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for beetle
2025-08-04 10:05:37,382 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 10:05:37,386 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for beetle
2025-08-04 10:05:37,387 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-08-04 10:05:37,389 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-08-04 10:05:37,391 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-08-04 10:05:37,392 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:06:37,946 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 10:06:37,948 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 10:06:37,949 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 10:06:37,952 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 10:06:37,953 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:06:37,956 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-08-04 10:06:37,958 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-08-04 10:06:37,959 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-08-04 10:06:37,963 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-08-04 10:06:37,965 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 10:06:37,966 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-08-04 10:06:37,968 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for beetle
2025-08-04 10:06:37,970 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 10:06:37,974 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-08-04 10:06:37,977 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:06:37,981 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-08-04 10:06:37,982 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-08-04 10:06:37,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-08-04 10:06:37,989 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-08-04 10:06:37,992 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 10:06:37,994 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 10:06:37,997 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for beetle
2025-08-04 10:06:37,999 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-08-04 10:06:38,002 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for beetle
2025-08-04 10:06:38,004 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 10:06:38,005 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-08-04 10:06:38,006 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-08-04 10:06:38,010 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-08-04 10:06:38,013 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-08-04 10:06:38,016 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 10:06:38,018 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-08-04 10:06:38,020 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for beetle
2025-08-04 10:06:38,027 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 10:06:38,028 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:06:38,029 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for beetle
2025-08-04 10:06:38,033 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 10:06:38,035 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-08-04 10:06:38,044 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:07:38,406 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-08-04 10:07:38,408 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 10:07:38,409 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:07:38,410 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-08-04 10:07:38,412 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 10:07:38,414 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 10:07:38,417 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-08-04 10:07:38,418 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 10:07:38,420 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-08-04 10:07:38,421 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for beetle
2025-08-04 10:07:38,425 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-08-04 10:07:38,427 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-08-04 10:07:38,428 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-08-04 10:07:38,430 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-08-04 10:07:38,432 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 10:07:38,433 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-08-04 10:07:38,436 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-08-04 10:07:38,437 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for beetle
2025-08-04 10:07:38,438 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-08-04 10:07:38,439 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-08-04 10:07:38,445 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 10:07:38,448 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 10:07:38,454 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:07:38,455 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for beetle
2025-08-04 10:07:38,457 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for beetle
2025-08-04 10:07:38,458 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-08-04 10:07:38,467 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-08-04 10:07:38,469 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-08-04 10:07:38,471 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-08-04 10:07:38,477 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for beetle
2025-08-04 10:07:38,478 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 10:07:38,481 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 10:07:38,483 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 10:07:38,486 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:07:38,488 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 10:07:38,491 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 10:07:38,493 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-08-04 10:07:38,498 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:08:39,514 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 10:08:39,517 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-08-04 10:08:39,522 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 10:08:39,528 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-08-04 10:08:39,532 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for beetle
2025-08-04 10:08:39,534 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-08-04 10:08:39,540 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 10:08:39,544 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 10:08:39,550 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 10:08:39,552 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-08-04 10:08:39,559 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-08-04 10:08:39,565 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-08-04 10:08:39,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-08-04 10:08:39,575 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-08-04 10:08:39,581 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for beetle
2025-08-04 10:08:39,599 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for beetle
2025-08-04 10:08:39,601 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 10:08:39,603 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 10:08:39,617 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 10:08:39,621 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 10:08:39,624 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-08-04 10:08:39,627 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-08-04 10:08:39,638 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
2025-08-04 10:08:39,645 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:08:39,663 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-08-04 10:08:39,665 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:08:39,667 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for beetle
2025-08-04 10:08:39,674 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:08:39,677 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-08-04 10:08:39,680 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-08-04 10:08:39,682 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for beetle
2025-08-04 10:08:39,697 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-08-04 10:08:39,702 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 10:08:39,717 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 10:08:39,722 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:08:39,724 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-08-04 10:08:39,726 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 10:08:39,734 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-08-04 10:09:40,516 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 10:09:40,519 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for beetle
2025-08-04 10:09:40,521 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for beetle
2025-08-04 10:09:40,523 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for beetle
2025-08-04 10:09:40,527 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:09:40,531 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 10:09:40,532 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for beetle
2025-08-04 10:09:40,535 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for beetle
2025-08-04 10:09:40,537 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for beetle
2025-08-04 10:09:40,539 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:09:40,540 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for beetle
2025-08-04 10:09:40,541 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for beetle
2025-08-04 10:09:40,542 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for beetle
2025-08-04 10:09:40,545 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for beetle
2025-08-04 10:09:40,546 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 10:09:40,548 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for beetle
2025-08-04 10:09:40,549 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 10:09:40,550 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for beetle
2025-08-04 10:09:40,556 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for beetle
2025-08-04 10:09:40,559 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for beetle
2025-08-04 10:09:40,561 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for beetle
2025-08-04 10:09:40,563 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 10:09:40,565 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for beetle
2025-08-04 10:09:40,568 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 10:09:40,570 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for beetle
2025-08-04 10:09:40,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for beetle
2025-08-04 10:09:40,574 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for beetle
2025-08-04 10:09:40,575 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for beetle
2025-08-04 10:09:40,577 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for beetle
2025-08-04 10:09:40,580 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for beetle
2025-08-04 10:09:40,582 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for beetle
2025-08-04 10:09:40,586 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for beetle
2025-08-04 10:09:40,591 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 10:09:40,593 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for beetle
2025-08-04 10:09:40,595 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for beetle
2025-08-04 10:09:40,598 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 10:09:40,601 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for beetle
2025-08-04 10:09:40,603 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 10:09:40,605 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 10:09:40,606 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 10:09:40,607 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 10:09:40,608 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for beetle
2025-08-04 10:09:40,614 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for beetle
