2025-07-23 16:30:24,408 ERROR frappe Unable to load translations
Site: mis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5962926092304900516

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 191, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1553, in get_installed_apps
    installed = json.loads(db.get_global("installed_apps") or "[]")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1134, in get_global
    return self.get_default(key, user)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1138, in get_default
    d = self.get_defaults(key, parent)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1154, in get_defaults
    defaults = frappe.defaults.get_defaults_for(parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/defaults.py", line 244, in get_defaults_for
    .run(as_dict=True)
     ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_e16f6233902b64d0.tabDefaultValue' doesn't exist")
2025-07-23 16:30:24,416 ERROR frappe Unable to load translations
Site: mis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5962926092304900516

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/translate.py", line 191, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1553, in get_installed_apps
    installed = json.loads(db.get_global("installed_apps") or "[]")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1134, in get_global
    return self.get_default(key, user)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1138, in get_default
    d = self.get_defaults(key, parent)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 1154, in get_defaults
    defaults = frappe.defaults.get_defaults_for(parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/defaults.py", line 244, in get_defaults_for
    .run(as_dict=True)
     ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 230, in sql
    self._cursor.execute(query, values)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table '_e16f6233902b64d0.tabDefaultValue' doesn't exist")
2025-07-24 14:10:56,962 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24"}', 'file_format_type': 'Excel', 'cmd': 'frappe.desk.query_report.export_query'}
2025-07-24 14:15:44,243 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:16:04,202 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:25:15,569 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:26:02,933 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:12,851 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:34,812 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 14:41:35,168 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{"from_date":"2025-06-24","to_date":"2025-07-24","report_type":"Workflow Bottleneck Analysis"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-24 16:40:13,597 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'customer': '', 'customer_phone': '', 'delivery_note': '', 'delivery_person': '', 'received_date': '', 'branch_agent': '', 'branch_agent_name': '', 'destination': '', 'total_amount': 0, 'goods_details': [], 'main_agent': '<EMAIL>', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.create_goods_receipt'}
2025-07-24 16:40:45,384 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'customer': '', 'customer_phone': '', 'delivery_note': '', 'delivery_person': '', 'received_date': '', 'branch_agent': '', 'branch_agent_name': '', 'destination': '', 'total_amount': 0, 'goods_details': [], 'main_agent': '<EMAIL>', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.create_goods_receipt'}
2025-07-24 16:52:27,911 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'doc_name': 'GR-2025-07-00156', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.doctype.goods_receipt.goods_receipt.create_delivery_note'}
2025-07-25 10:17:04,941 ERROR frappe New Exception collected in error log
Site: helpdesk
Form Dict: {}
2025-07-25 10:20:26,343 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'app_path': 'tickets/new'}
2025-07-25 10:42:13,630 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-25 12:49:31,484 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-25 12:50:55,646 ERROR frappe New Exception collected in error log
Site: thps.or.tz
Form Dict: {'txt': '', 'doctype': 'HD Ticket', 'ignore_user_permissions': '0', 'reference_doctype': 'HD Notification', 'page_length': '10', 'cmd': 'frappe.desk.search.search_link'}
2025-07-25 16:58:52,825 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:00:17,041 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:00:22,034 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:02:21,096 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:05:31,203 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:05:34,770 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:10:34,432 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:10:44,716 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:10:47,397 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:10:51,264 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:10:57,066 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:11:07,556 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:13:06,682 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:13:13,356 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:15:51,684 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:16:21,244 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:19:22,585 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:19:26,348 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:28:18,413 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:35:46,582 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:35:56,083 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:38:42,946 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:38:47,224 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-25 17:39:40,153 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.api.get_sales_invoices'}
2025-07-28 21:11:32,840 ERROR frappe New Exception collected in error log
Site: clearing
Form Dict: {'doc_name': 'GR-2024-11-00130', 'cmd': 'tenaciousfreightmaster.tenacious_freightmaster.doctype.goods_receipt.goods_receipt.create_shipment_manifest'}
2025-07-29 09:43:30,219 ERROR frappe New Exception collected in error log
Site: avhelp
Form Dict: {'doctype': 'HD Ticket', 'filters': {'_assign': ['LIKE', '%jmich%'], 'status': 'Replied'}, 'order_by': 'modified desc', 'page_length': 20, 'page_length_count': 20, 'view': {'view_type': 'list', 'group_by_field': 'owner'}, 'columns': [], 'rows': [], 'show_customer_portal_fields': False, 'is_default': True, 'cmd': 'helpdesk.api.doc.get_list_data'}
2025-07-29 09:43:30,219 ERROR frappe New Exception collected in error log
Site: avhelp
Form Dict: {'doctype': 'HD Ticket', 'show_customer_portal_fields': False, 'cmd': 'helpdesk.api.doc.get_quick_filters'}
2025-07-29 09:52:04,246 ERROR frappe New Exception collected in error log
Site: avhelp
Form Dict: {'doctype': 'HD Ticket', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-29 09:52:15,525 ERROR frappe New Exception collected in error log
Site: avhelp
Form Dict: {'doctype': 'HD Ticket', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-31 09:57:37,248 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doc': '{"docstatus":0,"doctype":"Leave Encashment","name":"new-leave-encashment-yvvrshnylf","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"NEELKANTH CHEMICALS LIMITED","status":"Paid","encashment_date":"2024-12-31","currency":"TZS","paid_amount":0,"pay_via_payment_entry":0,"posting_date":"2025-07-31","__run_link_triggers":false,"leave_period":"HR-LPR-2024-00001","employee":"HR-EMP-00126","employee_name":"HENDRY JOSEPH MLANGWA","department":"ELECTRICAL - NCL","leave_type":"Annual Leave","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null,"workflow_state":"Draft","encashment_amount":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-31 10:02:03,644 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doc': '{"docstatus":0,"doctype":"Leave Encashment","name":"new-leave-encashment-gredumpbec","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"NEELKANTH CHEMICALS LIMITED","status":"Paid","encashment_date":"2024-12-31","currency":"TZS","paid_amount":0,"pay_via_payment_entry":0,"posting_date":"2025-07-31","__run_link_triggers":false,"leave_period":"HR-LPR-2024-00001","employee":"HR-EMP-00126","employee_name":"HENDRY JOSEPH MLANGWA","department":"ELECTRICAL - NCL","leave_type":"Annual Leave","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null,"workflow_state":"Draft","encashment_amount":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-31 13:18:48,215 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'doctype': 'Sales Invoice', 'fields': '["`tabSales Invoice`.`name`","`tabSales Invoice`.`owner`","`tabSales Invoice`.`creation`","`tabSales Invoice`.`modified`","`tabSales Invoice`.`modified_by`","`tabSales Invoice`.`_user_tags`","`tabSales Invoice`.`_comments`","`tabSales Invoice`.`_assign`","`tabSales Invoice`.`_liked_by`","`tabSales Invoice`.`docstatus`","`tabSales Invoice`.`idx`","`tabSales Invoice`.`posting_date`","`tabSales Invoice`.`vfd_status`","`tabSales Invoice`.`amount_received`","`tabSales Invoice`.`total`","`tabSales Invoice`.`net_total`","`tabSales Invoice`.`total_taxes_and_charges`","`tabSales Invoice`.`grand_total`","`tabSales Invoice`.`rounding_adjustment`","`tabSales Invoice`.`rounded_total`","`tabSales Invoice`.`total_advance`","`tabSales Invoice`.`outstanding_amount`","`tabSales Invoice`.`discount_amount`","`tabSales Invoice`.`paid_amount`","`tabSales Invoice`.`total_billing_amount`","`tabSales Invoice`.`change_amount`","`tabSales Invoice`.`write_off_amount`","`tabSales Invoice`.`status`","`tabSales Invoice`.`title`","`tabSales Invoice`.`customer`","`tabSales Invoice`.`customer_name`","`tabSales Invoice`.`base_grand_total`","`tabSales Invoice`.`due_date`","`tabSales Invoice`.`company`","`tabSales Invoice`.`currency`","`tabSales Invoice`.`is_return`","`tabSales Invoice`.`_seen`","`tabSales Invoice`.`party_account_currency`"]', 'filters': '[["Sales Invoice","vfd_verification_url","is","set"],["Sales Invoice","posting_time",">",null]]', 'order_by': '`tabSales Invoice`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1', 'cmd': 'frappe.desk.reportview.get'}
2025-07-31 14:51:12,315 ERROR frappe New Exception collected in error log
Site: alphaassociates.co.tz
Form Dict: {}
2025-07-31 14:51:12,476 ERROR frappe New Exception collected in error log
Site: alphaassociates.co.tz
Form Dict: {}
2025-08-01 11:37:58,586 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:30:36,227 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:33:15,814 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:43:24,901 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:49:20,267 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:50:00,961 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 12:51:19,096 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 13:23:28,794 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 13:29:45,123 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 14:29:48,866 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 14:33:12,503 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'customer': 'F021', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_customer_statement'}
2025-08-01 17:33:20,510 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 17:41:15,815 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 17:46:35,256 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 18:00:33,893 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-01 18:09:53,774 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-01', 'cmd': 'send_supplier_statement'}
2025-08-04 11:03:11,656 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': '<EMAIL>', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
2025-08-04 11:08:19,238 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': 'wefdf@h5.6t', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
2025-08-04 11:08:51,216 ERROR frappe New Exception collected in error log
Site: masumin
Form Dict: {'supplier': 'F013', 'email': 'cdvhtrfgwe', 'report_date': '2025-08-04', 'cmd': 'send_supplier_statement'}
2025-08-04 11:11:20,517 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'doctype': 'Workflow Transition History', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-08-04 11:20:06,321 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Turnaround Analysis Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:34:27,930 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Bottleneck Analysis', 'filters': '{"threshold_percentage":50,"from_date":"2025-07-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:34:47,816 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Bottleneck Analysis', 'filters': '{"threshold_percentage":50,"from_date":"2025-07-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:41:25,749 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Workflow Completion Summary', 'filters': '{"status":"Completed","from_date":"2025-05-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-08-04 11:44:18,858 ERROR frappe New Exception collected in error log
Site: neel
Form Dict: {'report_name': 'Transition Frequency Analysis', 'filters': '{"from_date":"2025-05-04","to_date":"2025-08-04"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
