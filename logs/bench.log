2025-03-03 09:50:07,123 INFO /home/<USER>/.local/bin/bench restart
2025-03-03 09:50:07,132 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-03 09:50:15,255 INFO /home/<USER>/.local/bin/bench start
2025-03-03 09:50:15,533 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 09:50:15,539 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-03 09:50:15,541 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 09:50:15,549 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 09:50:46,109 INFO /home/<USER>/.local/bin/bench use gadget
2025-03-03 09:50:48,032 INFO /home/<USER>/.local/bin/bench start
2025-03-03 09:50:48,315 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 09:50:48,316 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-03 09:50:48,320 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 09:50:48,385 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 11:43:51,764 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/csf_tz.git --branch version-15
2025-03-03 11:43:51,777 LOG Getting csf_tz
2025-03-03 11:43:51,777 DEBUG cd ./apps && git clone https://github.com/j0sh01/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-03-03 11:44:02,423 LOG Installing csf_tz
2025-03-03 11:44:02,423 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-03 11:44:05,279 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-03 11:44:06,553 DEBUG bench build --app csf_tz
2025-03-03 11:44:06,697 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-03-03 11:44:08,511 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-03 12:00:01,810 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-03 12:57:08,645 INFO /home/<USER>/.local/bin/bench use gadget
2025-03-03 12:57:11,086 INFO /home/<USER>/.local/bin/bench start
2025-03-03 12:57:11,284 INFO /home/<USER>/.local/bin/bench schedule
2025-03-03 12:57:11,287 INFO /home/<USER>/.local/bin/bench watch
2025-03-03 12:57:11,289 INFO /home/<USER>/.local/bin/bench worker
2025-03-03 12:57:11,307 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 00:00:01,602 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-03 18:00:01,883 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-03 18:00:01,959 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-04 10:58:30,438 INFO /home/<USER>/.local/bin/bench use gadget
2025-03-04 10:58:35,769 INFO /home/<USER>/.local/bin/bench start
2025-03-04 10:58:36,282 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 10:58:36,297 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 10:58:36,332 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 10:58:36,347 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 11:46:41,723 INFO /home/<USER>/.local/bin/bench migrate
2025-03-04 11:46:58,343 INFO /home/<USER>/.local/bin/bench start
2025-03-04 11:46:58,347 WARNING /home/<USER>/.local/bin/bench start executed with exit code 1
2025-03-04 11:49:14,101 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 11:49:14,105 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 11:49:22,775 INFO /home/<USER>/.local/bin/bench build
2025-03-04 11:51:04,099 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 11:51:04,103 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 11:56:49,422 INFO /home/<USER>/.local/bin/bench start
2025-03-04 11:56:49,713 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 11:56:49,725 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 12:00:01,992 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-04 12:19:57,344 INFO /home/<USER>/.local/bin/bench start
2025-03-04 12:19:57,573 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 12:19:57,573 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 12:19:57,608 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 12:19:57,613 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 12:30:15,266 INFO /home/<USER>/.local/bin/bench start
2025-03-04 12:30:15,552 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 12:30:15,561 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 12:30:15,580 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 12:30:42,691 INFO /home/<USER>/.local/bin/bench start
2025-03-04 12:30:42,922 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 12:30:42,956 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 12:31:06,393 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 12:31:06,400 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 12:31:09,127 LOG Installing frappe
2025-03-04 12:31:09,128 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 12:31:11,286 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe  executed with exit code 1
2025-03-04 12:31:11,287 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 12:31:58,330 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 12:31:58,337 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 12:31:59,281 LOG Installing frappe
2025-03-04 12:31:59,281 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 12:32:00,873 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe  executed with exit code 1
2025-03-04 12:32:00,873 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 12:33:39,110 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 12:33:39,117 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 12:33:40,174 LOG Installing frappe
2025-03-04 12:33:40,174 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 12:33:43,603 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe  executed with exit code 1
2025-03-04 12:33:43,604 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 13:03:17,067 INFO /home/<USER>/.local/bin/bench -v
2025-03-04 13:03:20,764 INFO /home/<USER>/.local/bin/bench -version
2025-03-04 13:03:28,034 INFO /home/<USER>/.local/bin/bench --version
2025-03-04 13:03:34,569 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:03:34,866 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 13:03:34,877 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 13:03:53,039 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:03:53,332 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 13:03:53,332 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 13:03:53,363 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 13:08:13,205 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:08:13,469 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 13:08:13,477 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 13:08:13,504 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 13:08:13,504 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 13:09:36,450 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:09:36,738 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 13:09:36,745 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 13:09:36,745 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 13:09:36,759 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 13:12:16,399 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:12:16,669 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 13:12:16,683 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 13:12:16,683 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 13:12:16,699 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 13:12:50,494 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 13:12:50,501 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 13:12:51,563 LOG Installing frappe
2025-03-04 13:12:51,564 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 13:12:56,737 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-03-04 13:13:07,667 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-03-04 13:13:09,982 LOG Installing twilio_integration
2025-03-04 13:13:09,982 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/twilio_integration 
2025-03-04 13:13:12,469 LOG Installing hrms
2025-03-04 13:13:12,469 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-03-04 13:13:15,012 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-03-04 13:13:34,869 LOG Installing lending
2025-03-04 13:13:34,870 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-03-04 13:13:38,064 LOG Installing biometric_client
2025-03-04 13:13:38,064 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-03-04 13:13:40,325 LOG Installing erpbiometric_sync
2025-03-04 13:13:40,325 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpbiometric_sync 
2025-03-04 13:13:42,611 LOG Installing webshop
2025-03-04 13:13:42,611 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-03-04 13:13:44,763 LOG Installing vfd_providers
2025-03-04 13:13:44,763 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-03-04 13:13:46,570 LOG Installing tenaciousfreightmaster
2025-03-04 13:13:46,570 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenaciousfreightmaster 
2025-03-04 13:13:48,759 LOG Installing airplane_mode
2025-03-04 13:13:48,759 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/airplane_mode 
2025-03-04 13:13:50,957 LOG Installing utility_management
2025-03-04 13:13:50,957 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/utility_management 
2025-03-04 13:13:53,165 LOG Installing csf_tz
2025-03-04 13:13:53,166 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-04 13:13:57,105 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-04 13:13:57,561 LOG Installing tenacious_integration
2025-03-04 13:13:57,562 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacious_integration 
2025-03-04 13:13:59,737 LOG Installing hms_tz
2025-03-04 13:13:59,737 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-03-04 13:14:02,329 LOG Installing payware
2025-03-04 13:14:02,329 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-03-04 13:14:04,562 LOG Installing payments
2025-03-04 13:14:04,562 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-03-04 13:14:07,072 LOG Installing thps
2025-03-04 13:14:07,072 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/thps 
2025-03-04 13:14:09,680 LOG Installing fd_mis
2025-03-04 13:14:09,680 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/fd_mis 
2025-03-04 13:14:12,222 LOG Installing tenacioustravel
2025-03-04 13:14:12,222 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacioustravel 
2025-03-04 13:14:14,751 LOG Installing clearing
2025-03-04 13:14:14,751 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-03-04 13:14:17,252 LOG Installing healthcare
2025-03-04 13:14:17,253 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-03-04 13:14:19,201 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare  executed with exit code 1
2025-03-04 13:14:19,202 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 13:15:30,298 INFO /home/<USER>/.local/bin/bench build
2025-03-04 13:16:09,334 INFO /home/<USER>/.local/bin/bench build
2025-03-04 13:43:11,570 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/frappe.git --branch version-15
2025-03-04 13:43:13,658 INFO App moved from apps/frappe to archived/apps/frappe-2025-03-04
2025-03-04 13:43:13,660 LOG Getting frappe
2025-03-04 13:43:13,660 DEBUG cd ./apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-03-04 13:43:51,393 LOG Installing frappe
2025-03-04 13:43:51,393 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 13:44:01,481 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-03-04 13:44:05,982 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-03-04 13:44:50,212 DEBUG bench build --app frappe
2025-03-04 13:44:51,239 INFO /home/<USER>/.local/bin/bench build --app frappe
2025-03-04 13:45:42,489 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-04 13:46:04,638 INFO /home/<USER>/.local/bin/bench use gadget
2025-03-04 13:46:09,822 INFO /home/<USER>/.local/bin/bench start
2025-03-04 13:46:10,368 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 13:46:10,372 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 13:46:10,431 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 13:46:10,461 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 15:52:25,452 INFO /home/<USER>/.local/bin/bench start
2025-03-04 15:52:25,840 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-04 15:52:25,840 INFO /home/<USER>/.local/bin/bench worker
2025-03-04 15:52:25,841 INFO /home/<USER>/.local/bin/bench watch
2025-03-04 15:52:25,845 INFO /home/<USER>/.local/bin/bench schedule
2025-03-04 15:52:53,782 INFO /home/<USER>/.local/bin/bench start
2025-03-04 15:54:09,962 INFO /home/<USER>/.local/bin/bench start
2025-03-04 15:54:27,498 INFO /home/<USER>/.local/bin/bench restart
2025-03-04 15:54:27,507 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-04 15:55:47,180 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 15:55:47,188 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 15:55:49,145 LOG Installing frappe
2025-03-04 15:55:49,146 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 15:55:51,962 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-03-04 15:55:55,485 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-03-04 15:55:57,089 LOG Installing twilio_integration
2025-03-04 15:55:57,090 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/twilio_integration 
2025-03-04 15:55:59,786 LOG Installing hrms
2025-03-04 15:55:59,786 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-03-04 15:56:02,165 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-03-04 15:56:13,574 LOG Installing lending
2025-03-04 15:56:13,574 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-03-04 15:56:15,855 LOG Installing biometric_client
2025-03-04 15:56:15,856 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-03-04 15:56:18,139 LOG Installing erpbiometric_sync
2025-03-04 15:56:18,139 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpbiometric_sync 
2025-03-04 15:56:20,425 LOG Installing webshop
2025-03-04 15:56:20,426 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-03-04 15:56:23,047 LOG Installing vfd_providers
2025-03-04 15:56:23,047 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-03-04 15:56:24,882 LOG Installing tenaciousfreightmaster
2025-03-04 15:56:24,882 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenaciousfreightmaster 
2025-03-04 15:56:27,201 LOG Installing airplane_mode
2025-03-04 15:56:27,202 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/airplane_mode 
2025-03-04 15:56:29,445 LOG Installing utility_management
2025-03-04 15:56:29,445 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/utility_management 
2025-03-04 15:56:31,745 LOG Installing csf_tz
2025-03-04 15:56:31,746 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-04 15:56:35,543 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-04 15:56:36,018 LOG Installing tenacious_integration
2025-03-04 15:56:36,018 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacious_integration 
2025-03-04 15:56:38,190 LOG Installing hms_tz
2025-03-04 15:56:38,190 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-03-04 15:56:40,953 LOG Installing payware
2025-03-04 15:56:40,954 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-03-04 15:56:43,334 LOG Installing payments
2025-03-04 15:56:43,334 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-03-04 15:56:45,748 LOG Installing thps
2025-03-04 15:56:45,748 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/thps 
2025-03-04 15:56:48,181 LOG Installing fd_mis
2025-03-04 15:56:48,181 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/fd_mis 
2025-03-04 15:56:50,367 LOG Installing tenacioustravel
2025-03-04 15:56:50,367 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacioustravel 
2025-03-04 15:56:52,543 LOG Installing clearing
2025-03-04 15:56:52,543 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-03-04 15:56:54,776 LOG Installing healthcare
2025-03-04 15:56:54,777 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-03-04 15:56:56,753 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare  executed with exit code 1
2025-03-04 15:56:56,753 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 15:57:30,008 INFO /home/<USER>/.local/bin/bench remove-app healthcare
2025-03-04 15:57:30,129 INFO /home/<USER>/.local/bin/bench --site all list-apps --format json
2025-03-04 15:57:33,566 WARNING /home/<USER>/.local/bin/bench remove-app healthcare executed with exit code 1
2025-03-04 15:58:00,634 INFO /home/<USER>/.local/bin/bench --site nephro1 uninstall-app healthcare
2025-03-04 15:58:48,852 INFO /home/<USER>/.local/bin/bench remove-app healthcare
2025-03-04 15:58:49,010 INFO /home/<USER>/.local/bin/bench --site all list-apps --format json
2025-03-04 15:58:49,777 INFO /home/<USER>/.local/bin/bench --site hr list-apps
2025-03-04 15:58:50,362 INFO /home/<USER>/.local/bin/bench --site wasco list-apps
2025-03-04 15:58:50,926 INFO /home/<USER>/.local/bin/bench --site fintest list-apps
2025-03-04 15:58:51,495 INFO /home/<USER>/.local/bin/bench --site masumin list-apps
2025-03-04 15:58:52,071 INFO /home/<USER>/.local/bin/bench --site yogi list-apps
2025-03-04 15:58:52,648 INFO /home/<USER>/.local/bin/bench --site certifications list-apps
2025-03-04 15:58:53,212 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-03-04 15:58:53,784 INFO /home/<USER>/.local/bin/bench --site nephro1 list-apps
2025-03-04 15:58:54,358 INFO /home/<USER>/.local/bin/bench --site gadget list-apps
2025-03-04 15:58:54,922 INFO /home/<USER>/.local/bin/bench --site tervis list-apps
2025-03-04 15:58:55,510 INFO /home/<USER>/.local/bin/bench --site utility list-apps
2025-03-04 15:58:56,087 INFO /home/<USER>/.local/bin/bench --site clearing list-apps
2025-03-04 15:58:56,688 INFO /home/<USER>/.local/bin/bench --site horizon list-apps
2025-03-04 15:58:57,267 INFO /home/<USER>/.local/bin/bench --site helpdesk list-apps
2025-03-04 15:58:57,846 INFO /home/<USER>/.local/bin/bench --site ticket list-apps
2025-03-04 15:58:58,435 INFO /home/<USER>/.local/bin/bench --site integration list-apps
2025-03-04 15:58:59,013 INFO /home/<USER>/.local/bin/bench --site thps.or.tz list-apps
2025-03-04 15:58:59,474 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip uninstall -y healthcare
2025-03-04 15:59:00,096 INFO App moved from apps/healthcare to archived/apps/healthcare-2025-03-04
2025-03-04 15:59:00,107 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-04 15:59:14,988 INFO /home/<USER>/.local/bin/bench setup requirements
2025-03-04 15:59:14,998 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-03-04 15:59:16,047 LOG Installing frappe
2025-03-04 15:59:16,047 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-03-04 15:59:18,637 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-03-04 15:59:20,688 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-03-04 15:59:21,816 LOG Installing twilio_integration
2025-03-04 15:59:21,816 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/twilio_integration 
2025-03-04 15:59:24,144 LOG Installing hrms
2025-03-04 15:59:24,145 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-03-04 15:59:26,405 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-03-04 15:59:38,135 LOG Installing lending
2025-03-04 15:59:38,135 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-03-04 15:59:40,596 LOG Installing biometric_client
2025-03-04 15:59:40,596 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-03-04 15:59:42,889 LOG Installing erpbiometric_sync
2025-03-04 15:59:42,889 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpbiometric_sync 
2025-03-04 15:59:45,476 LOG Installing webshop
2025-03-04 15:59:45,476 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-03-04 15:59:48,570 LOG Installing vfd_providers
2025-03-04 15:59:48,570 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-03-04 15:59:51,605 LOG Installing tenaciousfreightmaster
2025-03-04 15:59:51,605 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenaciousfreightmaster 
2025-03-04 15:59:55,067 LOG Installing airplane_mode
2025-03-04 15:59:55,067 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/airplane_mode 
2025-03-04 15:59:58,095 LOG Installing utility_management
2025-03-04 15:59:58,095 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/utility_management 
2025-03-04 16:00:00,997 LOG Installing csf_tz
2025-03-04 16:00:00,997 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-03-04 16:00:05,707 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-03-04 16:00:06,184 LOG Installing tenacious_integration
2025-03-04 16:00:06,185 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacious_integration 
2025-03-04 16:00:08,884 LOG Installing hms_tz
2025-03-04 16:00:08,884 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-03-04 16:00:11,377 WARNING cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz  executed with exit code 1
2025-03-04 16:00:11,377 WARNING /home/<USER>/.local/bin/bench setup requirements executed with exit code 1
2025-03-04 16:01:42,618 INFO /home/<USER>/.local/bin/bench start
2025-03-04 16:01:54,536 INFO /home/<USER>/.local/bin/bench start
2025-03-04 18:00:01,655 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 00:00:01,841 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 09:14:33,118 INFO /home/<USER>/.local/bin/bench use alphaasociates.co.tz
2025-03-05 09:14:38,372 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:14:38,705 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:14:38,706 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:14:38,764 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:14:38,764 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:18:37,122 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-05 09:18:39,087 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:18:39,307 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:18:39,312 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:18:39,345 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:18:39,349 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:26:55,451 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:26:55,661 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:26:55,692 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:26:55,693 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:26:55,693 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:30:05,222 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:30:05,433 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:30:05,436 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:30:05,484 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:30:05,485 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:30:35,423 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:30:35,628 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:30:35,668 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:30:35,679 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:30:35,681 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:31:38,892 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:31:39,100 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:31:39,103 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:31:39,104 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:31:39,154 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:32:04,412 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:32:04,641 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:32:04,646 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:32:04,659 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:32:04,673 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:32:34,469 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:32:34,667 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:32:34,676 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:32:34,681 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:32:34,733 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:32:58,394 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:32:58,601 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 09:32:58,616 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:32:58,644 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:32:58,658 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:33:30,478 INFO /home/<USER>/.local/bin/bench start
2025-03-05 09:33:30,686 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 09:33:30,691 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 09:33:30,728 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 09:33:30,742 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:15:48,591 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:15:48,867 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:15:48,894 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:15:48,921 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:15:48,921 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:18:46,078 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:18:46,285 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:18:46,305 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:18:46,338 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:18:46,343 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:23:31,072 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:23:31,279 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:23:31,320 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:23:31,324 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:23:31,326 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:29:24,493 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:29:24,705 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:29:24,745 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:29:24,752 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:29:24,753 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:30:20,137 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:30:20,348 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:30:20,351 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:30:20,370 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:30:20,370 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:32:07,736 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:32:07,950 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:32:07,961 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:32:07,993 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:32:07,993 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:33:11,683 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:33:11,904 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:33:11,938 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:33:11,944 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:33:11,947 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:55:40,995 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:55:41,204 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:55:41,233 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:55:41,233 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:55:41,248 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:57:19,684 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:57:19,892 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:57:19,941 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:57:19,942 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 10:57:19,945 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:59:27,260 INFO /home/<USER>/.local/bin/bench start
2025-03-05 10:59:27,582 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 10:59:27,584 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 10:59:27,614 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 10:59:27,614 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 11:19:19,015 INFO /home/<USER>/.local/bin/bench start
2025-03-05 11:19:19,224 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 11:19:19,268 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 11:19:19,268 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 11:19:19,279 INFO /home/<USER>/.local/bin/bench schedule
2025-03-05 12:00:01,621 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 12:08:30,771 INFO /home/<USER>/.local/bin/bench migrate
2025-03-05 18:00:02,267 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 18:01:54,226 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-05 18:02:05,887 INFO /home/<USER>/.local/bin/bench start
2025-03-05 18:02:08,085 INFO /home/<USER>/.local/bin/bench watch
2025-03-05 18:02:08,090 INFO /home/<USER>/.local/bin/bench worker
2025-03-05 18:02:08,136 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-05 18:02:08,139 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 09:45:29,767 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-06 09:45:35,954 INFO /home/<USER>/.local/bin/bench start
2025-03-06 09:45:36,430 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-06 09:45:36,430 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 09:45:36,434 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 09:45:36,434 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 10:05:05,151 INFO /home/<USER>/.local/bin/bench start
2025-03-06 10:05:05,426 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 10:05:05,442 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-06 10:05:05,443 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 10:05:05,443 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 10:25:58,185 INFO /home/<USER>/.local/bin/bench start
2025-03-06 10:25:58,474 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 10:25:58,477 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-06 10:25:58,521 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 10:25:58,521 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 10:55:08,474 INFO /home/<USER>/.local/bin/bench start
2025-03-06 10:55:08,712 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-06 10:55:08,715 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 10:55:08,723 INFO /home/<USER>/.local/bin/bench schedule
2025-03-06 10:55:08,729 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 12:00:01,274 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-06 18:00:01,912 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-06 21:58:59,513 INFO /home/<USER>/.local/bin/bench use integration
2025-03-06 21:59:05,338 INFO /home/<USER>/.local/bin/bench start
2025-03-06 21:59:05,537 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-06 21:59:05,544 INFO /home/<USER>/.local/bin/bench watch
2025-03-06 21:59:05,552 INFO /home/<USER>/.local/bin/bench worker
2025-03-06 21:59:05,553 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 09:48:28,819 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-07 09:48:35,686 INFO /home/<USER>/.local/bin/bench start
2025-03-07 09:48:36,212 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 09:48:36,213 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 09:48:36,227 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 09:48:36,227 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 10:15:46,642 INFO /home/<USER>/.local/bin/bench start
2025-03-07 10:15:46,872 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 10:15:46,873 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 10:15:46,887 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 10:15:46,892 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:03:04,026 INFO /home/<USER>/.local/bin/bench --site alphaasociates.co.tz uninstall-app lending
2025-03-07 11:03:14,701 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-03-07 11:04:23,377 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:04:23,581 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:04:23,623 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:04:23,627 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:04:23,638 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:04:43,383 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending --force
2025-03-07 11:05:39,642 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending --force
2025-03-07 11:06:25,234 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:06:25,480 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:06:25,502 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:06:25,506 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:06:25,508 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:09:12,839 INFO /home/<USER>/.local/bin/bench migarte
2025-03-07 11:09:19,825 INFO /home/<USER>/.local/bin/bench migrate
2025-03-07 11:10:26,461 INFO /home/<USER>/.local/bin/bench migrate
2025-03-07 11:10:40,845 INFO /home/<USER>/.local/bin/bench migrate
2025-03-07 11:10:51,711 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:10:51,934 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:10:51,935 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:10:51,937 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:10:51,985 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:11:07,713 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:11:07,930 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:11:07,958 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:11:07,975 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:11:07,977 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:12:13,635 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/lending.git --branch version-15
2025-03-07 11:12:13,645 LOG Getting lending
2025-03-07 11:12:13,645 DEBUG cd ./apps && git clone https://github.com/frappe/lending.git --branch version-15 --depth 1 --origin upstream
2025-03-07 11:12:20,853 LOG Installing lending
2025-03-07 11:12:20,854 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-03-07 11:12:24,899 DEBUG bench build --app lending
2025-03-07 11:12:25,078 INFO /home/<USER>/.local/bin/bench build --app lending
2025-03-07 11:12:27,086 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-07 11:12:33,136 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:12:33,342 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:12:33,351 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:12:33,388 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:12:33,402 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:13:07,583 INFO /home/<USER>/.local/bin/bench migrate
2025-03-07 11:25:47,881 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-07 11:25:50,969 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:25:51,183 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:25:51,198 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:25:51,208 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:25:51,219 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:26:18,392 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-07 11:26:21,431 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:26:21,642 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:26:21,686 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:26:21,692 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:26:21,693 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:29:45,601 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-07 11:29:48,211 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:29:48,422 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:29:48,457 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:29:48,464 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:29:48,474 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 11:47:40,355 INFO /home/<USER>/.local/bin/bench start
2025-03-07 11:47:40,580 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 11:47:40,581 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 11:47:40,588 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 11:47:40,607 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 12:00:01,445 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-07 12:45:03,103 INFO /home/<USER>/.local/bin/bench start
2025-03-07 12:45:03,435 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 12:45:03,437 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 12:45:03,449 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 12:45:03,488 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 13:07:17,232 INFO /home/<USER>/.local/bin/bench get-app https://github.com/aakvatech/Payware.git --branch version-14
2025-03-07 13:07:17,244 LOG Getting Payware
2025-03-07 13:07:17,244 DEBUG cd ./apps && git clone https://github.com/aakvatech/Payware.git --branch version-14 --depth 1 --origin upstream
2025-03-07 13:07:19,292 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/aakvatech/Payware.git --branch version-14 executed with exit code 1
2025-03-07 13:07:41,308 INFO /home/<USER>/.local/bin/bench get-app https://github.com/aakvatech/Payware.git --branch version-14
2025-03-07 13:07:43,136 INFO App moved from apps/Payware to archived/apps/Payware-2025-03-07
2025-03-07 13:07:43,139 LOG Getting Payware
2025-03-07 13:07:43,139 DEBUG cd ./apps && git clone https://github.com/aakvatech/Payware.git --branch version-14 --depth 1 --origin upstream
2025-03-07 13:07:45,202 LOG Installing payware
2025-03-07 13:07:45,203 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-03-07 13:07:48,559 DEBUG bench build --app payware
2025-03-07 13:07:48,717 INFO /home/<USER>/.local/bin/bench build --app payware
2025-03-07 13:07:51,055 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-07 13:08:24,884 INFO /home/<USER>/.local/bin/bench start
2025-03-07 13:08:25,238 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 13:08:25,246 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 13:08:25,246 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 13:08:25,248 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 16:05:57,865 INFO /home/<USER>/.local/bin/bench use cetifications
2025-03-07 16:06:01,405 INFO /home/<USER>/.local/bin/bench start
2025-03-07 16:06:01,813 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 16:06:01,817 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 16:06:01,828 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 16:06:01,845 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 16:07:12,807 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-07 16:07:16,994 INFO /home/<USER>/.local/bin/bench start
2025-03-07 16:07:17,616 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 16:07:17,635 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 16:07:17,638 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 16:07:17,657 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 17:00:23,002 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-07 17:00:25,321 INFO /home/<USER>/.local/bin/bench start
2025-03-07 17:00:25,787 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 17:00:25,793 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 17:00:25,800 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 17:00:25,824 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 17:50:52,309 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-07 17:50:59,416 INFO /home/<USER>/.local/bin/bench start
2025-03-07 17:51:00,042 INFO /home/<USER>/.local/bin/bench watch
2025-03-07 17:51:00,048 INFO /home/<USER>/.local/bin/bench worker
2025-03-07 17:51:00,086 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-07 17:51:00,087 INFO /home/<USER>/.local/bin/bench schedule
2025-03-07 18:00:01,634 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-08 00:00:02,136 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-10 09:09:14,755 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-10 09:09:21,579 INFO /home/<USER>/.local/bin/bench start
2025-03-10 09:09:22,043 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 09:09:22,047 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-10 09:09:22,100 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 09:09:22,103 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 12:00:02,545 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-10 14:06:07,647 INFO /home/<USER>/.local/bin/bench use integration
2025-03-10 14:06:11,126 INFO /home/<USER>/.local/bin/bench start
2025-03-10 14:06:11,364 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-10 14:06:11,366 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 14:06:11,366 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 14:06:11,368 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 14:46:33,931 INFO /home/<USER>/.local/bin/bench start
2025-03-10 14:46:34,187 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 14:46:34,188 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 14:46:34,189 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 14:46:34,235 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-10 18:00:02,444 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-10 18:17:42,161 INFO /home/<USER>/.local/bin/bench use integration
2025-03-10 18:17:45,875 INFO /home/<USER>/.local/bin/bench start
2025-03-10 18:17:46,104 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 18:17:46,106 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 18:17:46,139 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 18:17:46,152 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-10 18:45:43,572 INFO /home/<USER>/.local/bin/bench start
2025-03-10 18:45:43,779 INFO /home/<USER>/.local/bin/bench watch
2025-03-10 18:45:43,807 INFO /home/<USER>/.local/bin/bench schedule
2025-03-10 18:45:43,808 INFO /home/<USER>/.local/bin/bench worker
2025-03-10 18:45:43,831 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 00:30:30,921 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-11 00:30:42,511 INFO /home/<USER>/.local/bin/bench start
2025-03-11 00:30:42,929 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 00:30:42,950 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 00:30:42,951 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 00:30:42,955 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 09:16:49,837 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-11 09:16:56,573 INFO /home/<USER>/.local/bin/bench start
2025-03-11 09:16:57,136 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 09:16:57,142 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 09:16:57,167 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 09:16:57,167 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 10:01:19,252 INFO /home/<USER>/.local/bin/bench use integration
2025-03-11 10:01:22,107 INFO /home/<USER>/.local/bin/bench start
2025-03-11 10:01:22,312 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 10:01:22,337 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 10:01:22,364 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 10:01:22,370 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 10:05:52,775 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 10:16:47,515 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-11 10:16:50,111 INFO /home/<USER>/.local/bin/bench start
2025-03-11 10:16:50,326 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 10:16:50,326 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 10:16:50,360 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 10:16:50,375 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 10:24:50,962 INFO /home/<USER>/.local/bin/bench use integration
2025-03-11 10:24:52,877 INFO /home/<USER>/.local/bin/bench start
2025-03-11 10:24:53,095 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 10:24:53,099 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 10:24:53,142 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 10:24:53,143 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 10:55:29,681 INFO /home/<USER>/.local/bin/bench console
2025-03-11 10:58:38,923 INFO /home/<USER>/.local/bin/bench logs
2025-03-11 10:58:52,244 INFO /home/<USER>/.local/bin/bench --site integration logs
2025-03-11 11:00:07,491 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-11 11:00:09,630 INFO /home/<USER>/.local/bin/bench start
2025-03-11 11:00:09,844 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 11:00:09,886 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 11:00:09,887 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 11:00:09,893 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 11:00:22,721 INFO /home/<USER>/.local/bin/bench console
2025-03-11 11:01:26,690 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app tenacious_integration
2025-03-11 11:01:44,184 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 11:05:59,459 INFO /home/<USER>/.local/bin/bench console
2025-03-11 11:32:03,667 INFO /home/<USER>/.local/bin/bench console
2025-03-11 12:00:01,879 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-11 13:24:25,443 INFO /home/<USER>/.local/bin/bench console
2025-03-11 13:32:54,648 INFO /home/<USER>/.local/bin/bench start
2025-03-11 13:32:55,069 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 13:32:55,074 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 13:32:55,081 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 13:32:55,095 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 13:42:34,392 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 13:47:53,405 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 13:55:01,176 INFO /home/<USER>/.local/bin/bench console
2025-03-11 16:16:20,286 INFO /home/<USER>/.local/bin/bench start
2025-03-11 16:16:20,546 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 16:16:20,561 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 16:16:20,561 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 16:16:20,593 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 16:23:38,897 INFO /home/<USER>/.local/bin/bench start
2025-03-11 16:23:39,118 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 16:23:39,123 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 16:23:39,133 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 16:23:39,177 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 16:44:00,366 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-11 16:44:01,942 INFO /home/<USER>/.local/bin/bench start
2025-03-11 16:44:02,166 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 16:44:02,168 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 16:44:02,213 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 16:44:02,219 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 17:03:21,893 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-11 17:03:23,928 INFO /home/<USER>/.local/bin/bench start
2025-03-11 17:03:24,163 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 17:03:24,166 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 17:03:24,200 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 17:03:24,209 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 17:33:02,613 INFO /home/<USER>/.local/bin/bench start
2025-03-11 17:33:02,832 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 17:33:02,835 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 17:33:02,868 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 17:33:02,874 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 17:33:12,032 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 18:00:01,183 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-11 18:13:38,248 INFO /home/<USER>/.local/bin/bench start
2025-03-11 18:13:38,467 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 18:13:38,470 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 18:13:38,500 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 18:13:38,513 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 18:13:42,041 INFO /home/<USER>/.local/bin/bench migrate
2025-03-11 21:52:52,929 INFO /home/<USER>/.local/bin/bench usecertifications
2025-03-11 21:53:00,965 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-11 21:53:07,719 INFO /home/<USER>/.local/bin/bench start
2025-03-11 21:53:07,997 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 21:53:07,998 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 21:53:08,002 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 21:53:08,008 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 22:24:30,407 INFO /home/<USER>/.local/bin/bench start
2025-03-11 22:24:30,633 INFO /home/<USER>/.local/bin/bench schedule
2025-03-11 22:24:30,638 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 22:24:30,689 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 22:24:30,705 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 23:12:57,631 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-11 23:13:08,759 INFO /home/<USER>/.local/bin/bench start
2025-03-11 23:13:09,763 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-11 23:13:09,765 INFO /home/<USER>/.local/bin/bench watch
2025-03-11 23:13:09,779 INFO /home/<USER>/.local/bin/bench worker
2025-03-11 23:13:09,807 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 10:11:17,189 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-12 10:11:21,324 INFO /home/<USER>/.local/bin/bench start
2025-03-12 10:11:21,556 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 10:11:21,564 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 10:11:21,605 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 10:11:21,618 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 12:00:01,459 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-12 12:01:08,975 INFO /home/<USER>/.local/bin/bench start
2025-03-12 12:01:09,499 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 12:01:09,519 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 12:01:09,523 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 12:01:09,525 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 12:16:00,266 INFO /home/<USER>/.local/bin/bench start
2025-03-12 12:16:00,633 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 12:16:00,636 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 12:16:00,681 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 12:16:00,683 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 12:22:44,351 INFO /home/<USER>/.local/bin/bench start
2025-03-12 12:22:44,635 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 12:22:44,649 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 12:22:44,690 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 12:22:44,693 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 14:18:50,361 INFO /home/<USER>/.local/bin/bench start
2025-03-12 14:18:50,703 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 14:18:50,709 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 14:18:50,758 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 14:18:50,763 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 14:52:19,452 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-12 14:52:24,567 INFO /home/<USER>/.local/bin/bench start
2025-03-12 14:52:24,882 INFO /home/<USER>/.local/bin/bench worker
2025-03-12 14:52:24,887 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-12 14:52:24,909 INFO /home/<USER>/.local/bin/bench watch
2025-03-12 14:52:24,911 INFO /home/<USER>/.local/bin/bench schedule
2025-03-12 18:00:02,121 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-13 00:00:01,871 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-13 09:16:00,717 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-13 09:16:04,519 INFO /home/<USER>/.local/bin/bench start
2025-03-13 09:16:04,822 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 09:16:04,830 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 09:16:04,867 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 09:16:04,881 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 10:49:51,979 INFO /home/<USER>/.local/bin/bench start
2025-03-13 10:49:52,262 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 10:49:52,264 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 10:49:52,270 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 10:49:52,274 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 11:52:09,748 INFO /home/<USER>/.local/bin/bench start
2025-03-13 11:52:10,023 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 11:52:10,023 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 11:52:10,029 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 11:52:10,039 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 11:59:11,498 INFO /home/<USER>/.local/bin/bench start
2025-03-13 11:59:11,770 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 11:59:11,772 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 11:59:11,776 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 11:59:11,806 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 12:00:01,806 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-13 15:31:42,009 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-13 15:31:44,518 INFO /home/<USER>/.local/bin/bench start
2025-03-13 15:31:44,855 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 15:31:44,859 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 15:31:44,885 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 15:31:44,885 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 16:21:22,154 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-13 16:21:24,619 INFO /home/<USER>/.local/bin/bench start
2025-03-13 16:21:24,978 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 16:21:25,004 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 16:21:25,025 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 16:21:25,027 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 17:35:26,498 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-13 17:35:28,438 INFO /home/<USER>/.local/bin/bench start
2025-03-13 17:35:28,694 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-13 17:35:28,699 INFO /home/<USER>/.local/bin/bench watch
2025-03-13 17:35:28,730 INFO /home/<USER>/.local/bin/bench schedule
2025-03-13 17:35:28,741 INFO /home/<USER>/.local/bin/bench worker
2025-03-13 18:00:01,674 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 10:13:16,967 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-14 10:13:23,585 INFO /home/<USER>/.local/bin/bench start
2025-03-14 10:13:23,871 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 10:13:23,871 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 10:13:23,883 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 10:13:23,884 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 11:24:37,589 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-14 11:24:45,247 INFO /home/<USER>/.local/bin/bench start
2025-03-14 11:24:45,640 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 11:24:45,644 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 11:24:45,689 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 11:24:45,693 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 11:35:07,980 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-14 11:35:10,930 INFO /home/<USER>/.local/bin/bench start
2025-03-14 11:35:11,150 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 11:35:11,151 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 11:35:11,160 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 11:35:11,179 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 12:00:01,536 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 13:15:28,325 INFO /home/<USER>/.local/bin/bench use integration
2025-03-14 13:15:30,434 INFO /home/<USER>/.local/bin/bench start
2025-03-14 13:15:30,911 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 13:15:30,912 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 13:15:30,944 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 13:15:30,946 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 13:16:46,918 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-14 13:16:48,775 INFO /home/<USER>/.local/bin/bench start
2025-03-14 13:16:49,001 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 13:16:49,001 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 13:16:49,004 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 13:16:49,060 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 14:07:36,154 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-14 14:07:37,781 INFO /home/<USER>/.local/bin/bench start
2025-03-14 14:07:38,081 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 14:07:38,083 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 14:07:38,125 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 14:07:38,131 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 14:22:06,271 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-14 14:22:08,470 INFO /home/<USER>/.local/bin/bench start
2025-03-14 14:22:08,830 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 14:22:08,834 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 14:22:08,844 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 14:22:08,849 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 17:51:54,916 INFO /home/<USER>/.local/bin/bench use integration
2025-03-14 17:51:59,141 INFO /home/<USER>/.local/bin/bench start
2025-03-14 17:51:59,541 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 17:51:59,545 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 17:51:59,555 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 17:51:59,560 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:00:01,844 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 18:15:57,679 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-14 18:16:00,867 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:16:01,173 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:16:01,178 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:16:01,190 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:16:01,196 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:18:47,427 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:18:47,640 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:18:47,692 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:18:47,695 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:18:47,696 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:22:46,509 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:22:46,722 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:22:46,767 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:22:46,769 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:22:46,774 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:24:14,042 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:24:14,256 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:24:14,264 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:24:14,268 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:24:14,320 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:28:16,993 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:28:17,201 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:28:17,250 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:28:17,251 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:28:17,254 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:32:15,446 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:32:15,694 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:32:15,695 INFO /home/<USER>/.local/bin/bench worker
2025-03-14 18:32:15,697 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:32:15,713 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:33:51,729 INFO /home/<USER>/.local/bin/bench start
2025-03-14 18:33:51,946 INFO /home/<USER>/.local/bin/bench watch
2025-03-14 18:33:51,960 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-14 18:33:51,983 INFO /home/<USER>/.local/bin/bench schedule
2025-03-14 18:33:51,986 INFO /home/<USER>/.local/bin/bench worker
2025-03-15 21:47:38,264 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-15 21:47:51,181 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-15 21:47:56,109 INFO /home/<USER>/.local/bin/bench start
2025-03-15 21:47:56,662 INFO /home/<USER>/.local/bin/bench watch
2025-03-15 21:47:56,666 INFO /home/<USER>/.local/bin/bench schedule
2025-03-15 21:47:56,687 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-15 21:47:56,689 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 09:41:54,165 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-17 09:41:59,302 INFO /home/<USER>/.local/bin/bench start
2025-03-17 09:41:59,601 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 09:41:59,616 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 09:41:59,629 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 09:41:59,658 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 12:00:01,863 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-17 12:34:06,855 INFO /home/<USER>/.local/bin/bench console
2025-03-17 13:00:49,243 INFO /home/<USER>/.local/bin/bench console
2025-03-17 13:30:23,202 INFO /home/<USER>/.local/bin/bench use integration
2025-03-17 13:30:24,854 INFO /home/<USER>/.local/bin/bench start
2025-03-17 13:30:25,122 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 13:30:25,123 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 13:30:25,162 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 13:30:25,164 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 13:48:28,555 INFO /home/<USER>/.local/bin/bench start
2025-03-17 13:48:28,769 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 13:48:28,777 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 13:48:28,783 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 13:48:28,804 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 13:50:36,135 INFO /home/<USER>/.local/bin/bench start
2025-03-17 13:50:36,358 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 13:50:36,368 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 13:50:36,374 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 13:50:36,384 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 13:53:26,184 INFO /home/<USER>/.local/bin/bench console
2025-03-17 14:07:10,048 INFO /home/<USER>/.local/bin/bench start
2025-03-17 14:07:10,303 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 14:07:10,309 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 14:07:10,326 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 14:07:10,326 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 14:13:58,592 INFO /home/<USER>/.local/bin/bench start
2025-03-17 14:13:58,819 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 14:13:58,834 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 14:13:58,834 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 14:13:58,855 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 14:23:34,743 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-17 14:23:36,723 INFO /home/<USER>/.local/bin/bench start
2025-03-17 14:23:36,954 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 14:23:36,962 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 14:23:37,000 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 14:23:37,014 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 14:24:46,549 INFO /home/<USER>/.local/bin/bench migrate
2025-03-17 14:25:35,656 INFO /home/<USER>/.local/bin/bench console
2025-03-17 16:36:41,279 INFO /home/<USER>/.local/bin/bench start
2025-03-17 16:36:41,832 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 16:36:41,833 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 16:36:41,833 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 16:36:41,835 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 16:41:44,914 INFO /home/<USER>/.local/bin/bench start
2025-03-17 16:41:45,385 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 16:41:45,389 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 16:41:45,390 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 16:41:45,415 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 16:46:11,159 INFO /home/<USER>/.local/bin/bench start
2025-03-17 16:46:11,624 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 16:46:11,635 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 16:46:11,651 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 16:46:11,654 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 16:51:34,409 INFO /home/<USER>/.local/bin/bench start
2025-03-17 16:51:34,864 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 16:51:34,890 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 16:51:34,911 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 16:51:34,920 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 17:02:50,316 INFO /home/<USER>/.local/bin/bench start
2025-03-17 17:02:50,597 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 17:02:50,600 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 17:02:50,619 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 17:02:50,622 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 17:20:01,691 INFO /home/<USER>/.local/bin/bench start
2025-03-17 17:20:02,092 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 17:20:02,096 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 17:20:02,156 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 17:20:02,159 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 18:00:02,728 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-17 18:15:11,119 INFO /home/<USER>/.local/bin/bench start
2025-03-17 18:15:11,834 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 18:15:11,836 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 18:15:11,837 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 18:15:11,840 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 18:43:26,399 INFO /home/<USER>/.local/bin/bench start
2025-03-17 18:43:27,085 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 18:43:27,086 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 18:43:27,087 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 18:43:27,087 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 23:13:36,208 INFO /home/<USER>/.local/bin/bench use certificatons
2025-03-17 23:13:41,261 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-17 23:13:45,212 INFO /home/<USER>/.local/bin/bench start
2025-03-17 23:13:45,490 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 23:13:45,490 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 23:13:45,491 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 23:13:45,495 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 23:15:17,108 INFO /home/<USER>/.local/bin/bench migrate
2025-03-17 23:40:44,785 INFO /home/<USER>/.local/bin/bench start
2025-03-17 23:40:45,001 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 23:40:45,003 INFO /home/<USER>/.local/bin/bench watch
2025-03-17 23:40:45,005 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 23:40:45,052 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 23:53:03,085 INFO /home/<USER>/.local/bin/bench start
2025-03-17 23:53:03,319 INFO /home/<USER>/.local/bin/bench worker
2025-03-17 23:53:03,322 INFO /home/<USER>/.local/bin/bench schedule
2025-03-17 23:53:03,389 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-17 23:53:03,389 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:00:02,048 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-18 00:13:24,326 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:13:24,617 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:13:24,622 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:13:24,695 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:13:24,696 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:23:00,396 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:23:00,668 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:23:00,692 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:23:00,742 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:23:00,751 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:29:51,944 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:29:52,188 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:29:52,197 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:29:52,233 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:29:52,235 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:30:46,799 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:30:47,040 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:30:47,042 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:30:47,076 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:30:47,080 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:31:45,897 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:31:46,133 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:31:46,134 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:31:46,146 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:31:46,157 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:46:26,387 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:46:26,607 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 00:46:26,609 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:46:26,623 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:46:26,625 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:55:33,430 INFO /home/<USER>/.local/bin/bench start
2025-03-18 00:55:33,648 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 00:55:33,651 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 00:55:33,702 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 00:55:33,702 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 01:17:44,459 INFO /home/<USER>/.local/bin/bench start
2025-03-18 01:17:44,699 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 01:17:44,706 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 01:17:44,723 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 01:17:44,730 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 02:02:00,455 INFO /home/<USER>/.local/bin/bench start
2025-03-18 02:02:00,670 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 02:02:00,716 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 02:02:00,718 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 02:02:00,720 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 02:02:32,317 INFO /home/<USER>/.local/bin/bench console
2025-03-18 02:10:21,719 INFO /home/<USER>/.local/bin/bench start
2025-03-18 02:10:21,934 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 02:10:21,940 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 02:10:21,980 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 02:10:21,989 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 02:24:17,037 INFO /home/<USER>/.local/bin/bench start
2025-03-18 02:24:17,272 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 02:24:17,278 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 02:24:17,293 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 02:24:17,313 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 02:25:50,847 INFO /home/<USER>/.local/bin/bench console
2025-03-18 09:36:12,735 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-18 09:36:18,193 INFO /home/<USER>/.local/bin/bench start
2025-03-18 09:36:19,270 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 09:36:19,276 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 09:36:19,289 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 09:36:19,289 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 09:38:27,686 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-18 09:38:31,626 INFO /home/<USER>/.local/bin/bench start
2025-03-18 09:38:31,834 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 09:38:31,856 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 09:38:31,885 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 09:38:31,891 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 10:09:47,891 INFO /home/<USER>/.local/bin/bench start
2025-03-18 10:09:48,186 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 10:09:48,201 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 10:09:48,221 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 10:09:48,228 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 10:16:08,490 INFO /home/<USER>/.local/bin/bench start
2025-03-18 10:16:08,762 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 10:16:08,766 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 10:16:08,819 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 10:16:08,819 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 10:49:26,524 INFO /home/<USER>/.local/bin/bench console
2025-03-18 10:51:17,653 INFO /home/<USER>/.local/bin/bench start
2025-03-18 10:51:17,996 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 10:51:17,998 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 10:51:18,062 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 10:51:18,072 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 12:00:01,520 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-18 15:00:44,837 INFO /home/<USER>/.local/bin/bench migrate
2025-03-18 16:07:50,364 INFO /home/<USER>/.local/bin/bench use masumin
2025-03-18 16:07:52,670 INFO /home/<USER>/.local/bin/bench start
2025-03-18 16:07:53,254 INFO /home/<USER>/.local/bin/bench watch
2025-03-18 16:07:53,265 INFO /home/<USER>/.local/bin/bench schedule
2025-03-18 16:07:53,284 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-18 16:07:53,301 INFO /home/<USER>/.local/bin/bench worker
2025-03-18 16:17:04,619 INFO /home/<USER>/.local/bin/bench migrate
2025-03-18 18:00:02,646 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-19 09:27:53,894 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-19 09:28:04,983 INFO /home/<USER>/.local/bin/bench start
2025-03-19 09:28:05,918 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 09:28:05,921 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 09:28:05,963 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 09:28:05,977 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 09:37:16,427 INFO /home/<USER>/.local/bin/bench start
2025-03-19 09:37:16,683 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 09:37:16,692 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 09:37:16,699 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 09:37:16,720 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 10:22:21,744 INFO /home/<USER>/.local/bin/bench start
2025-03-19 10:22:22,013 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 10:22:22,049 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 10:22:22,051 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 10:22:22,062 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 10:27:42,312 INFO /home/<USER>/.local/bin/bench start
2025-03-19 10:27:42,544 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 10:27:42,573 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 10:27:42,586 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 10:27:42,599 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 10:41:03,893 INFO /home/<USER>/.local/bin/bench start
2025-03-19 10:41:04,101 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 10:41:04,104 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 10:41:04,105 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 10:41:04,161 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:23:01,438 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:24:12,504 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:26:03,016 INFO /home/<USER>/.local/bin/bench start
2025-03-19 11:26:03,234 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 11:26:03,277 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 11:26:03,281 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 11:26:03,286 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:26:05,986 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:38:00,856 INFO /home/<USER>/.local/bin/bench start
2025-03-19 11:38:01,079 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 11:38:01,081 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 11:38:01,087 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 11:38:01,123 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 11:38:04,349 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:38:42,027 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:39:10,504 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:41:51,122 INFO /home/<USER>/.local/bin/bench console
2025-03-19 11:42:38,755 INFO /home/<USER>/.local/bin/bench --site certifications execute airplane_mode.rentals.doctype.payment_schedule.payment_schedule.send_rent_reminders
2025-03-19 11:43:06,496 INFO /home/<USER>/.local/bin/bench --site certifications execute airplane_mode.airport_shop_management.doctype.payment_schedule.payment_schedule.send_rent_reminders
2025-03-19 11:43:33,328 INFO /home/<USER>/.local/bin/bench --site certifications execute airplane_mode.airport_shop_management.doctype.payment_schedule.payment_schedule.send_rent_reminders
2025-03-19 11:45:53,126 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 11:46:21,846 INFO /home/<USER>/.local/bin/bench --site certifications execute airplane_mode.airport_shop_management.doctype.payment_schedule.payment_schedule.send_rent_reminders
2025-03-19 11:47:07,806 INFO /home/<USER>/.local/bin/bench console
2025-03-19 12:00:01,785 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-19 12:07:35,965 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 12:09:10,911 INFO /home/<USER>/.local/bin/bench console
2025-03-19 14:11:56,915 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:11:57,131 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:11:57,150 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:11:57,160 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:11:57,189 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:17:12,191 INFO /home/<USER>/.local/bin/bench console
2025-03-19 14:17:25,954 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:17:26,196 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:17:26,199 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:17:26,223 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:17:26,229 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:23:21,295 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:23:21,513 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:23:21,520 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:23:21,526 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:23:21,560 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:23:38,884 INFO /home/<USER>/.local/bin/bench console
2025-03-19 14:31:43,658 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:31:43,882 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:31:43,883 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:31:43,929 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:31:43,933 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:31:57,769 INFO /home/<USER>/.local/bin/bench console
2025-03-19 14:33:45,206 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:33:45,533 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:33:45,534 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:33:45,536 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:33:45,572 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:33:49,839 INFO /home/<USER>/.local/bin/bench console
2025-03-19 14:45:13,578 INFO /home/<USER>/.local/bin/bench use gadget
2025-03-19 14:45:15,342 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:45:15,560 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:45:15,612 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:45:15,628 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 14:45:15,641 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:48:05,807 INFO /home/<USER>/.local/bin/bench use masumin
2025-03-19 14:48:07,515 INFO /home/<USER>/.local/bin/bench start
2025-03-19 14:48:07,731 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 14:48:07,774 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 14:48:07,780 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 14:48:07,780 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 15:27:47,712 INFO /home/<USER>/.local/bin/bench build
2025-03-19 16:08:01,591 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-19 16:08:05,137 INFO /home/<USER>/.local/bin/bench start
2025-03-19 16:08:05,760 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 16:08:05,761 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 16:08:05,801 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 16:08:05,815 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 16:14:23,405 INFO /home/<USER>/.local/bin/bench console
2025-03-19 16:58:12,019 INFO /home/<USER>/.local/bin/bench start
2025-03-19 16:58:12,291 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 16:58:12,294 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 16:58:12,297 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 16:58:12,297 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 17:04:52,439 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 17:07:11,151 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 17:13:15,641 INFO /home/<USER>/.local/bin/bench console
2025-03-19 17:44:10,983 INFO /home/<USER>/.local/bin/bench start
2025-03-19 17:44:11,220 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 17:44:11,240 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 17:44:11,244 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 17:44:11,267 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 17:44:17,146 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 17:47:13,050 INFO /home/<USER>/.local/bin/bench console
2025-03-19 17:58:52,446 INFO /home/<USER>/.local/bin/bench start
2025-03-19 17:58:52,683 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 17:58:52,689 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 17:58:52,690 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 17:58:52,737 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:00:01,593 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-19 18:00:01,873 INFO /home/<USER>/.local/bin/bench console
2025-03-19 18:00:08,320 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:06:13,379 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:06:13,660 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:06:13,665 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:06:13,669 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:06:13,669 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:07:20,427 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:07:51,083 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:12:40,658 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:12:40,924 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:12:40,943 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:12:40,959 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:12:40,966 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:12:45,194 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:17:44,893 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:17:45,121 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:17:45,124 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:17:45,131 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:17:45,168 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:18:05,711 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:26:54,691 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:26:54,942 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:26:54,943 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:26:54,948 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:26:54,984 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:27:00,007 INFO /home/<USER>/.local/bin/bench console
2025-03-19 18:28:56,658 INFO /home/<USER>/.local/bin/bench console
2025-03-19 18:33:54,099 INFO /home/<USER>/.local/bin/bench console
2025-03-19 18:41:21,815 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:41:22,024 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:41:22,067 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:41:22,069 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:41:22,082 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:42:50,672 INFO /home/<USER>/.local/bin/bench migrate
2025-03-19 18:44:45,282 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:44:45,502 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:44:45,534 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:44:45,551 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:44:45,564 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:47:46,076 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:47:46,307 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:47:46,337 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:47:46,341 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:47:46,350 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:51:11,018 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:51:11,251 INFO /home/<USER>/.local/bin/bench worker
2025-03-19 18:51:11,264 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:51:11,296 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:51:11,298 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:55:00,577 INFO /home/<USER>/.local/bin/bench start
2025-03-19 18:55:00,809 INFO /home/<USER>/.local/bin/bench watch
2025-03-19 18:55:00,828 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-19 18:55:00,845 INFO /home/<USER>/.local/bin/bench schedule
2025-03-19 18:55:00,847 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 10:22:31,070 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-20 10:22:37,340 INFO /home/<USER>/.local/bin/bench start
2025-03-20 10:22:37,741 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 10:22:37,741 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 10:22:37,767 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 10:22:37,770 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 11:18:12,568 INFO /home/<USER>/.local/bin/bench use masumin
2025-03-20 11:18:29,884 INFO /home/<USER>/.local/bin/bench start
2025-03-20 11:18:30,271 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 11:18:30,279 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 11:18:30,279 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 11:18:30,282 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 11:19:44,666 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-20 11:20:24,338 INFO /home/<USER>/.local/bin/bench start
2025-03-20 11:20:24,644 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 11:20:24,669 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 11:20:24,676 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 11:20:24,676 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 11:38:59,959 INFO /home/<USER>/.local/bin/bench console
2025-03-20 12:00:01,211 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-20 12:20:43,688 INFO /home/<USER>/.local/bin/bench start
2025-03-20 12:20:44,164 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 12:20:44,168 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 12:20:44,173 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 12:20:44,175 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 14:37:12,910 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-20 14:37:18,557 INFO /home/<USER>/.local/bin/bench start
2025-03-20 14:37:18,932 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 14:37:18,936 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 14:37:18,968 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 14:37:18,974 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 17:49:44,858 INFO /home/<USER>/.local/bin/bench start
2025-03-20 17:49:45,514 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 17:49:45,516 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 17:49:45,556 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 17:49:45,559 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 17:54:48,845 INFO /home/<USER>/.local/bin/bench start
2025-03-20 17:54:49,662 INFO /home/<USER>/.local/bin/bench worker
2025-03-20 17:54:49,662 INFO /home/<USER>/.local/bin/bench watch
2025-03-20 17:54:49,705 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-20 17:54:49,707 INFO /home/<USER>/.local/bin/bench schedule
2025-03-20 18:00:10,445 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-21 00:00:02,441 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-21 09:39:38,753 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-21 09:39:46,316 INFO /home/<USER>/.local/bin/bench start
2025-03-21 09:39:46,578 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 09:39:46,580 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 09:39:46,588 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 09:39:46,635 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 09:40:38,820 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/flexible-budget.git
2025-03-21 09:40:38,830 LOG Getting flexible-budget
2025-03-21 09:40:38,830 DEBUG cd ./apps && git clone https://github.com/j0sh01/flexible-budget.git  --depth 1 --origin upstream
2025-03-21 09:41:33,124 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/flexible-budget.git
2025-03-21 09:41:33,139 LOG Getting flexible-budget
2025-03-21 09:41:33,139 DEBUG cd ./apps && git clone https://github.com/j0sh01/flexible-budget.git  --depth 1 --origin upstream
2025-03-21 09:41:35,385 LOG Installing flexible_budget
2025-03-21 09:41:35,386 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-03-21 09:41:38,688 DEBUG bench build --app flexible_budget
2025-03-21 09:41:38,839 INFO /home/<USER>/.local/bin/bench build --app flexible_budget
2025-03-21 09:41:40,709 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-21 09:42:07,210 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app flexible_budget
2025-03-21 09:42:24,988 INFO /home/<USER>/.local/bin/bench migrate
2025-03-21 09:42:48,334 INFO /home/<USER>/.local/bin/bench start
2025-03-21 09:42:48,549 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 09:42:48,562 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 09:42:48,605 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 09:42:48,605 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 09:43:00,934 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app flexible_budget
2025-03-21 09:43:04,486 INFO /home/<USER>/.local/bin/bench migrate
2025-03-21 10:51:21,307 INFO /home/<USER>/.local/bin/bench start
2025-03-21 10:51:21,552 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 10:51:21,555 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 10:51:21,570 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 10:51:21,587 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 12:00:01,204 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-21 16:23:42,188 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-21 16:23:46,899 INFO /home/<USER>/.local/bin/bench start
2025-03-21 16:23:47,372 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 16:23:47,375 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 16:23:47,407 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 16:23:47,413 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 16:42:38,764 INFO /home/<USER>/.local/bin/bench use wasco
2025-03-21 16:42:45,423 INFO /home/<USER>/.local/bin/bench start
2025-03-21 16:42:45,697 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 16:42:45,706 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 16:42:45,736 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 16:42:45,740 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 16:52:01,896 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-21 16:52:03,374 INFO /home/<USER>/.local/bin/bench start
2025-03-21 16:52:03,778 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-21 16:52:03,786 INFO /home/<USER>/.local/bin/bench worker
2025-03-21 16:52:03,788 INFO /home/<USER>/.local/bin/bench watch
2025-03-21 16:52:03,797 INFO /home/<USER>/.local/bin/bench schedule
2025-03-21 18:00:01,908 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-23 16:21:29,916 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-23 16:21:35,785 INFO /home/<USER>/.local/bin/bench start
2025-03-23 16:21:36,085 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-23 16:21:36,102 INFO /home/<USER>/.local/bin/bench schedule
2025-03-23 16:21:36,124 INFO /home/<USER>/.local/bin/bench worker
2025-03-23 16:21:36,157 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 09:16:19,840 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-25 09:16:24,852 INFO /home/<USER>/.local/bin/bench start
2025-03-25 09:16:25,149 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-25 09:16:25,154 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 09:16:25,179 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 09:16:25,179 INFO /home/<USER>/.local/bin/bench schedule
2025-03-25 09:22:20,917 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-25 09:22:23,858 INFO /home/<USER>/.local/bin/bench start
2025-03-25 09:22:24,072 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 09:22:24,138 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-25 09:22:24,143 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 09:22:24,149 INFO /home/<USER>/.local/bin/bench schedule
2025-03-25 09:38:15,676 INFO /home/<USER>/.local/bin/bench migarte
2025-03-25 09:38:25,939 INFO /home/<USER>/.local/bin/bench migrate
2025-03-25 11:13:00,552 INFO /home/<USER>/.local/bin/bench migrate
2025-03-25 11:21:34,286 INFO /home/<USER>/.local/bin/bench console
2025-03-25 12:00:01,330 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 15:21:02,003 INFO /home/<USER>/.local/bin/bench start
2025-03-25 15:21:02,597 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-25 15:21:02,600 INFO /home/<USER>/.local/bin/bench schedule
2025-03-25 15:21:02,639 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 15:21:02,639 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 18:00:02,900 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 21:49:47,013 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-25 21:49:54,804 INFO /home/<USER>/.local/bin/bench start
2025-03-25 21:49:55,289 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 21:49:55,291 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 21:49:55,313 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-25 21:49:55,314 INFO /home/<USER>/.local/bin/bench schedule
2025-03-25 22:02:55,778 INFO /home/<USER>/.local/bin/bench start
2025-03-25 22:02:55,997 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-25 22:02:56,007 INFO /home/<USER>/.local/bin/bench worker
2025-03-25 22:02:56,045 INFO /home/<USER>/.local/bin/bench watch
2025-03-25 22:02:56,049 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 00:00:01,513 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-26 08:50:33,934 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-26 08:50:40,950 INFO /home/<USER>/.local/bin/bench start
2025-03-26 08:50:41,368 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 08:50:41,404 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 08:50:41,427 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 08:50:41,434 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 09:21:39,826 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 09:30:51,242 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-26 09:30:53,207 INFO /home/<USER>/.local/bin/bench start
2025-03-26 09:30:53,613 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 09:30:53,614 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 09:30:53,615 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 09:30:53,624 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 10:28:16,266 INFO /home/<USER>/.local/bin/bench console
2025-03-26 10:33:21,531 INFO /home/<USER>/.local/bin/bench start
2025-03-26 10:33:21,831 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 10:33:21,834 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 10:33:21,851 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 10:33:21,857 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 10:36:38,268 INFO /home/<USER>/.local/bin/bench start
2025-03-26 10:36:38,597 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 10:36:38,612 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 10:36:38,626 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 10:36:38,638 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 10:42:20,248 INFO /home/<USER>/.local/bin/bench start
2025-03-26 10:42:20,497 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 10:42:20,524 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 10:42:20,544 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 10:42:20,561 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:32:06,410 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:32:07,087 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:32:07,089 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:32:07,090 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:32:07,090 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:33:33,506 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:33:34,189 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:33:34,193 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:33:34,234 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:33:34,235 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:34:57,962 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:34:58,552 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:34:58,554 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:34:58,604 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:34:58,607 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:38:51,155 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:38:51,795 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:38:51,797 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:38:51,797 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:38:51,798 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:40:26,095 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:40:26,771 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:40:26,789 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:40:26,852 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:40:26,857 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:42:03,699 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:42:04,385 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:42:04,391 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:42:04,434 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:42:04,436 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 11:43:59,598 INFO /home/<USER>/.local/bin/bench start
2025-03-26 11:44:00,254 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 11:44:00,255 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 11:44:00,294 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 11:44:00,295 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 12:00:02,785 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-26 12:14:50,162 INFO /home/<USER>/.local/bin/bench start
2025-03-26 12:14:50,864 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 12:14:50,867 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 12:14:50,867 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 12:14:50,870 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 12:15:50,466 INFO /home/<USER>/.local/bin/bench start
2025-03-26 12:15:51,671 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 12:15:51,674 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 12:15:51,676 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 12:15:51,678 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 12:34:37,000 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-26 12:34:41,960 INFO /home/<USER>/.local/bin/bench start
2025-03-26 12:34:42,245 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 12:34:42,255 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 12:34:42,294 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 12:34:42,299 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 12:42:37,159 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git
2025-03-26 12:42:37,172 LOG Getting LibMS
2025-03-26 12:42:37,172 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git  --depth 1 --origin upstream
2025-03-26 12:42:40,350 LOG Installing library_management
2025-03-26 12:42:40,351 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/library_management 
2025-03-26 12:42:42,475 DEBUG bench build --app library_management
2025-03-26 12:42:42,604 INFO /home/<USER>/.local/bin/bench build --app library_management
2025-03-26 12:42:44,654 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-26 12:43:45,603 INFO /home/<USER>/.local/bin/bench new-site libms
2025-03-26 12:44:01,108 INFO /home/<USER>/.local/bin/bench new-site libraryms
2025-03-26 12:45:22,885 INFO /home/<USER>/.local/bin/bench --site libraryms install-app erpnext
2025-03-26 12:46:37,662 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-03-26 12:46:37,674 LOG Getting LibMS
2025-03-26 12:46:37,674 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-03-26 12:46:57,591 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop executed with exit code 1
2025-03-26 12:47:34,393 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-03-26 12:47:36,945 INFO App moved from apps/LibMS to archived/apps/LibMS-2025-03-26
2025-03-26 12:47:36,953 LOG Getting LibMS
2025-03-26 12:47:36,953 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-03-26 12:47:57,662 LOG Installing library_management
2025-03-26 12:47:57,663 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/library_management 
2025-03-26 12:47:59,379 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/library_management && yarn install --check-files
2025-03-26 12:48:03,308 DEBUG bench build --app library_management
2025-03-26 12:48:03,430 INFO /home/<USER>/.local/bin/bench build --app library_management
2025-03-26 12:48:06,180 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-03-26 12:48:21,984 INFO /home/<USER>/.local/bin/bench --site libraryms install-app payments
2025-03-26 12:48:39,839 INFO /home/<USER>/.local/bin/bench --site libraryms install-app webshop
2025-03-26 12:49:09,082 INFO /home/<USER>/.local/bin/bench --site libraryms install-app library_management
2025-03-26 12:49:33,744 INFO /home/<USER>/.local/bin/bench use libraryms
2025-03-26 12:49:38,102 INFO /home/<USER>/.local/bin/bench start
2025-03-26 12:49:38,442 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 12:49:38,449 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 12:49:38,474 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 12:49:38,486 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 12:54:41,993 INFO /home/<USER>/.local/bin/bench --site libraryms restore /home/<USER>/Downloads/20250326_113002-libms-dev_aakvaerp_com-database.sql.gz
2025-03-26 12:57:12,365 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 13:01:47,844 INFO /home/<USER>/.local/bin/bench start
2025-03-26 13:01:48,182 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 13:01:48,186 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 13:01:48,189 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 13:01:48,199 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 13:02:41,952 INFO /home/<USER>/.local/bin/bench --site libraryms set-maintenance-mode off
2025-03-26 13:03:02,124 INFO /home/<USER>/.local/bin/bench --site libraryms set-admin-password aakvatech
2025-03-26 13:11:01,542 INFO /home/<USER>/.local/bin/bench start
2025-03-26 13:11:01,824 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 13:11:01,838 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 13:11:01,839 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 13:11:01,841 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 15:34:39,389 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-26 15:34:41,157 INFO /home/<USER>/.local/bin/bench start
2025-03-26 15:34:41,389 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 15:34:41,392 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 15:34:41,427 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 15:34:41,441 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 15:39:14,332 INFO /home/<USER>/.local/bin/bench start
2025-03-26 15:39:14,578 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 15:39:14,592 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 15:39:14,603 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 15:39:14,624 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 15:39:31,816 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 15:41:13,561 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 15:42:01,655 INFO /home/<USER>/.local/bin/bench start
2025-03-26 15:42:01,900 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 15:42:01,909 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 15:42:01,933 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 15:42:01,951 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 15:42:20,672 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 15:47:59,772 INFO /home/<USER>/.local/bin/bench console
2025-03-26 17:03:11,391 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app erpbiometric_sync
2025-03-26 17:03:20,755 INFO /home/<USER>/.local/bin/bench migrate
2025-03-26 17:18:03,427 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-26 17:18:05,408 INFO /home/<USER>/.local/bin/bench start
2025-03-26 17:18:05,727 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 17:18:05,736 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 17:18:05,779 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 17:18:05,786 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 17:54:40,618 INFO /home/<USER>/.local/bin/bench start
2025-03-26 17:54:40,864 INFO /home/<USER>/.local/bin/bench schedule
2025-03-26 17:54:40,878 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-26 17:54:40,891 INFO /home/<USER>/.local/bin/bench watch
2025-03-26 17:54:40,903 INFO /home/<USER>/.local/bin/bench worker
2025-03-26 17:55:15,315 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-users
2025-03-26 18:00:02,138 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-27 08:59:33,118 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-27 08:59:39,198 INFO /home/<USER>/.local/bin/bench start
2025-03-27 08:59:39,720 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 08:59:39,721 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 08:59:39,745 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 08:59:39,752 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 09:03:19,204 INFO /home/<USER>/.local/bin/bench start
2025-03-27 09:03:19,410 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 09:03:19,412 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 09:03:19,417 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 09:03:19,479 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 09:03:30,003 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-27 09:03:35,189 INFO /home/<USER>/.local/bin/bench start
2025-03-27 09:03:35,437 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 09:03:35,444 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 09:03:35,449 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 09:03:35,454 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 09:06:29,476 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 09:06:54,870 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 09:11:33,824 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 09:22:04,829 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 09:38:33,043 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 10:03:55,505 INFO /home/<USER>/.local/bin/bench start
2025-03-27 10:04:02,734 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 10:04:02,746 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 10:04:02,748 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 10:04:02,764 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 10:12:42,046 INFO /home/<USER>/.local/bin/bench start
2025-03-27 10:12:42,285 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 10:12:42,346 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 10:12:42,350 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 10:12:42,355 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 10:19:15,239 INFO /home/<USER>/.local/bin/bench start
2025-03-27 10:19:15,453 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 10:19:15,455 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 10:19:15,455 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 10:19:15,507 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 10:21:02,407 INFO /home/<USER>/.local/bin/bench start
2025-03-27 10:21:02,622 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 10:21:02,624 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 10:21:02,643 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 10:21:02,649 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 10:27:44,351 INFO /home/<USER>/.local/bin/bench start
2025-03-27 10:27:44,571 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 10:27:44,575 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 10:27:44,576 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 10:27:44,622 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 10:27:47,704 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 10:45:16,317 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 10:51:31,172 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 11:02:41,496 INFO /home/<USER>/.local/bin/bench use libraryms
2025-03-27 11:02:47,320 INFO /home/<USER>/.local/bin/bench start
2025-03-27 11:02:47,840 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 11:02:47,842 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 11:02:47,845 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 11:02:47,860 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 11:46:54,097 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-27 11:46:58,300 INFO /home/<USER>/.local/bin/bench start
2025-03-27 11:46:58,518 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 11:46:58,571 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 11:46:58,574 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 11:46:58,581 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:00:01,804 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-27 12:07:35,899 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-27 12:07:40,092 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:07:40,364 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:07:40,366 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:07:40,419 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:07:40,424 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 12:14:40,131 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-27 12:14:43,740 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:14:43,986 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 12:14:43,993 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:14:43,995 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:14:43,995 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:31:41,948 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 12:38:07,100 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:38:07,360 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 12:38:07,379 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:38:07,409 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:38:07,414 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:39:00,396 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:39:00,637 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:39:00,645 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:39:00,649 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:39:00,681 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 12:42:51,757 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:42:51,974 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:42:52,032 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 12:42:52,033 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:42:52,034 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:49:47,634 INFO /home/<USER>/.local/bin/bench start
2025-03-27 12:49:47,863 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 12:49:47,872 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 12:49:47,886 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 12:49:47,888 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 13:17:20,730 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 13:23:55,114 INFO /home/<USER>/.local/bin/bench start
2025-03-27 13:23:55,357 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 13:23:55,361 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 13:23:55,362 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 13:23:55,409 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 13:23:57,747 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 14:02:28,280 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 14:04:04,503 INFO /home/<USER>/.local/bin/bench migrate
2025-03-27 14:56:52,257 INFO /home/<USER>/.local/bin/bench start
2025-03-27 14:56:52,506 INFO /home/<USER>/.local/bin/bench watch
2025-03-27 14:56:52,542 INFO /home/<USER>/.local/bin/bench worker
2025-03-27 14:56:52,558 INFO /home/<USER>/.local/bin/bench schedule
2025-03-27 14:56:52,558 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-27 17:57:47,109 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz clear-cache
2025-03-27 17:57:54,224 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz clear-website-cache
2025-03-27 18:00:01,559 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-28 00:00:01,985 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-28 08:40:04,396 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-28 08:40:19,770 INFO /home/<USER>/.local/bin/bench start
2025-03-28 08:40:20,077 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 08:40:20,084 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 08:40:20,135 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 08:40:20,136 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 10:09:24,237 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-28 10:09:26,733 INFO /home/<USER>/.local/bin/bench start
2025-03-28 10:09:27,041 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 10:09:27,041 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 10:09:27,042 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 10:09:27,057 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 10:34:54,848 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 10:39:13,836 INFO /home/<USER>/.local/bin/bench console
2025-03-28 10:40:52,390 INFO /home/<USER>/.local/bin/bench console
2025-03-28 10:53:32,754 INFO /home/<USER>/.local/bin/bench start
2025-03-28 10:53:32,981 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 10:53:32,983 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 10:53:33,020 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 10:53:33,032 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 10:56:53,533 INFO /home/<USER>/.local/bin/bench start
2025-03-28 10:56:53,766 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 10:56:53,771 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 10:56:53,801 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 10:56:53,814 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 10:58:35,317 INFO /home/<USER>/.local/bin/bench start
2025-03-28 10:58:35,549 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 10:58:35,550 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 10:58:35,593 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 10:58:35,596 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 11:08:01,353 INFO /home/<USER>/.local/bin/bench migrate
2025-03-28 11:11:52,270 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-28 11:11:53,932 INFO /home/<USER>/.local/bin/bench start
2025-03-28 11:11:54,184 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 11:11:54,187 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 11:11:54,233 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 11:11:54,242 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 12:00:01,938 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-28 16:53:32,516 INFO /home/<USER>/.local/bin/bench start
2025-03-28 16:53:32,806 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 16:53:32,810 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 16:53:32,858 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 16:53:32,859 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 16:58:45,260 INFO /home/<USER>/.local/bin/bench start
2025-03-28 16:58:48,732 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 16:58:48,734 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 16:58:48,736 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 16:58:48,737 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 18:00:02,731 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-28 23:22:49,142 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-28 23:22:55,403 INFO /home/<USER>/.local/bin/bench start
2025-03-28 23:22:55,965 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 23:22:55,972 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 23:22:55,993 INFO /home/<USER>/.local/bin/bench worker
2025-03-28 23:22:56,003 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 23:59:17,378 INFO /home/<USER>/.local/bin/bench start
2025-03-28 23:59:17,590 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-28 23:59:17,605 INFO /home/<USER>/.local/bin/bench schedule
2025-03-28 23:59:17,630 INFO /home/<USER>/.local/bin/bench watch
2025-03-28 23:59:17,646 INFO /home/<USER>/.local/bin/bench worker
2025-03-29 00:00:01,461 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 00:06:32,671 INFO /home/<USER>/.local/bin/bench start
2025-03-29 00:06:37,206 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-29 00:06:37,207 INFO /home/<USER>/.local/bin/bench schedule
2025-03-29 00:06:37,209 INFO /home/<USER>/.local/bin/bench watch
2025-03-29 00:06:37,216 INFO /home/<USER>/.local/bin/bench worker
2025-03-29 00:07:54,639 INFO /home/<USER>/.local/bin/bench start
2025-03-29 00:07:54,836 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-29 00:07:54,840 INFO /home/<USER>/.local/bin/bench schedule
2025-03-29 00:07:54,840 INFO /home/<USER>/.local/bin/bench watch
2025-03-29 00:07:54,903 INFO /home/<USER>/.local/bin/bench worker
2025-03-29 06:00:02,035 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 12:00:01,902 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-31 10:45:08,706 INFO /home/<USER>/.local/bin/bench use certifications
2025-03-31 10:45:23,847 INFO /home/<USER>/.local/bin/bench start
2025-03-31 10:45:24,192 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 10:45:24,226 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 10:45:24,229 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 10:45:24,245 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 10:46:30,338 INFO /home/<USER>/.local/bin/bench console
2025-03-31 10:50:54,452 INFO /home/<USER>/.local/bin/bench start
2025-03-31 10:50:54,662 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 10:50:54,663 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 10:50:54,673 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 10:50:54,733 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 10:55:56,779 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 10:59:15,311 INFO /home/<USER>/.local/bin/bench start
2025-03-31 10:59:15,528 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 10:59:15,566 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 10:59:15,574 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 10:59:15,577 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 10:59:19,342 INFO /home/<USER>/.local/bin/bench console
2025-03-31 11:01:11,562 INFO /home/<USER>/.local/bin/bench start
2025-03-31 11:01:11,803 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 11:01:11,849 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 11:01:11,855 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 11:01:11,858 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 11:01:16,630 INFO /home/<USER>/.local/bin/bench console
2025-03-31 11:15:32,161 INFO /home/<USER>/.local/bin/bench start
2025-03-31 11:15:32,371 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 11:15:32,428 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 11:15:32,430 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 11:15:32,432 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 11:22:39,668 INFO /home/<USER>/.local/bin/bench start
2025-03-31 11:22:39,875 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 11:22:39,939 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 11:22:39,943 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 11:22:39,945 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 12:00:01,193 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-31 13:28:50,429 INFO /home/<USER>/.local/bin/bench start
2025-03-31 13:28:50,654 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 13:28:50,656 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 13:28:50,678 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 13:28:50,694 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 15:03:22,250 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-31 15:03:44,735 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-31 15:04:32,494 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-31 15:04:45,316 INFO /home/<USER>/.local/bin/bench start
2025-03-31 15:04:53,896 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 15:04:53,896 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 15:04:53,905 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 15:04:53,914 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 22:11:33,817 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-31 22:11:48,777 INFO /home/<USER>/.local/bin/bench start
2025-03-31 22:11:49,223 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 22:11:49,237 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 22:11:49,251 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 22:11:49,264 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 22:13:36,387 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 22:33:46,682 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-03-31 22:33:52,599 INFO /home/<USER>/.local/bin/bench start
2025-03-31 22:33:52,920 INFO /home/<USER>/.local/bin/bench worker
2025-03-31 22:33:52,924 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 22:33:52,952 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 22:33:52,953 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 22:34:00,641 INFO /home/<USER>/.local/bin/bench migrate
2025-03-31 23:20:26,130 INFO /home/<USER>/.local/bin/bench start
2025-03-31 23:20:26,367 INFO /home/<USER>/.local/bin/bench schedule
2025-03-31 23:20:26,374 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-03-31 23:20:26,428 INFO /home/<USER>/.local/bin/bench watch
2025-03-31 23:20:26,434 INFO /home/<USER>/.local/bin/bench worker
2025-04-01 00:00:01,447 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-01 00:33:23,934 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-01 00:33:27,021 INFO /home/<USER>/.local/bin/bench start
2025-04-01 00:33:42,038 INFO /home/<USER>/.local/bin/bench start
2025-04-01 00:33:42,276 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-01 00:33:42,283 INFO /home/<USER>/.local/bin/bench worker
2025-04-01 00:33:42,288 INFO /home/<USER>/.local/bin/bench schedule
2025-04-01 00:33:42,291 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 09:42:56,837 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-02 09:43:03,646 INFO /home/<USER>/.local/bin/bench start
2025-04-02 09:43:04,165 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 09:43:04,176 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 09:43:04,179 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 09:43:04,180 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 09:50:47,129 INFO /home/<USER>/.local/bin/bench start
2025-04-02 09:50:48,285 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 09:50:48,286 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 09:50:48,290 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 09:50:48,295 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 09:55:00,698 INFO /home/<USER>/.local/bin/bench start
2025-04-02 09:55:00,929 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 09:55:00,930 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 09:55:00,948 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 09:55:00,948 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 11:25:18,018 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-02 11:25:20,833 INFO /home/<USER>/.local/bin/bench start
2025-04-02 11:25:21,146 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 11:25:21,159 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 11:25:21,160 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 11:25:21,178 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 12:00:01,235 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-02 12:20:36,026 INFO /home/<USER>/.local/bin/bench migrate
2025-04-02 12:27:33,851 INFO /home/<USER>/.local/bin/bench migrate
2025-04-02 12:40:31,925 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-02 12:40:33,757 INFO /home/<USER>/.local/bin/bench start
2025-04-02 12:40:33,970 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 12:40:33,972 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 12:40:34,009 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 12:40:34,010 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 14:04:00,569 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-02 14:04:28,174 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-02 14:04:31,556 INFO /home/<USER>/.local/bin/bench start
2025-04-02 14:04:46,012 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 14:04:46,012 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 14:04:46,058 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 14:04:46,061 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 15:35:44,697 INFO /home/<USER>/.local/bin/bench migrate
2025-04-02 16:54:57,298 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-02 16:55:02,258 INFO /home/<USER>/.local/bin/bench start
2025-04-02 16:55:02,980 INFO /home/<USER>/.local/bin/bench schedule
2025-04-02 16:55:03,025 INFO /home/<USER>/.local/bin/bench worker
2025-04-02 16:55:03,052 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-02 16:55:03,110 INFO /home/<USER>/.local/bin/bench watch
2025-04-02 18:00:01,293 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 00:00:01,481 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 10:30:48,323 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-03 10:30:56,641 INFO /home/<USER>/.local/bin/bench start
2025-04-03 10:30:57,182 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 10:30:57,210 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 10:30:57,224 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 10:30:57,242 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 11:04:47,636 INFO /home/<USER>/.local/bin/bench get-app https://github.com/VVSD-LTD/staff_loans.git --branch version-15
2025-04-03 11:04:48,509 LOG Getting staff_loans
2025-04-03 11:04:48,509 DEBUG cd ./apps && git clone https://github.com/VVSD-LTD/staff_loans.git --branch version-15 --depth 1 --origin upstream
2025-04-03 11:04:53,599 LOG Installing staff_loans
2025-04-03 11:04:53,600 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/staff_loans 
2025-04-03 11:05:27,881 DEBUG bench build --app staff_loans
2025-04-03 11:05:28,032 INFO /home/<USER>/.local/bin/bench build --app staff_loans
2025-04-03 11:07:39,715 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/VVSD-LTD/staff_loans.git --branch version-15 executed with exit code 1
2025-04-03 11:11:43,512 INFO /home/<USER>/.local/bin/bench get-app https://github.com/VVSD-LTD/staff_loans.git --branch version-15
2025-04-03 11:11:46,354 INFO App moved from apps/staff_loans to archived/apps/staff_loans-2025-04-03
2025-04-03 11:11:46,363 LOG Getting staff_loans
2025-04-03 11:11:46,364 DEBUG cd ./apps && git clone https://github.com/VVSD-LTD/staff_loans.git --branch version-15 --depth 1 --origin upstream
2025-04-03 11:11:48,394 LOG Installing staff_loans
2025-04-03 11:11:48,395 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/staff_loans 
2025-04-03 11:11:50,989 DEBUG bench build --app staff_loans
2025-04-03 11:11:51,190 INFO /home/<USER>/.local/bin/bench build --app staff_loans
2025-04-03 11:11:55,575 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-03 11:12:22,169 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/servicems.git --branch version-15
2025-04-03 11:12:22,181 LOG Getting servicems
2025-04-03 11:12:22,181 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/servicems.git --branch version-15 --depth 1 --origin upstream
2025-04-03 11:12:36,257 LOG Installing servicems
2025-04-03 11:12:36,257 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/servicems 
2025-04-03 11:12:38,795 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/servicems && yarn install --check-files
2025-04-03 11:14:10,083 DEBUG bench build --app servicems
2025-04-03 11:14:10,225 INFO /home/<USER>/.local/bin/bench build --app servicems
2025-04-03 11:14:24,183 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-03 11:15:02,997 INFO /home/<USER>/.local/bin/bench get-app https://github.com/yrestom/erpnext_telegram.git --branch version-14
2025-04-03 11:15:03,009 LOG Getting erpnext_telegram
2025-04-03 11:15:03,009 DEBUG cd ./apps && git clone https://github.com/yrestom/erpnext_telegram.git --branch version-14 --depth 1 --origin upstream
2025-04-03 11:15:04,907 LOG Installing erpnext_telegram_integration
2025-04-03 11:15:04,908 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext_telegram_integration 
2025-04-03 11:15:11,580 DEBUG bench build --app erpnext_telegram_integration
2025-04-03 11:15:11,760 INFO /home/<USER>/.local/bin/bench build --app erpnext_telegram_integration
2025-04-03 11:15:13,932 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-03 11:15:52,629 INFO /home/<USER>/.local/bin/bench new-site beetle
2025-04-03 11:17:07,455 INFO /home/<USER>/.local/bin/bench --site beetle install-app erpnext
2025-04-03 11:34:19,123 INFO /home/<USER>/.local/bin/bench --site beetle install-app hrms
2025-04-03 11:35:03,801 INFO /home/<USER>/.local/bin/bench --site beetle install-app hrms
2025-04-03 11:35:14,080 INFO /home/<USER>/.local/bin/bench --site beetle install-app csf_tz
2025-04-03 11:37:40,637 INFO /home/<USER>/.local/bin/bench --site beetle install-app payware
2025-04-03 11:37:49,300 INFO /home/<USER>/.local/bin/bench --site beetle install-app payments
2025-04-03 11:38:01,381 INFO /home/<USER>/.local/bin/bench --site beetle install-app staff_loans
2025-04-03 11:38:24,251 INFO /home/<USER>/.local/bin/bench --site beetle install-app erpnext_telegram_integration
2025-04-03 11:38:47,441 INFO /home/<USER>/.local/bin/bench --site beetle set-admin-password aakvatech
2025-04-03 11:39:00,959 INFO /home/<USER>/.local/bin/bench start
2025-04-03 11:39:01,286 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 11:39:01,286 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 11:39:01,319 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 11:39:01,319 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 11:39:32,103 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tzclear
2025-04-03 11:39:53,857 INFO /home/<USER>/.local/bin/bench --site beetle restore /home/<USER>/Downloads/20250402_203020-beetle-av_frappe_cloud-database.sql.gz
2025-04-03 11:43:28,283 INFO /home/<USER>/.local/bin/bench --site beetle set-admin-password aakvatech
2025-04-03 11:44:06,444 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-03 11:44:09,574 INFO /home/<USER>/.local/bin/bench start
2025-04-03 11:44:09,789 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 11:44:09,789 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 11:44:09,797 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 11:44:09,841 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 11:44:36,116 INFO /home/<USER>/.local/bin/bench --site beetle set-maintenance-mode off
2025-04-03 12:00:01,390 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 12:42:20,302 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-03 12:42:22,052 INFO /home/<USER>/.local/bin/bench start
2025-04-03 12:42:22,617 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 12:42:22,630 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 12:42:22,640 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 12:42:22,654 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 13:07:22,174 INFO /home/<USER>/.local/bin/bench start
2025-04-03 13:07:28,688 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 13:07:28,690 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 13:07:28,690 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 13:07:28,691 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 13:09:06,563 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-03 13:09:11,490 INFO /home/<USER>/.local/bin/bench start
2025-04-03 13:09:12,206 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 13:09:12,247 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 13:09:12,280 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 13:09:12,326 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 14:09:07,720 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-03 14:09:09,771 INFO /home/<USER>/.local/bin/bench start
2025-04-03 14:09:10,056 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 14:09:10,058 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 14:09:10,066 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 14:09:10,071 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 18:00:02,324 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 18:23:14,626 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-03 18:23:18,626 INFO /home/<USER>/.local/bin/bench start
2025-04-03 18:23:18,996 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-03 18:23:19,007 INFO /home/<USER>/.local/bin/bench watch
2025-04-03 18:23:19,039 INFO /home/<USER>/.local/bin/bench worker
2025-04-03 18:23:19,052 INFO /home/<USER>/.local/bin/bench schedule
2025-04-03 18:23:27,940 INFO /home/<USER>/.local/bin/bench migrate
2025-04-03 18:25:42,702 INFO /home/<USER>/.local/bin/bench migrate
2025-04-03 18:30:01,906 INFO /home/<USER>/.local/bin/bench migrate
2025-04-04 09:00:52,408 INFO /home/<USER>/.local/bin/bench start
2025-04-04 09:00:52,919 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-04 09:00:52,923 INFO /home/<USER>/.local/bin/bench schedule
2025-04-04 09:00:52,965 INFO /home/<USER>/.local/bin/bench watch
2025-04-04 09:00:52,967 INFO /home/<USER>/.local/bin/bench worker
2025-04-04 09:15:29,303 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-04 09:15:32,719 INFO /home/<USER>/.local/bin/bench start
2025-04-04 09:15:32,959 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-04 09:15:32,959 INFO /home/<USER>/.local/bin/bench watch
2025-04-04 09:15:33,016 INFO /home/<USER>/.local/bin/bench schedule
2025-04-04 09:15:33,019 INFO /home/<USER>/.local/bin/bench worker
2025-04-04 12:00:02,161 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-04 16:25:05,108 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-04 16:25:09,204 INFO /home/<USER>/.local/bin/bench start
2025-04-04 16:25:09,552 INFO /home/<USER>/.local/bin/bench watch
2025-04-04 16:25:09,564 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-04 16:25:09,636 INFO /home/<USER>/.local/bin/bench schedule
2025-04-04 16:25:09,645 INFO /home/<USER>/.local/bin/bench worker
2025-04-04 18:00:01,382 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-07 12:00:02,273 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-07 00:00:01,884 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-07 23:29:40,831 INFO /home/<USER>/.local/bin/bench new-site ytyyyjyu
2025-04-08 00:00:01,791 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-08 10:20:04,561 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-08 10:20:10,836 INFO /home/<USER>/.local/bin/bench start
2025-04-08 10:20:11,225 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 10:20:11,242 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 10:20:11,276 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 10:20:11,301 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-08 11:17:02,053 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 11:20:26,525 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 12:00:02,065 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-08 12:35:43,737 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-08 12:35:47,771 INFO /home/<USER>/.local/bin/bench start
2025-04-08 12:35:48,033 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 12:35:48,035 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 12:35:48,089 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-08 12:35:48,097 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 14:06:38,887 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-08 14:06:54,688 INFO /home/<USER>/.local/bin/bench start
2025-04-08 14:06:54,952 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-08 14:06:54,953 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 14:06:54,983 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 14:06:54,987 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 15:19:34,469 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-08 15:19:45,700 INFO /home/<USER>/.local/bin/bench start
2025-04-08 15:19:46,085 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 15:19:46,091 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 15:19:46,103 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-08 15:19:46,229 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 15:43:17,943 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:43:18,060 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:45:09,513 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:48:42,690 INFO /home/<USER>/.local/bin/bench migrate
2025-04-08 16:54:18,123 INFO /home/<USER>/.local/bin/bench start
2025-04-08 16:54:18,478 INFO /home/<USER>/.local/bin/bench watch
2025-04-08 16:54:18,481 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-08 16:54:18,511 INFO /home/<USER>/.local/bin/bench worker
2025-04-08 16:54:18,529 INFO /home/<USER>/.local/bin/bench schedule
2025-04-08 17:16:44,677 INFO /home/<USER>/.local/bin/bench console
2025-04-08 18:00:01,566 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-09 00:00:02,323 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-09 09:15:08,516 INFO /home/<USER>/.local/bin/bench start
2025-04-09 09:15:08,827 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 09:15:08,831 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 09:15:08,839 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 09:15:08,843 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 09:50:56,666 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-09 09:50:58,852 INFO /home/<USER>/.local/bin/bench start
2025-04-09 09:50:59,153 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 09:50:59,159 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 09:50:59,188 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 09:50:59,194 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 11:12:40,059 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-09 11:12:50,864 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-09 11:12:53,910 INFO /home/<USER>/.local/bin/bench start
2025-04-09 11:12:54,134 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 11:12:54,156 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 11:12:54,186 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 11:12:54,187 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 12:00:01,447 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-09 13:51:01,137 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 14:13:16,573 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-09 14:13:20,778 INFO /home/<USER>/.local/bin/bench start
2025-04-09 14:13:21,069 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 14:13:21,077 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 14:13:21,111 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 14:13:21,113 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 14:44:09,482 INFO /home/<USER>/.local/bin/bench start
2025-04-09 14:44:09,816 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 14:44:09,821 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 14:44:09,823 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 14:44:09,831 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 14:44:32,357 INFO /home/<USER>/.local/bin/bench build
2025-04-09 14:54:40,995 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/servicems.git --branch version-15
2025-04-09 14:54:44,944 INFO App moved from apps/servicems to archived/apps/servicems-2025-04-09
2025-04-09 14:54:44,952 LOG Getting servicems
2025-04-09 14:54:44,952 DEBUG cd ./apps && git clone https://github.com/j0sh01/servicems.git --branch version-15 --depth 1 --origin upstream
2025-04-09 14:54:54,647 LOG Installing servicems
2025-04-09 14:54:54,647 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/servicems 
2025-04-09 14:54:58,623 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/servicems && yarn install --check-files
2025-04-09 14:55:10,701 DEBUG bench build --app servicems
2025-04-09 14:55:10,879 INFO /home/<USER>/.local/bin/bench build --app servicems
2025-04-09 14:55:30,905 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-09 14:55:31,330 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-09 15:11:51,153 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-09 15:11:53,422 INFO /home/<USER>/.local/bin/bench start
2025-04-09 15:11:53,780 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 15:11:53,781 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 15:11:53,835 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 15:11:53,845 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 16:09:25,754 INFO /home/<USER>/.local/bin/bench start
2025-04-09 16:09:26,216 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 16:09:26,236 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 16:09:26,240 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 16:09:26,248 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 16:09:36,227 INFO /home/<USER>/.local/bin/bench console
2025-04-09 16:20:11,029 INFO /home/<USER>/.local/bin/bench console
2025-04-09 16:30:39,357 INFO /home/<USER>/.local/bin/bench console
2025-04-09 16:47:01,319 INFO /home/<USER>/.local/bin/bench console
2025-04-09 16:48:12,317 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 16:52:01,478 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:04:11,447 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:17:56,729 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:25:24,517 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:26:17,135 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:30:09,578 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:30:39,395 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:31:04,401 INFO /home/<USER>/.local/bin/bench console
2025-04-09 17:31:22,911 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:31:40,942 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:32:42,092 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:39:13,043 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:42:00,307 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:46:07,402 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 17:52:28,527 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-09 17:52:35,170 INFO /home/<USER>/.local/bin/bench start
2025-04-09 17:52:35,864 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 17:52:35,867 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 17:52:35,899 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 17:52:35,918 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 17:54:24,800 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-09 18:00:01,495 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-10 08:56:06,286 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-10 08:56:11,772 INFO /home/<USER>/.local/bin/bench start
2025-04-10 08:56:12,812 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 08:56:12,815 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 08:56:12,817 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 08:56:12,838 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-10 10:58:28,065 INFO /home/<USER>/.local/bin/bench migrate
2025-04-10 11:17:12,468 INFO /home/<USER>/.local/bin/bench migrate
2025-04-10 12:00:01,330 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-10 12:23:23,511 INFO /home/<USER>/.local/bin/bench migrate
2025-04-10 12:53:52,579 INFO /home/<USER>/.local/bin/bench migrate
2025-04-10 13:22:49,239 INFO /home/<USER>/.local/bin/bench use wasco
2025-04-10 13:22:51,183 INFO /home/<USER>/.local/bin/bench start
2025-04-10 13:22:51,852 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 13:22:51,853 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 13:22:51,899 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 13:22:51,913 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-10 14:29:31,756 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-10 14:29:34,291 INFO /home/<USER>/.local/bin/bench start
2025-04-10 14:29:34,523 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 14:29:34,524 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-10 14:29:34,552 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 14:29:34,565 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 17:49:21,095 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-10 17:49:25,802 INFO /home/<USER>/.local/bin/bench start
2025-04-10 17:49:26,176 INFO /home/<USER>/.local/bin/bench watch
2025-04-10 17:49:26,225 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-10 17:49:26,236 INFO /home/<USER>/.local/bin/bench schedule
2025-04-10 17:49:26,255 INFO /home/<USER>/.local/bin/bench worker
2025-04-10 18:00:01,454 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-11 08:53:40,784 INFO /home/<USER>/.local/bin/bench use certifications
2025-04-11 08:53:46,092 INFO /home/<USER>/.local/bin/bench start
2025-04-11 08:53:46,296 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-11 08:53:46,310 INFO /home/<USER>/.local/bin/bench worker
2025-04-11 08:53:46,320 INFO /home/<USER>/.local/bin/bench schedule
2025-04-11 08:53:46,326 INFO /home/<USER>/.local/bin/bench watch
2025-04-11 09:21:51,378 INFO /home/<USER>/.local/bin/bench start
2025-04-11 09:21:51,791 INFO /home/<USER>/.local/bin/bench watch
2025-04-11 09:21:51,815 INFO /home/<USER>/.local/bin/bench schedule
2025-04-11 09:21:51,815 INFO /home/<USER>/.local/bin/bench worker
2025-04-11 09:21:51,840 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-11 10:07:50,185 INFO /home/<USER>/.local/bin/bench console
2025-04-11 10:07:58,371 INFO /home/<USER>/.local/bin/bench console
2025-04-11 12:00:01,454 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-11 12:27:23,069 INFO /home/<USER>/.local/bin/bench mariadb
2025-04-11 14:25:41,463 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-11 14:25:47,843 INFO /home/<USER>/.local/bin/bench start
2025-04-11 14:25:48,137 INFO /home/<USER>/.local/bin/bench watch
2025-04-11 14:25:48,148 INFO /home/<USER>/.local/bin/bench schedule
2025-04-11 14:25:48,160 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-11 14:25:48,173 INFO /home/<USER>/.local/bin/bench worker
2025-04-11 18:00:02,296 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-12 09:20:21,187 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-12 09:20:29,117 INFO /home/<USER>/.local/bin/bench start
2025-04-12 09:20:29,695 INFO /home/<USER>/.local/bin/bench watch
2025-04-12 09:20:29,695 INFO /home/<USER>/.local/bin/bench schedule
2025-04-12 09:20:29,696 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-12 09:20:29,698 INFO /home/<USER>/.local/bin/bench worker
2025-04-13 06:00:01,768 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-13 09:06:16,412 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-13 09:06:20,670 INFO /home/<USER>/.local/bin/bench start
2025-04-13 09:06:20,928 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-13 09:06:20,929 INFO /home/<USER>/.local/bin/bench watch
2025-04-13 09:06:20,973 INFO /home/<USER>/.local/bin/bench schedule
2025-04-13 09:06:20,973 INFO /home/<USER>/.local/bin/bench worker
2025-04-13 09:08:10,573 INFO /home/<USER>/.local/bin/bench migrate
2025-04-13 09:20:43,759 INFO /home/<USER>/.local/bin/bench migrate
2025-04-14 11:47:11,081 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-04-14 11:47:16,990 INFO /home/<USER>/.local/bin/bench start
2025-04-14 11:47:17,302 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-14 11:47:17,306 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 11:47:17,346 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 11:47:17,346 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 12:00:01,427 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-14 12:28:09,903 INFO /home/<USER>/.local/bin/bench use alphaasociates.co.tz
2025-04-14 12:28:17,435 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-14 12:28:20,564 INFO /home/<USER>/.local/bin/bench start
2025-04-14 12:28:20,889 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-14 12:28:20,911 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 12:28:20,939 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 12:28:20,945 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 13:08:15,014 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-14 13:08:16,917 INFO /home/<USER>/.local/bin/bench start
2025-04-14 13:08:17,233 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-14 13:08:17,257 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 13:08:17,296 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 13:08:17,296 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 13:45:11,796 INFO /home/<USER>/.local/bin/bench start
2025-04-14 13:45:12,019 INFO /home/<USER>/.local/bin/bench watch
2025-04-14 13:45:12,075 INFO /home/<USER>/.local/bin/bench schedule
2025-04-14 13:45:12,079 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-14 13:45:12,079 INFO /home/<USER>/.local/bin/bench worker
2025-04-14 18:00:01,721 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-15 06:07:14,574 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-15 06:07:28,906 INFO /home/<USER>/.local/bin/bench start
2025-04-15 06:07:29,454 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 06:07:29,466 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 06:07:29,511 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 06:07:29,524 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 08:53:24,565 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-15 08:53:31,564 INFO /home/<USER>/.local/bin/bench start
2025-04-15 08:53:32,141 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 08:53:32,141 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 08:53:32,175 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 08:53:32,184 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 09:24:38,972 INFO /home/<USER>/.local/bin/bench start
2025-04-15 09:24:39,206 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 09:24:39,210 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 09:24:39,246 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 09:24:39,253 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 09:31:13,438 INFO /home/<USER>/.local/bin/bench clear-website-cache
2025-04-15 09:44:11,629 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-15 09:44:13,891 INFO /home/<USER>/.local/bin/bench start
2025-04-15 09:44:14,143 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 09:44:14,174 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 09:44:14,190 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 09:44:14,200 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 09:51:36,138 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-15 09:51:38,192 INFO /home/<USER>/.local/bin/bench start
2025-04-15 09:51:38,517 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 09:51:38,521 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 09:51:38,521 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 09:51:38,523 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 09:58:38,323 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/servicems.git --branch version-15
2025-04-15 09:58:38,337 LOG Getting servicems
2025-04-15 09:58:38,337 DEBUG cd ./apps && git clone https://github.com/j0sh01/servicems.git --branch version-15 --depth 1 --origin upstream
2025-04-15 09:58:47,204 LOG Installing servicems
2025-04-15 09:58:47,205 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/servicems 
2025-04-15 09:58:51,921 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/servicems && yarn install --check-files
2025-04-15 09:59:45,868 DEBUG bench build --app servicems
2025-04-15 09:59:46,236 INFO /home/<USER>/.local/bin/bench build --app servicems
2025-04-15 10:00:12,805 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-15 10:00:13,555 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-15 10:40:06,433 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-15 10:40:12,548 INFO /home/<USER>/.local/bin/bench start
2025-04-15 10:40:12,891 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 10:40:12,894 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 10:40:12,952 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 10:40:12,956 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 11:15:13,668 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-15 11:15:16,860 INFO /home/<USER>/.local/bin/bench start
2025-04-15 11:15:17,182 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 11:15:17,198 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 11:15:17,201 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 11:15:17,207 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 11:21:51,220 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 11:44:43,935 INFO /home/<USER>/.local/bin/bench start
2025-04-15 11:44:44,162 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 11:44:44,168 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 11:44:44,170 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 11:44:44,179 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 12:00:02,074 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-15 12:24:07,231 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-15 12:24:09,283 INFO /home/<USER>/.local/bin/bench start
2025-04-15 12:24:09,570 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 12:24:09,594 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 12:24:09,595 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 12:24:09,606 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 12:27:44,264 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 13:26:12,333 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 14:08:25,278 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-15 14:09:15,074 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-15 14:09:18,651 INFO /home/<USER>/.local/bin/bench start
2025-04-15 14:09:19,059 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 14:09:19,059 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 14:09:19,086 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 14:09:19,100 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 16:09:47,080 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 16:12:52,625 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 16:22:13,159 INFO /home/<USER>/.local/bin/bench migrate
2025-04-15 16:23:26,002 INFO /home/<USER>/.local/bin/bench start
2025-04-15 16:23:26,437 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 16:23:26,439 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 16:23:26,462 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 16:23:26,468 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 16:43:28,270 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-04-15 16:43:30,794 INFO /home/<USER>/.local/bin/bench start
2025-04-15 16:43:31,047 INFO /home/<USER>/.local/bin/bench schedule
2025-04-15 16:43:31,060 INFO /home/<USER>/.local/bin/bench worker
2025-04-15 16:43:31,064 INFO /home/<USER>/.local/bin/bench watch
2025-04-15 16:43:31,108 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-15 17:17:27,241 INFO /home/<USER>/.local/bin/bench --site thps.or.tz uninstall-app frappe_whatsapp
2025-04-15 17:44:08,429 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-04-15 18:00:01,658 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-16 09:59:46,506 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-16 09:59:52,335 INFO /home/<USER>/.local/bin/bench start
2025-04-16 09:59:52,787 INFO /home/<USER>/.local/bin/bench worker
2025-04-16 09:59:52,787 INFO /home/<USER>/.local/bin/bench schedule
2025-04-16 09:59:52,819 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-16 09:59:52,822 INFO /home/<USER>/.local/bin/bench watch
2025-04-16 12:00:03,671 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-16 17:13:08,743 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-16 17:13:12,641 INFO /home/<USER>/.local/bin/bench start
2025-04-16 17:13:12,956 INFO /home/<USER>/.local/bin/bench worker
2025-04-16 17:13:12,966 INFO /home/<USER>/.local/bin/bench schedule
2025-04-16 17:13:13,000 INFO /home/<USER>/.local/bin/bench watch
2025-04-16 17:13:13,013 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-16 17:33:04,052 INFO /home/<USER>/.local/bin/bench ls sites
2025-04-16 17:33:21,207 INFO /home/<USER>/.local/bin/bench use gadget
2025-04-16 17:33:26,994 INFO /home/<USER>/.local/bin/bench start
2025-04-16 17:33:27,218 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-16 17:33:27,232 INFO /home/<USER>/.local/bin/bench worker
2025-04-16 17:33:27,268 INFO /home/<USER>/.local/bin/bench watch
2025-04-16 17:33:27,281 INFO /home/<USER>/.local/bin/bench schedule
2025-04-16 17:38:29,122 INFO /home/<USER>/.local/bin/bench migrate
2025-04-16 18:00:01,791 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-17 00:00:01,332 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-17 06:00:01,581 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-17 11:25:12,995 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-17 11:25:23,261 INFO /home/<USER>/.local/bin/bench start
2025-04-17 11:25:23,623 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 11:25:23,624 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-17 11:25:23,631 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 11:25:23,686 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 11:25:54,963 INFO /home/<USER>/.local/bin/bench start
2025-04-17 11:25:55,198 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 11:25:55,200 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 11:25:55,228 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 11:25:55,228 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-17 11:33:56,359 INFO /home/<USER>/.local/bin/bench start
2025-04-17 11:33:57,776 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 11:33:57,801 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 11:33:57,801 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 11:33:57,819 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-17 12:00:01,963 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-17 12:29:13,210 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-17 12:29:18,115 INFO /home/<USER>/.local/bin/bench start
2025-04-17 12:29:18,666 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 12:29:18,666 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-17 12:29:18,678 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 12:29:18,681 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 16:25:37,449 INFO /home/<USER>/.local/bin/bench start
2025-04-17 16:25:37,975 INFO /home/<USER>/.local/bin/bench schedule
2025-04-17 16:25:37,975 INFO /home/<USER>/.local/bin/bench worker
2025-04-17 16:25:38,014 INFO /home/<USER>/.local/bin/bench watch
2025-04-17 16:25:38,014 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-17 18:00:01,895 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-18 12:00:01,812 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-19 19:21:38,997 INFO /home/<USER>/.local/bin/bench start
2025-04-19 19:21:39,612 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-19 19:21:39,613 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 19:21:39,653 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 19:21:39,654 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 19:37:45,461 INFO /home/<USER>/.local/bin/bench new-app Rental Management Gateway
2025-04-19 19:37:45,469 WARNING /home/<USER>/.local/bin/bench new-app Rental Management Gateway executed with exit code 2
2025-04-19 19:37:46,459 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-19 19:38:07,015 INFO /home/<USER>/.local/bin/bench new-app rental_management_gateway
2025-04-19 19:38:07,020 LOG creating new app rental_management_gateway
2025-04-19 19:39:35,308 LOG Installing rental_management_gateway
2025-04-19 19:39:35,320 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/rental_management_gateway 
2025-04-19 19:39:40,146 DEBUG bench build --app rental_management_gateway
2025-04-19 19:39:40,331 INFO /home/<USER>/.local/bin/bench build --app rental_management_gateway
2025-04-19 19:39:42,812 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-19 19:39:43,736 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-19 19:40:10,776 INFO /home/<USER>/.local/bin/bench new-site rental
2025-04-19 19:41:18,530 INFO /home/<USER>/.local/bin/bench use rental
2025-04-19 19:41:20,930 INFO /home/<USER>/.local/bin/bench start
2025-04-19 19:41:21,211 INFO /home/<USER>/.local/bin/bench worker
2025-04-19 19:41:21,245 INFO /home/<USER>/.local/bin/bench schedule
2025-04-19 19:41:21,252 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-19 19:41:21,262 INFO /home/<USER>/.local/bin/bench watch
2025-04-19 19:42:22,910 INFO /home/<USER>/.local/bin/bench --site rental install-app rental_management_gateway
2025-04-19 19:43:05,290 INFO /home/<USER>/.local/bin/bench --site rental set-maintenance-mode off
2025-04-20 00:00:03,337 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-20 12:00:02,249 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-20 12:12:28,806 INFO /home/<USER>/.local/bin/bench use rental
2025-04-20 12:12:32,975 INFO /home/<USER>/.local/bin/bench start
2025-04-20 12:12:33,631 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-20 12:12:33,631 INFO /home/<USER>/.local/bin/bench schedule
2025-04-20 12:12:33,645 INFO /home/<USER>/.local/bin/bench watch
2025-04-20 12:12:33,647 INFO /home/<USER>/.local/bin/bench worker
2025-04-20 18:00:01,580 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 00:00:01,551 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 12:00:04,487 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 12:01:10,495 INFO /home/<USER>/.local/bin/bench use rental
2025-04-21 12:01:19,887 INFO /home/<USER>/.local/bin/bench start
2025-04-21 12:01:20,717 INFO /home/<USER>/.local/bin/bench watch
2025-04-21 12:01:20,722 INFO /home/<USER>/.local/bin/bench schedule
2025-04-21 12:01:20,770 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-21 12:01:20,777 INFO /home/<USER>/.local/bin/bench worker
2025-04-21 16:50:25,332 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-21 16:50:30,281 INFO /home/<USER>/.local/bin/bench start
2025-04-21 16:50:30,505 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-21 16:50:30,515 INFO /home/<USER>/.local/bin/bench watch
2025-04-21 16:50:30,544 INFO /home/<USER>/.local/bin/bench schedule
2025-04-21 16:50:30,558 INFO /home/<USER>/.local/bin/bench worker
2025-04-21 16:53:49,743 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-21 16:53:55,563 INFO /home/<USER>/.local/bin/bench start
2025-04-21 16:53:55,852 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-21 16:53:55,861 INFO /home/<USER>/.local/bin/bench watch
2025-04-21 16:53:55,879 INFO /home/<USER>/.local/bin/bench schedule
2025-04-21 16:53:55,892 INFO /home/<USER>/.local/bin/bench worker
2025-04-21 16:55:59,436 INFO /home/<USER>/.local/bin/bench migrate
2025-04-21 18:00:01,687 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 23:31:31,437 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-21 23:31:40,178 INFO /home/<USER>/.local/bin/bench start
2025-04-21 23:31:40,676 INFO /home/<USER>/.local/bin/bench watch
2025-04-21 23:31:40,677 INFO /home/<USER>/.local/bin/bench schedule
2025-04-21 23:31:40,677 INFO /home/<USER>/.local/bin/bench worker
2025-04-21 23:31:40,681 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-21 23:34:47,473 INFO /home/<USER>/.local/bin/bench migrate
2025-04-22 00:00:01,893 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 10:33:36,786 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-22 10:33:40,911 INFO /home/<USER>/.local/bin/bench start
2025-04-22 10:33:41,538 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 10:33:41,545 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-22 10:33:41,575 INFO /home/<USER>/.local/bin/bench schedule
2025-04-22 10:33:41,575 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 12:00:01,458 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 16:13:32,550 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-22 16:13:37,414 INFO /home/<USER>/.local/bin/bench start
2025-04-22 16:13:38,181 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 16:13:38,251 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-22 16:13:38,277 INFO /home/<USER>/.local/bin/bench schedule
2025-04-22 16:13:38,373 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 18:00:01,512 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 22:08:19,732 INFO /home/<USER>/.local/bin/bench use rental
2025-04-22 22:08:24,449 INFO /home/<USER>/.local/bin/bench start
2025-04-22 22:08:24,922 INFO /home/<USER>/.local/bin/bench worker
2025-04-22 22:08:24,923 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-22 22:08:24,947 INFO /home/<USER>/.local/bin/bench watch
2025-04-22 22:08:24,958 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 00:00:01,820 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 10:08:27,203 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-23 10:08:31,476 INFO /home/<USER>/.local/bin/bench start
2025-04-23 10:08:32,084 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 10:08:32,098 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 10:08:32,101 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 10:08:32,107 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 10:15:23,034 INFO /home/<USER>/.local/bin/bench use rental
2025-04-23 10:15:34,545 INFO /home/<USER>/.local/bin/bench start
2025-04-23 10:15:34,858 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 10:15:34,862 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 10:15:34,866 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 10:15:34,889 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 11:13:22,147 INFO /home/<USER>/.local/bin/bench use rental
2025-04-23 11:13:27,782 INFO /home/<USER>/.local/bin/bench start
2025-04-23 11:13:28,545 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 11:13:28,557 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 11:13:28,573 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 11:13:28,576 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 11:36:27,514 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-23 11:36:29,576 INFO /home/<USER>/.local/bin/bench start
2025-04-23 11:36:29,833 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 11:36:29,867 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 11:36:29,873 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 11:36:29,880 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 12:00:01,650 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 14:59:55,650 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-23 14:59:57,360 INFO /home/<USER>/.local/bin/bench start
2025-04-23 14:59:57,698 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 14:59:57,699 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 14:59:57,748 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 14:59:57,754 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 15:01:59,159 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-23 15:02:04,894 INFO /home/<USER>/.local/bin/bench start
2025-04-23 15:02:05,122 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 15:02:05,169 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 15:02:05,173 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 15:02:05,179 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 15:02:23,116 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-23 15:02:25,301 INFO /home/<USER>/.local/bin/bench start
2025-04-23 15:02:25,535 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 15:02:25,545 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 15:02:25,584 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 15:02:25,591 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 15:05:56,549 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/vfd_providers.git --branch version-15
2025-04-23 15:05:58,840 INFO App moved from apps/vfd_providers to archived/apps/vfd_providers-2025-04-23
2025-04-23 15:05:58,842 LOG Getting vfd_providers
2025-04-23 15:05:58,842 DEBUG cd ./apps && git clone https://github.com/j0sh01/vfd_providers.git --branch version-15 --depth 1 --origin upstream
2025-04-23 15:06:01,388 LOG Installing vfd_providers
2025-04-23 15:06:01,389 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-04-23 15:06:06,224 DEBUG bench build --app vfd_providers
2025-04-23 15:06:06,551 INFO /home/<USER>/.local/bin/bench build --app vfd_providers
2025-04-23 15:06:10,941 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-23 15:06:11,430 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-23 15:06:18,914 INFO /home/<USER>/.local/bin/bench migrate
2025-04-23 16:06:05,476 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-23 16:06:11,306 INFO /home/<USER>/.local/bin/bench start
2025-04-23 16:06:12,062 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 16:06:12,072 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 16:06:12,076 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 16:06:12,094 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 16:14:48,474 INFO /home/<USER>/.local/bin/bench use masumin
2025-04-23 16:14:52,834 INFO /home/<USER>/.local/bin/bench start
2025-04-23 16:14:53,533 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 16:14:53,617 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 16:14:53,636 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 16:14:53,672 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 17:42:56,883 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-23 17:42:59,511 INFO /home/<USER>/.local/bin/bench start
2025-04-23 17:42:59,843 INFO /home/<USER>/.local/bin/bench schedule
2025-04-23 17:42:59,861 INFO /home/<USER>/.local/bin/bench watch
2025-04-23 17:42:59,865 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-23 17:42:59,869 INFO /home/<USER>/.local/bin/bench worker
2025-04-23 17:57:44,168 INFO /home/<USER>/.local/bin/bench migrate
2025-04-23 17:59:42,157 INFO /home/<USER>/.local/bin/bench migrate
2025-04-23 18:00:01,673 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 18:09:15,436 INFO /home/<USER>/.local/bin/bench site --alphaasociates.co.tz execute apps/erpbiometric_sync/erpbiometric_sync/erpbiometric_sync/scrap.run
2025-04-23 18:09:49,584 INFO /home/<USER>/.local/bin/bench site --alphaasociates.co.tz execute apps/erpbiometric_sync/erpbiometric_sync/erpbiometric_sync/scrap/run
2025-04-23 18:10:07,257 INFO /home/<USER>/.local/bin/bench --site alphaasociates.co.tz execute apps/erpbiometric_sync/erpbiometric_sync/erpbiometric_sync/scrap.run
2025-04-23 18:10:17,934 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz execute apps/erpbiometric_sync/erpbiometric_sync/erpbiometric_sync/scrap.run
2025-04-23 18:11:00,162 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz execute apps/erpbiometric_sync/erpbiometric_sync/scrap.run
2025-04-23 18:12:30,583 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz execute erpbiometric_sync.erpbiometric_sync.scrap.run
2025-04-24 00:00:02,003 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-24 00:55:43,445 INFO /home/<USER>/.local/bin/bench use rental
2025-04-24 00:55:53,028 INFO /home/<USER>/.local/bin/bench start
2025-04-24 00:55:53,888 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 00:55:53,897 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 00:55:53,898 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 00:55:53,905 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 01:13:10,956 INFO /home/<USER>/.local/bin/bench start
2025-04-24 01:13:11,211 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 01:13:11,228 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 01:13:11,239 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 01:13:11,247 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 01:25:52,559 INFO /home/<USER>/.local/bin/bench use rental
2025-04-24 01:25:54,259 INFO /home/<USER>/.local/bin/bench start
2025-04-24 12:00:01,964 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-24 12:06:27,047 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-24 12:06:33,182 INFO /home/<USER>/.local/bin/bench start
2025-04-24 12:06:33,946 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 12:06:33,951 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 12:06:33,986 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 12:06:33,987 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 15:15:45,065 INFO /home/<USER>/.local/bin/bench start
2025-04-24 15:15:45,468 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 15:15:45,488 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 15:15:45,499 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 15:15:45,499 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 15:54:35,840 INFO /home/<USER>/.local/bin/bench start
2025-04-24 15:54:36,219 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 15:54:36,229 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 15:54:36,242 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 15:54:36,262 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 16:48:17,241 INFO /home/<USER>/.local/bin/bench start
2025-04-24 16:48:17,639 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 16:48:17,648 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 16:48:17,687 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 16:48:17,706 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 17:09:21,630 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-24 17:09:27,062 INFO /home/<USER>/.local/bin/bench start
2025-04-24 17:09:27,434 INFO /home/<USER>/.local/bin/bench worker
2025-04-24 17:09:27,439 INFO /home/<USER>/.local/bin/bench watch
2025-04-24 17:09:27,460 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-24 17:09:27,466 INFO /home/<USER>/.local/bin/bench schedule
2025-04-24 18:00:01,349 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-25 06:00:01,740 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-25 06:28:03,997 INFO /home/<USER>/.local/bin/bench use rental
2025-04-25 06:28:08,784 INFO /home/<USER>/.local/bin/bench start
2025-04-25 06:28:09,214 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 06:28:09,218 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 06:28:09,218 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 06:28:09,222 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 07:11:49,181 INFO /home/<USER>/.local/bin/bench start
2025-04-25 07:11:49,443 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 07:11:49,443 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 07:11:49,461 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 07:11:49,470 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 09:24:43,546 INFO /home/<USER>/.local/bin/bench use rental
2025-04-25 09:24:51,003 INFO /home/<USER>/.local/bin/bench start
2025-04-25 09:24:51,731 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 09:24:51,732 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 09:24:51,742 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 09:24:51,749 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 09:33:39,736 INFO /home/<USER>/.local/bin/bench new-site rent
2025-04-25 09:35:42,888 INFO /home/<USER>/.local/bin/bench new-app rent
2025-04-25 09:35:42,893 LOG creating new app rent
2025-04-25 09:35:43,371 WARNING /home/<USER>/.local/bin/bench new-app rent executed with exit code 1
2025-04-25 09:35:56,328 INFO /home/<USER>/.local/bin/bench new-app rents
2025-04-25 09:35:56,333 LOG creating new app rents
2025-04-25 09:36:49,881 LOG Installing rents
2025-04-25 09:36:49,890 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/rents 
2025-04-25 09:36:52,644 DEBUG bench build --app rents
2025-04-25 09:36:52,772 INFO /home/<USER>/.local/bin/bench build --app rents
2025-04-25 09:36:54,554 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-25 09:36:54,953 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-25 09:37:07,602 INFO /home/<USER>/.local/bin/bench --site rent install-app rents
2025-04-25 09:37:37,906 INFO /home/<USER>/.local/bin/bench use rent
2025-04-25 09:37:41,127 INFO /home/<USER>/.local/bin/bench start
2025-04-25 09:37:41,414 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 09:37:41,431 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 09:37:41,437 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 09:37:41,452 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 09:38:04,870 INFO /home/<USER>/.local/bin/bench --site rent set-maintenance-mode off
2025-04-25 12:00:02,533 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-25 14:02:24,012 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-25 14:02:28,630 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:02:34,029 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 14:02:34,046 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:02:34,051 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:02:34,065 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:04:41,290 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:04:41,643 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:04:41,646 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 14:04:41,655 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:04:41,657 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:05:37,996 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:05:38,301 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:05:38,301 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 14:05:38,323 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:05:38,329 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:07:07,081 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-25 14:07:10,640 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:07:10,932 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:07:10,946 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:07:10,957 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:07:10,962 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 14:10:05,586 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-25 14:10:20,556 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:10:22,621 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:10:22,632 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:10:22,656 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 14:10:22,666 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:11:13,535 INFO /home/<USER>/.local/bin/bench start
2025-04-25 14:11:13,839 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 14:11:13,854 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 14:11:13,876 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 14:11:13,895 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 15:46:22,896 INFO /home/<USER>/.local/bin/bench execute rental_management_gateway/scrap.py
2025-04-25 15:46:52,965 INFO /home/<USER>/.local/bin/bench execute rental_management_gateway/scrap.py
2025-04-25 15:47:57,367 INFO /home/<USER>/.local/bin/bench execute scrap.py
2025-04-25 15:48:15,391 INFO /home/<USER>/.local/bin/bench execute rental_management_gateway/rental_management_gateway/scrap.py
2025-04-25 15:53:22,253 INFO /home/<USER>/.local/bin/bench pip install requests beautifulsoup4 pillow
2025-04-25 16:08:30,531 INFO /home/<USER>/.local/bin/bench site --masumin execute rental_management_gateway/scrap.run
2025-04-25 16:08:43,573 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_management_gateway/scrap.run
2025-04-25 16:08:53,031 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_management_gateway.scrap.run
2025-04-25 16:10:22,413 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_management_gateway.rental_management_gateway.scrap.run
2025-04-25 16:12:35,669 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_management_gateway.scrap.run
2025-04-25 16:23:34,015 INFO /home/<USER>/.local/bin/bench --site masumin execute hrms.scrap.run
2025-04-25 16:24:22,753 INFO /home/<USER>/.local/bin/bench site --masumin execute rental_management_gateway/scrap.run
2025-04-25 16:24:40,453 INFO /home/<USER>/.local/bin/bench site --masumin execute rental_hrms.scrap.run
2025-04-25 16:24:53,791 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_hrms.scrap.run
2025-04-25 16:24:59,839 INFO /home/<USER>/.local/bin/bench --site masumin execute rental_hrms.hrms.scrap.run
2025-04-25 16:31:15,093 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-25 16:31:31,132 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-25 16:31:52,126 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.fetcher.run
2025-04-25 16:32:51,440 INFO /home/<USER>/.local/bin/bench --site masumin execute vfd_providers.scrap.run
2025-04-25 17:42:47,577 INFO /home/<USER>/.local/bin/bench use rent
2025-04-25 17:42:58,416 INFO /home/<USER>/.local/bin/bench start
2025-04-25 17:44:02,528 INFO /home/<USER>/.local/bin/bench use rent
2025-04-25 17:44:04,579 INFO /home/<USER>/.local/bin/bench start
2025-04-25 17:44:04,854 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-25 17:44:04,858 INFO /home/<USER>/.local/bin/bench watch
2025-04-25 17:44:04,862 INFO /home/<USER>/.local/bin/bench schedule
2025-04-25 17:44:04,872 INFO /home/<USER>/.local/bin/bench worker
2025-04-25 18:00:01,200 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-27 01:56:39,017 INFO /home/<USER>/.local/bin/bench use rent
2025-04-27 01:56:50,387 INFO /home/<USER>/.local/bin/bench start
2025-04-27 01:56:51,337 INFO /home/<USER>/.local/bin/bench worker
2025-04-27 01:56:51,358 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 01:56:51,372 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-27 01:56:51,398 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 12:33:32,655 INFO /home/<USER>/.local/bin/bench use rent
2025-04-27 12:33:38,043 INFO /home/<USER>/.local/bin/bench start
2025-04-27 12:33:38,706 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 12:33:38,717 INFO /home/<USER>/.local/bin/bench worker
2025-04-27 12:33:38,729 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-27 12:33:38,732 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 14:40:07,750 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-04-27 14:40:11,476 INFO /home/<USER>/.local/bin/bench start
2025-04-27 14:40:12,127 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 14:40:12,132 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 14:40:12,134 INFO /home/<USER>/.local/bin/bench worker
2025-04-27 14:40:12,135 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-27 15:39:21,978 INFO /home/<USER>/.local/bin/bench use rent
2025-04-27 15:39:25,148 INFO /home/<USER>/.local/bin/bench start
2025-04-27 15:39:25,598 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 15:39:25,598 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-27 15:39:25,600 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 15:39:25,612 INFO /home/<USER>/.local/bin/bench worker
2025-04-27 19:44:44,375 INFO /home/<USER>/.local/bin/bench use rent
2025-04-27 19:44:47,842 INFO /home/<USER>/.local/bin/bench start
2025-04-27 19:44:48,445 INFO /home/<USER>/.local/bin/bench schedule
2025-04-27 19:44:48,448 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-27 19:44:48,453 INFO /home/<USER>/.local/bin/bench watch
2025-04-27 19:44:48,457 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 09:51:13,624 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-28 09:51:19,765 INFO /home/<USER>/.local/bin/bench start
2025-04-28 09:51:20,176 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 09:51:20,180 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 09:51:20,185 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 09:51:20,186 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 10:27:03,892 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-28 10:27:08,263 INFO /home/<USER>/.local/bin/bench start
2025-04-28 10:27:08,564 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 10:27:08,570 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 10:27:08,595 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 10:27:08,608 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 11:02:57,510 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-28 11:03:01,444 INFO /home/<USER>/.local/bin/bench start
2025-04-28 11:03:01,903 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 11:03:01,914 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 11:03:01,937 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 11:03:01,944 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 11:57:08,300 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-04-28 11:57:08,314 LOG Getting LibMS
2025-04-28 11:57:08,314 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-04-28 11:59:05,457 LOG Installing library_management
2025-04-28 11:59:05,457 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/library_management 
2025-04-28 11:59:22,808 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/library_management && yarn install --check-files
2025-04-28 11:59:42,946 DEBUG bench build --app library_management
2025-04-28 11:59:43,648 INFO /home/<USER>/.local/bin/bench build --app library_management
2025-04-28 11:59:49,376 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-28 11:59:51,660 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-28 12:00:02,256 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 12:01:01,146 INFO /home/<USER>/.local/bin/bench migrate
2025-04-28 12:07:39,488 INFO /home/<USER>/.local/bin/bench migrate
2025-04-28 12:27:15,158 INFO /home/<USER>/.local/bin/bench start
2025-04-28 12:27:15,923 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 12:27:15,923 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 12:27:15,937 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 12:27:15,937 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 12:36:18,185 INFO /home/<USER>/.local/bin/bench start
2025-04-28 12:36:18,443 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 12:36:18,478 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 12:36:18,485 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 12:36:18,488 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 13:39:18,269 INFO /home/<USER>/.local/bin/bench start
2025-04-28 13:39:18,774 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 13:39:18,774 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 13:39:18,805 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 13:39:18,806 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 13:49:46,559 INFO /home/<USER>/.local/bin/bench start
2025-04-28 13:49:46,837 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 13:49:46,846 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 13:49:46,854 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 13:49:46,856 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 14:05:50,505 INFO /home/<USER>/.local/bin/bench start
2025-04-28 14:06:06,538 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 14:06:06,538 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 14:06:06,579 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 14:06:06,588 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 14:09:17,862 INFO /home/<USER>/.local/bin/bench start
2025-04-28 14:09:18,309 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 14:09:18,319 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 14:09:18,342 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 14:09:18,363 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 14:53:35,726 INFO /home/<USER>/.local/bin/bench start
2025-04-28 14:53:36,144 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 14:53:36,144 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 14:53:36,152 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 14:53:36,169 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 14:55:51,809 INFO /home/<USER>/.local/bin/bench start
2025-04-28 14:55:52,387 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 14:55:52,390 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 14:55:52,425 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 14:55:52,434 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 15:06:24,746 INFO /home/<USER>/.local/bin/bench start
2025-04-28 15:06:32,075 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 15:06:32,088 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 15:06:32,119 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 15:06:32,133 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 15:09:57,058 INFO /home/<USER>/.local/bin/bench start
2025-04-28 15:09:57,321 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 15:09:57,329 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 15:09:57,361 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 15:09:57,366 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 15:57:47,302 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-28 15:57:55,085 INFO /home/<USER>/.local/bin/bench start
2025-04-28 15:57:55,816 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 15:57:55,840 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 15:57:55,869 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 15:57:55,898 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 18:00:01,858 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 21:58:05,068 INFO /home/<USER>/.local/bin/bench use rentals
2025-04-28 21:58:09,879 INFO /home/<USER>/.local/bin/bench use rental
2025-04-28 21:58:15,481 INFO /home/<USER>/.local/bin/bench start
2025-04-28 21:58:16,081 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 21:58:16,082 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 21:58:16,082 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 21:58:16,083 INFO /home/<USER>/.local/bin/bench schedule
2025-04-28 22:04:29,217 INFO /home/<USER>/.local/bin/bench use rent
2025-04-28 22:04:31,871 INFO /home/<USER>/.local/bin/bench start
2025-04-28 22:04:32,154 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-28 22:04:32,158 INFO /home/<USER>/.local/bin/bench worker
2025-04-28 22:04:32,213 INFO /home/<USER>/.local/bin/bench watch
2025-04-28 22:04:32,215 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 00:00:01,930 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-29 06:00:01,344 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-29 06:29:23,328 INFO /home/<USER>/.local/bin/bench start
2025-04-29 06:29:23,747 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 06:29:23,750 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 06:29:23,761 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 06:29:23,765 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 06:30:48,883 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-29 06:30:52,071 INFO /home/<USER>/.local/bin/bench start
2025-04-29 06:30:52,371 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 06:30:52,385 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 06:30:52,415 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 06:30:52,421 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 06:32:05,050 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-29 06:32:08,396 INFO /home/<USER>/.local/bin/bench start
2025-04-29 07:07:59,391 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-29 07:08:07,999 INFO /home/<USER>/.local/bin/bench start
2025-04-29 07:08:08,559 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 07:08:08,564 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 07:08:08,579 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 07:08:08,590 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 07:12:16,935 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-04-29 07:12:19,862 INFO /home/<USER>/.local/bin/bench start
2025-04-29 07:12:20,154 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 07:12:20,154 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 07:12:20,177 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 07:12:20,177 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 07:14:28,717 INFO /home/<USER>/.local/bin/bench use beetle
2025-04-29 07:14:30,634 INFO /home/<USER>/.local/bin/bench start
2025-04-29 07:14:30,941 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 07:14:30,950 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 07:14:30,962 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 07:14:30,968 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 07:14:48,614 INFO /home/<USER>/.local/bin/bench migrate
2025-04-29 07:45:10,842 INFO /home/<USER>/.local/bin/bench migrate
2025-04-29 07:58:53,563 INFO /home/<USER>/.local/bin/bench start
2025-04-29 07:58:53,830 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 07:58:53,830 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 07:58:53,834 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 07:58:53,838 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 12:21:31,440 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-29 12:21:44,009 INFO /home/<USER>/.local/bin/bench start
2025-04-29 12:21:44,741 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 12:21:44,741 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 12:21:44,758 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 12:21:44,759 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 12:23:46,677 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-29 12:23:49,882 INFO /home/<USER>/.local/bin/bench start
2025-04-29 12:23:50,133 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 12:23:50,158 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 12:23:50,164 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 12:23:50,177 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 13:05:40,260 INFO /home/<USER>/.local/bin/bench start
2025-04-29 13:05:42,199 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 13:05:42,205 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 13:05:42,207 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 13:05:42,212 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 13:16:15,056 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Velocetec-Ltd/velocetec-erpnext.git
2025-04-29 13:16:15,068 LOG Getting velocetec-erpnext
2025-04-29 13:16:15,068 DEBUG cd ./apps && git clone https://github.com/Velocetec-Ltd/velocetec-erpnext.git  --depth 1 --origin upstream
2025-04-29 13:16:18,469 LOG Installing velocetec
2025-04-29 13:16:18,470 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/velocetec 
2025-04-29 13:16:23,424 DEBUG bench build --app velocetec
2025-04-29 13:16:23,556 INFO /home/<USER>/.local/bin/bench build --app velocetec
2025-04-29 13:16:25,854 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-29 13:16:26,489 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-04-29 13:20:17,754 INFO /home/<USER>/.local/bin/bench start
2025-04-29 13:20:18,099 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 13:20:18,110 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 13:20:18,117 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 13:20:18,131 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:01:06,321 INFO /home/<USER>/.local/bin/bench use rents
2025-04-29 17:01:09,904 INFO /home/<USER>/.local/bin/bench use rent
2025-04-29 17:01:15,112 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:01:15,479 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:01:15,482 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:01:15,509 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:01:15,537 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:01:25,264 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:01:25,558 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:01:25,593 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:01:25,598 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:01:25,605 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:12:25,084 INFO /home/<USER>/.local/bin/bench use rent
2025-04-29 17:12:28,902 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:12:29,510 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:12:29,514 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:12:29,552 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:12:29,636 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:13:05,435 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:13:06,019 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:13:06,026 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:13:06,037 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:13:06,039 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:13:29,074 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:13:29,540 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:13:29,554 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:13:29,557 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:13:29,582 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:14:19,767 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:14:20,117 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:14:20,131 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:14:20,140 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:14:22,351 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:14:22,728 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:14:22,730 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:14:22,739 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:14:22,751 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:14:37,438 INFO /home/<USER>/.local/bin/bench use rent
2025-04-29 17:14:40,899 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:14:41,212 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:14:41,246 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:14:41,246 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:14:41,258 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:15:13,039 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:15:13,431 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:15:13,434 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:15:13,443 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:15:13,489 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:15:20,008 INFO /home/<USER>/.local/bin/bench use rent
2025-04-29 17:15:22,454 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:15:22,853 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:15:22,861 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:15:22,869 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:15:22,884 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:16:22,278 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:16:22,631 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:16:22,639 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:16:22,640 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:16:22,651 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:39:31,303 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:39:31,988 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:39:31,992 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:39:31,999 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:39:32,005 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:49:35,016 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:49:35,430 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:49:35,471 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:49:35,474 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:49:35,487 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:53:15,766 INFO /home/<USER>/.local/bin/bench use rent
2025-04-29 17:53:18,072 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:53:18,475 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:53:18,477 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 17:53:18,514 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:53:18,523 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:57:26,897 INFO /home/<USER>/.local/bin/bench start
2025-04-29 17:57:27,338 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 17:57:27,345 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 17:57:27,346 INFO /home/<USER>/.local/bin/bench worker
2025-04-29 17:57:27,367 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 18:00:01,225 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-29 18:20:52,517 INFO /home/<USER>/.local/bin/bench start
2025-04-29 18:20:52,938 INFO /home/<USER>/.local/bin/bench watch
2025-04-29 18:20:52,943 INFO /home/<USER>/.local/bin/bench schedule
2025-04-29 18:20:52,953 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-29 18:20:52,976 INFO /home/<USER>/.local/bin/bench worker
2025-04-30 00:00:02,311 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 11:39:39,356 INFO /home/<USER>/.local/bin/bench use wasco
2025-04-30 11:39:44,325 INFO /home/<USER>/.local/bin/bench start
2025-04-30 11:39:44,707 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-30 11:39:44,714 INFO /home/<USER>/.local/bin/bench watch
2025-04-30 11:39:44,734 INFO /home/<USER>/.local/bin/bench schedule
2025-04-30 11:39:44,734 INFO /home/<USER>/.local/bin/bench worker
2025-04-30 12:00:01,222 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 13:22:27,846 INFO /home/<USER>/.local/bin/bench use libraryms
2025-04-30 13:22:29,988 INFO /home/<USER>/.local/bin/bench start
2025-04-30 13:22:30,276 INFO /home/<USER>/.local/bin/bench watch
2025-04-30 13:22:30,305 INFO /home/<USER>/.local/bin/bench worker
2025-04-30 13:22:30,315 INFO /home/<USER>/.local/bin/bench schedule
2025-04-30 13:22:30,317 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-30 18:00:01,488 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 18:22:24,714 INFO /home/<USER>/.local/bin/bench start
2025-04-30 18:22:25,158 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-30 18:22:25,171 INFO /home/<USER>/.local/bin/bench schedule
2025-04-30 18:22:25,173 INFO /home/<USER>/.local/bin/bench worker
2025-04-30 18:22:25,178 INFO /home/<USER>/.local/bin/bench watch
2025-05-02 09:43:02,121 INFO /home/<USER>/.local/bin/bench use libraryms
2025-05-02 09:43:09,102 INFO /home/<USER>/.local/bin/bench start
2025-05-02 09:43:09,643 INFO /home/<USER>/.local/bin/bench worker
2025-05-02 09:43:09,650 INFO /home/<USER>/.local/bin/bench watch
2025-05-02 09:43:09,661 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-02 09:43:09,667 INFO /home/<USER>/.local/bin/bench schedule
2025-05-02 12:00:01,653 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-02 15:11:26,432 INFO /home/<USER>/.local/bin/bench use libraryms
2025-05-02 15:11:30,447 INFO /home/<USER>/.local/bin/bench start
2025-05-02 15:11:31,158 INFO /home/<USER>/.local/bin/bench worker
2025-05-02 15:11:31,164 INFO /home/<USER>/.local/bin/bench watch
2025-05-02 15:11:31,167 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-02 15:11:31,199 INFO /home/<USER>/.local/bin/bench schedule
2025-05-02 15:26:50,429 INFO /home/<USER>/.local/bin/bench migrate
2025-05-02 15:59:14,669 INFO /home/<USER>/.local/bin/bench migrate
2025-05-02 16:43:37,328 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-05-02 16:43:37,356 LOG Getting LibMS
2025-05-02 16:43:37,356 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-05-02 16:43:56,038 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop executed with exit code 1
2025-05-02 16:43:56,836 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-02 16:44:06,492 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-05-02 16:44:08,816 INFO App moved from apps/LibMS to archived/apps/LibMS-2025-05-02
2025-05-02 16:44:08,821 LOG Getting LibMS
2025-05-02 16:44:08,821 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-05-02 16:44:48,347 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop executed with exit code 1
2025-05-02 16:44:49,202 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-02 16:53:21,402 INFO /home/<USER>/.local/bin/bench migrate
2025-05-02 18:00:01,299 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-04 00:00:01,970 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-04 01:06:05,675 INFO /home/<USER>/.local/bin/bench use rent
2025-05-04 01:06:13,343 INFO /home/<USER>/.local/bin/bench start
2025-05-04 01:06:13,708 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 01:06:13,718 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-04 01:06:13,738 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 01:06:13,747 INFO /home/<USER>/.local/bin/bench worker
2025-05-04 01:06:31,874 INFO /home/<USER>/.local/bin/bench start
2025-05-04 01:06:32,177 INFO /home/<USER>/.local/bin/bench serve --port 8001
2025-05-04 01:06:32,186 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 01:06:32,212 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 01:06:32,214 INFO /home/<USER>/.local/bin/bench worker
2025-05-04 11:55:25,767 INFO /home/<USER>/.local/bin/bench use rent
2025-05-04 11:55:32,469 INFO /home/<USER>/.local/bin/bench start
2025-05-04 11:55:33,112 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 11:55:33,126 INFO /home/<USER>/.local/bin/bench worker
2025-05-04 11:55:33,133 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 11:55:33,140 INFO /home/<USER>/.local/bin/bench serve --port 8001
2025-05-04 11:56:01,009 INFO /home/<USER>/.local/bin/bench start
2025-05-04 11:56:01,358 INFO /home/<USER>/.local/bin/bench worker
2025-05-04 11:56:01,361 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 11:56:01,365 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-04 11:56:01,393 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 12:00:04,050 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-04 12:06:20,976 INFO /home/<USER>/.local/bin/bench consle
2025-05-04 12:06:25,318 INFO /home/<USER>/.local/bin/bench console
2025-05-04 13:00:17,496 INFO /home/<USER>/.local/bin/bench use rent
2025-05-04 13:00:21,637 INFO /home/<USER>/.local/bin/bench start
2025-05-04 13:00:22,201 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 13:00:22,206 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 13:00:22,266 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-04 13:00:22,271 INFO /home/<USER>/.local/bin/bench worker
2025-05-04 23:01:15,627 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-05-04 23:01:25,866 INFO /home/<USER>/.local/bin/bench start
2025-05-04 23:01:26,996 INFO /home/<USER>/.local/bin/bench watch
2025-05-04 23:01:27,004 INFO /home/<USER>/.local/bin/bench schedule
2025-05-04 23:01:27,029 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-04 23:01:27,043 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 09:32:04,824 INFO /home/<USER>/.local/bin/bench use libraryms
2025-05-05 09:32:10,800 INFO /home/<USER>/.local/bin/bench start
2025-05-05 09:32:11,444 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 09:32:11,444 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 09:32:11,445 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 09:32:11,446 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 09:55:30,077 INFO /home/<USER>/.local/bin/bench get-app https://github.com/j0sh01/LibMS.git --branch develop
2025-05-05 09:55:30,099 LOG Getting LibMS
2025-05-05 09:55:30,100 DEBUG cd ./apps && git clone https://github.com/j0sh01/LibMS.git --branch develop --depth 1 --origin upstream
2025-05-05 09:56:44,212 LOG Installing library_management
2025-05-05 09:56:44,212 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/library_management 
2025-05-05 09:56:50,636 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/library_management && yarn install --check-files
2025-05-05 09:57:10,649 DEBUG bench build --app library_management
2025-05-05 09:57:11,447 INFO /home/<USER>/.local/bin/bench build --app library_management
2025-05-05 09:57:40,823 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-05 09:57:41,447 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-05 09:59:56,122 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 10:06:20,559 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 11:00:48,717 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 11:14:39,299 INFO /home/<USER>/.local/bin/bench use rent
2025-05-05 11:14:42,459 INFO /home/<USER>/.local/bin/bench start
2025-05-05 11:14:43,090 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 11:14:43,094 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 11:14:43,115 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 11:14:43,117 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 12:00:01,839 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-05 12:56:49,166 INFO /home/<USER>/.local/bin/bench use wasco
2025-05-05 12:56:55,987 INFO /home/<USER>/.local/bin/bench start
2025-05-05 12:57:09,668 INFO /home/<USER>/.local/bin/bench start
2025-05-05 13:09:13,572 INFO /home/<USER>/.local/bin/bench use wasco
2025-05-05 13:09:19,608 INFO /home/<USER>/.local/bin/bench start
2025-05-05 13:09:20,153 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 13:09:20,159 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 13:09:20,174 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 13:09:20,184 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 13:26:08,040 INFO /home/<USER>/.local/bin/bench migrate
2025-05-05 15:12:56,845 INFO /home/<USER>/.local/bin/bench use rent
2025-05-05 15:12:59,350 INFO /home/<USER>/.local/bin/bench start
2025-05-05 15:12:59,712 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 15:12:59,719 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 15:12:59,739 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 15:12:59,751 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 16:12:53,375 INFO /home/<USER>/.local/bin/bench use wasco
2025-05-05 16:12:57,401 INFO /home/<USER>/.local/bin/bench start
2025-05-05 16:12:57,865 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 16:12:57,867 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 16:12:57,919 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 16:12:57,923 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 16:21:38,867 INFO /home/<USER>/.local/bin/bench use wasco
2025-05-05 16:21:47,979 INFO /home/<USER>/.local/bin/bench start
2025-05-05 16:21:48,560 INFO /home/<USER>/.local/bin/bench watch
2025-05-05 16:21:48,560 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-05 16:21:48,609 INFO /home/<USER>/.local/bin/bench schedule
2025-05-05 16:21:48,609 INFO /home/<USER>/.local/bin/bench worker
2025-05-05 18:00:02,479 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-06 18:00:01,259 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-06 20:22:37,129 INFO /home/<USER>/.local/bin/bench use rent
2025-05-06 20:22:44,096 INFO /home/<USER>/.local/bin/bench start
2025-05-06 20:22:44,491 INFO /home/<USER>/.local/bin/bench schedule
2025-05-06 20:22:44,499 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-06 20:22:44,507 INFO /home/<USER>/.local/bin/bench watch
2025-05-06 20:22:44,514 INFO /home/<USER>/.local/bin/bench worker
2025-05-07 00:00:01,262 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-07 06:00:01,525 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-07 08:17:32,376 INFO /home/<USER>/.local/bin/bench use rent
2025-05-07 08:17:38,903 INFO /home/<USER>/.local/bin/bench start
2025-05-07 08:17:39,175 INFO /home/<USER>/.local/bin/bench watch
2025-05-07 08:17:39,175 INFO /home/<USER>/.local/bin/bench schedule
2025-05-07 08:17:39,206 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-07 08:17:39,210 INFO /home/<USER>/.local/bin/bench worker
2025-05-07 14:01:16,457 INFO /home/<USER>/.local/bin/bench use rent
2025-05-07 14:01:23,739 INFO /home/<USER>/.local/bin/bench start
2025-05-07 14:01:24,209 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-07 14:01:24,210 INFO /home/<USER>/.local/bin/bench watch
2025-05-07 14:01:24,266 INFO /home/<USER>/.local/bin/bench schedule
2025-05-07 14:01:24,270 INFO /home/<USER>/.local/bin/bench worker
2025-05-07 15:13:38,528 INFO /home/<USER>/.local/bin/bench use rent
2025-05-07 15:13:41,032 INFO /home/<USER>/.local/bin/bench start
2025-05-07 18:00:01,660 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-08 00:00:02,086 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-08 11:57:41,917 INFO /home/<USER>/.local/bin/bench use rent
2025-05-08 11:57:46,333 INFO /home/<USER>/.local/bin/bench start
2025-05-08 11:57:46,694 INFO /home/<USER>/.local/bin/bench watch
2025-05-08 11:57:46,694 INFO /home/<USER>/.local/bin/bench schedule
2025-05-08 11:57:46,732 INFO /home/<USER>/.local/bin/bench worker
2025-05-08 11:57:46,736 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-08 12:00:01,331 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-08 13:11:13,268 INFO /home/<USER>/.local/bin/bench get-app https://github.com/frappe/helpdesk.git --branch develop
2025-05-08 13:11:15,393 INFO App moved from apps/helpdesk to archived/apps/helpdesk-2025-05-08
2025-05-08 13:11:15,395 LOG Getting helpdesk
2025-05-08 13:11:15,395 DEBUG cd ./apps && git clone https://github.com/frappe/helpdesk.git --branch develop --depth 1 --origin upstream
2025-05-08 13:11:21,679 LOG Installing helpdesk
2025-05-08 13:11:21,680 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/helpdesk 
2025-05-08 13:11:25,377 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/helpdesk && yarn install --check-files
2025-05-08 13:13:38,440 DEBUG bench build --app helpdesk
2025-05-08 13:13:38,579 INFO /home/<USER>/.local/bin/bench build --app helpdesk
2025-05-08 13:14:11,689 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-08 13:14:12,576 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-08 18:00:01,622 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-09 00:00:01,189 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-09 18:00:01,843 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-11 12:41:09,111 INFO /home/<USER>/.local/bin/bench use rent
2025-05-11 12:41:13,526 INFO /home/<USER>/.local/bin/bench start
2025-05-11 12:41:13,894 INFO /home/<USER>/.local/bin/bench worker
2025-05-11 12:41:13,903 INFO /home/<USER>/.local/bin/bench watch
2025-05-11 12:41:13,921 INFO /home/<USER>/.local/bin/bench schedule
2025-05-11 12:41:13,921 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-14 15:43:07,186 INFO /home/<USER>/.local/bin/bench use wasco
2025-05-14 15:43:18,921 INFO /home/<USER>/.local/bin/bench start
2025-05-14 15:43:20,858 INFO /home/<USER>/.local/bin/bench watch
2025-05-14 15:43:20,895 INFO /home/<USER>/.local/bin/bench schedule
2025-05-14 15:43:20,902 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-14 15:43:20,903 INFO /home/<USER>/.local/bin/bench worker
2025-05-14 16:20:54,986 INFO /home/<USER>/.local/bin/bench use rent
2025-05-14 16:20:57,133 INFO /home/<USER>/.local/bin/bench start
2025-05-14 16:20:57,546 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-14 16:20:57,548 INFO /home/<USER>/.local/bin/bench watch
2025-05-14 16:20:57,607 INFO /home/<USER>/.local/bin/bench worker
2025-05-14 16:20:57,609 INFO /home/<USER>/.local/bin/bench schedule
2025-05-21 18:00:01,606 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-22 09:07:12,497 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-05-22 09:07:20,124 INFO /home/<USER>/.local/bin/bench start
2025-05-22 09:07:20,440 INFO /home/<USER>/.local/bin/bench schedule
2025-05-22 09:07:20,442 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-22 09:07:20,489 INFO /home/<USER>/.local/bin/bench watch
2025-05-22 09:07:20,490 INFO /home/<USER>/.local/bin/bench worker
2025-05-22 09:08:15,920 INFO /home/<USER>/.local/bin/bench build
2025-05-23 12:00:02,096 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-23 12:10:14,238 INFO /home/<USER>/.local/bin/bench use rent
2025-05-23 12:10:18,735 INFO /home/<USER>/.local/bin/bench start
2025-05-23 12:10:19,409 INFO /home/<USER>/.local/bin/bench schedule
2025-05-23 12:10:19,415 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-23 12:10:19,417 INFO /home/<USER>/.local/bin/bench watch
2025-05-23 12:10:19,448 INFO /home/<USER>/.local/bin/bench worker
2025-05-23 12:31:33,013 INFO /home/<USER>/.local/bin/bench use rent
2025-05-23 12:31:37,924 INFO /home/<USER>/.local/bin/bench start
2025-05-23 12:31:38,233 INFO /home/<USER>/.local/bin/bench worker
2025-05-23 12:31:38,234 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-23 12:31:38,246 INFO /home/<USER>/.local/bin/bench watch
2025-05-23 12:31:38,248 INFO /home/<USER>/.local/bin/bench schedule
2025-05-23 15:11:46,539 INFO /home/<USER>/.local/bin/bench use rents
2025-05-23 15:11:52,417 INFO /home/<USER>/.local/bin/bench use rent
2025-05-23 15:11:58,868 INFO /home/<USER>/.local/bin/bench start
2025-05-23 15:11:59,498 INFO /home/<USER>/.local/bin/bench worker
2025-05-23 15:11:59,504 INFO /home/<USER>/.local/bin/bench schedule
2025-05-23 15:11:59,514 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-23 15:11:59,519 INFO /home/<USER>/.local/bin/bench watch
2025-05-23 15:14:39,557 INFO /home/<USER>/.local/bin/bench use rent
2025-05-23 15:15:03,207 INFO /home/<USER>/.local/bin/bench set-config -g developer_mode 1
2025-05-23 15:15:45,522 INFO /home/<USER>/.local/bin/bench set-config -g developer_mode 1
2025-05-23 15:16:02,774 INFO /home/<USER>/.local/bin/bench -site rent set-config -g developer_mode 1
2025-05-23 15:16:17,986 INFO /home/<USER>/.local/bin/bench --site rent set-config -g developer_mode 1
2025-05-23 15:16:27,015 INFO /home/<USER>/.local/bin/bench migrate
2025-05-27 10:25:40,774 INFO /home/<USER>/.local/bin/bench use beetle
2025-05-27 10:25:46,966 INFO /home/<USER>/.local/bin/bench start
2025-05-27 10:25:47,640 INFO /home/<USER>/.local/bin/bench watch
2025-05-27 10:25:47,647 INFO /home/<USER>/.local/bin/bench worker
2025-05-27 10:25:47,692 INFO /home/<USER>/.local/bin/bench schedule
2025-05-27 10:25:47,702 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-27 11:05:55,980 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-05-27 11:06:00,263 INFO /home/<USER>/.local/bin/bench start
2025-05-27 11:06:00,600 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-27 11:06:00,608 INFO /home/<USER>/.local/bin/bench schedule
2025-05-27 11:06:00,634 INFO /home/<USER>/.local/bin/bench worker
2025-05-27 11:06:00,643 INFO /home/<USER>/.local/bin/bench watch
2025-05-27 12:00:01,197 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-27 16:54:19,076 INFO /home/<USER>/.local/bin/bench use rent
2025-05-27 16:54:22,334 INFO /home/<USER>/.local/bin/bench start
2025-05-27 16:54:22,708 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-27 16:54:22,730 INFO /home/<USER>/.local/bin/bench worker
2025-05-27 16:54:22,739 INFO /home/<USER>/.local/bin/bench schedule
2025-05-27 16:54:22,760 INFO /home/<USER>/.local/bin/bench watch
2025-05-27 18:00:02,204 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-28 09:19:11,074 INFO /home/<USER>/.local/bin/bench use rent
2025-05-28 09:19:17,542 INFO /home/<USER>/.local/bin/bench start
2025-05-28 09:19:18,115 INFO /home/<USER>/.local/bin/bench worker
2025-05-28 09:19:18,125 INFO /home/<USER>/.local/bin/bench schedule
2025-05-28 09:19:18,167 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-28 09:19:18,170 INFO /home/<USER>/.local/bin/bench watch
2025-05-28 12:00:01,920 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-28 18:00:01,375 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-29 12:00:02,046 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-29 14:25:23,929 INFO /home/<USER>/.local/bin/bench use libraryms
2025-05-29 14:25:31,662 INFO /home/<USER>/.local/bin/bench start
2025-05-29 14:25:32,379 INFO /home/<USER>/.local/bin/bench worker
2025-05-29 14:25:32,388 INFO /home/<USER>/.local/bin/bench watch
2025-05-29 14:25:32,422 INFO /home/<USER>/.local/bin/bench schedule
2025-05-29 14:25:32,446 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-29 14:29:00,711 INFO /home/<USER>/.local/bin/bench migrate
2025-05-29 18:00:01,967 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-30 11:53:32,198 INFO /home/<USER>/.local/bin/bench use libraryms
2025-05-30 11:53:37,348 INFO /home/<USER>/.local/bin/bench start
2025-05-30 11:53:37,874 INFO /home/<USER>/.local/bin/bench schedule
2025-05-30 11:53:37,879 INFO /home/<USER>/.local/bin/bench watch
2025-05-30 11:53:37,908 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-30 11:53:37,925 INFO /home/<USER>/.local/bin/bench worker
2025-05-30 12:00:23,069 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-30 12:46:27,150 INFO /home/<USER>/.local/bin/bench use certifications
2025-05-30 12:46:35,475 INFO /home/<USER>/.local/bin/bench start
2025-05-30 12:46:35,810 INFO /home/<USER>/.local/bin/bench schedule
2025-05-30 12:46:35,811 INFO /home/<USER>/.local/bin/bench watch
2025-05-30 12:46:35,813 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-05-30 12:46:35,818 INFO /home/<USER>/.local/bin/bench worker
2025-05-30 18:00:02,208 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-02 10:15:26,137 INFO /home/<USER>/.local/bin/bench use rent
2025-06-02 10:15:33,165 INFO /home/<USER>/.local/bin/bench start
2025-06-02 10:15:33,981 INFO /home/<USER>/.local/bin/bench watch
2025-06-02 10:15:33,982 INFO /home/<USER>/.local/bin/bench schedule
2025-06-02 10:15:34,014 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-02 10:15:34,029 INFO /home/<USER>/.local/bin/bench worker
2025-06-02 11:31:20,072 INFO /home/<USER>/.local/bin/bench use nephro1
2025-06-02 11:31:26,732 INFO /home/<USER>/.local/bin/bench start
2025-06-02 11:31:27,733 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-02 11:31:27,758 INFO /home/<USER>/.local/bin/bench worker
2025-06-02 11:31:27,780 INFO /home/<USER>/.local/bin/bench schedule
2025-06-02 11:31:27,783 INFO /home/<USER>/.local/bin/bench watch
2025-06-02 11:34:25,097 INFO /home/<USER>/.local/bin/bench get-app https://github.com/earthians/marley.git --branch version-15
2025-06-02 11:34:25,122 LOG Getting marley
2025-06-02 11:34:25,122 DEBUG cd ./apps && git clone https://github.com/earthians/marley.git --branch version-15 --depth 1 --origin upstream
2025-06-02 11:34:30,468 LOG Installing healthcare
2025-06-02 11:34:30,468 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-06-02 11:34:36,917 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r ./apps/healthcare/dev-requirements.txt
2025-06-02 11:34:55,790 DEBUG bench build --app healthcare
2025-06-02 11:34:56,004 INFO /home/<USER>/.local/bin/bench build --app healthcare
2025-06-02 11:35:16,758 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-02 11:35:17,272 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-02 11:36:42,617 INFO /home/<USER>/.local/bin/bench --site nephro1 install-app healthcare
2025-06-02 11:37:23,711 INFO /home/<USER>/.local/bin/bench use nephro1
2025-06-02 11:37:25,984 INFO /home/<USER>/.local/bin/bench start
2025-06-02 11:37:26,452 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-02 11:37:26,452 INFO /home/<USER>/.local/bin/bench watch
2025-06-02 11:37:26,474 INFO /home/<USER>/.local/bin/bench schedule
2025-06-02 11:37:26,476 INFO /home/<USER>/.local/bin/bench worker
2025-06-02 11:37:30,757 INFO /home/<USER>/.local/bin/bench --site nephro1 install-app healthcare
2025-06-02 11:37:39,392 INFO /home/<USER>/.local/bin/bench migrate
2025-06-02 11:39:52,792 INFO /home/<USER>/.local/bin/bench --site nephro1 set-admin-password aakvatech
2025-06-02 12:00:02,574 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-02 12:40:59,449 INFO /home/<USER>/.local/bin/bench migrate
2025-06-02 18:00:01,864 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-03 11:12:21,914 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-03 11:12:29,265 INFO /home/<USER>/.local/bin/bench start
2025-06-03 11:12:29,698 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 11:12:29,702 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-03 11:12:29,709 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 11:12:29,716 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 11:21:57,390 INFO /home/<USER>/.local/bin/bench start
2025-06-03 11:22:18,806 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 11:22:18,807 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 11:22:18,846 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-03 11:22:18,846 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 11:37:51,146 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-03 11:37:53,503 INFO /home/<USER>/.local/bin/bench start
2025-06-03 11:37:53,801 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 11:37:53,808 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 11:37:53,822 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 11:37:53,833 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-03 11:46:02,941 INFO /home/<USER>/.local/bin/bench console
2025-06-03 12:00:02,037 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-03 12:10:50,170 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz reinstall
2025-06-03 12:11:24,833 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz reinstall
2025-06-03 12:14:01,329 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-03 12:14:36,837 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app erpnext
2025-06-03 12:19:20,183 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app hrms
2025-06-03 12:20:16,861 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app csf_tz
2025-06-03 12:21:34,363 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz restore /home/<USER>/Downloads/20250102_130900-alphaassociates_co_tz-database.sql.gz
2025-06-03 12:22:07,575 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app payments
2025-06-03 12:23:48,643 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz restore set-admin-password aakvatech
2025-06-03 12:23:58,218 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz set-admin-password aakvatech
2025-06-03 12:24:25,708 INFO /home/<USER>/.local/bin/bench console
2025-06-03 12:26:53,906 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-03 12:27:13,731 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-03 12:28:01,849 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app lending
2025-06-03 12:28:01,856 WARNING /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app lending executed with exit code 2
2025-06-03 12:28:02,205 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 12:28:06,626 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove lending
2025-06-03 12:28:06,629 WARNING /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove lending executed with exit code 2
2025-06-03 12:28:06,914 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 12:28:10,053 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-03 12:29:10,118 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app biometric_client
2025-06-03 12:29:28,523 INFO /home/<USER>/.local/bin/bench migrate
2025-06-03 12:30:46,859 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-03 12:31:52,368 INFO /home/<USER>/.local/bin/bench migrate
2025-06-03 12:32:10,365 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-03 12:32:15,337 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-03 12:32:26,206 INFO /home/<USER>/.local/bin/bench get-app lending
2025-06-03 12:32:27,655 LOG Getting lending
2025-06-03 12:32:27,656 DEBUG cd ./apps && git clone https://github.com/frappe/lending.git  --depth 1 --origin upstream
2025-06-03 12:32:30,595 LOG Installing lending
2025-06-03 12:32:30,595 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-06-03 12:32:34,211 DEBUG bench build --app lending
2025-06-03 12:32:34,412 INFO /home/<USER>/.local/bin/bench build --app lending
2025-06-03 12:32:38,033 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-03 12:32:38,284 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 12:32:44,382 INFO /home/<USER>/.local/bin/bench migrate
2025-06-03 12:33:49,697 INFO /home/<USER>/.local/bin/bench get-app lending --branch version-15
2025-06-03 12:33:51,402 INFO App moved from apps/lending to archived/apps/lending-2025-06-03
2025-06-03 12:33:51,405 LOG Getting lending
2025-06-03 12:33:51,405 DEBUG cd ./apps && git clone https://github.com/frappe/lending.git --branch version-15 --depth 1 --origin upstream
2025-06-03 12:33:54,240 LOG Installing lending
2025-06-03 12:33:54,240 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-06-03 12:33:59,567 DEBUG bench build --app lending
2025-06-03 12:33:59,793 INFO /home/<USER>/.local/bin/bench build --app lending
2025-06-03 12:34:04,418 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-03 12:34:04,879 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 12:34:07,033 INFO /home/<USER>/.local/bin/bench migrate
2025-06-03 15:56:15,542 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/biometric-client.git
2025-06-03 15:56:15,559 LOG Getting biometric-client
2025-06-03 15:56:15,559 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/biometric-client.git  --depth 1 --origin upstream
2025-06-03 15:56:17,270 LOG Installing biometric_client
2025-06-03 15:56:17,271 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-06-03 15:56:22,073 DEBUG bench build --app biometric_client
2025-06-03 15:56:22,270 INFO /home/<USER>/.local/bin/bench build --app biometric_client
2025-06-03 15:56:25,642 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-03 15:56:25,994 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 17:17:36,406 INFO /home/<USER>/.local/bin/bench use libraryms
2025-06-03 17:17:45,006 INFO /home/<USER>/.local/bin/bench start
2025-06-03 17:17:45,513 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-03 17:17:45,530 INFO /home/<USER>/.local/bin/bench watch
2025-06-03 17:17:45,537 INFO /home/<USER>/.local/bin/bench schedule
2025-06-03 17:17:45,541 INFO /home/<USER>/.local/bin/bench worker
2025-06-03 17:23:22,839 INFO /home/<USER>/.local/bin/bench migrate
2025-06-03 18:00:01,418 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-04 11:05:01,639 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-04 11:05:06,394 INFO /home/<USER>/.local/bin/bench start
2025-06-04 11:05:06,742 INFO /home/<USER>/.local/bin/bench worker
2025-06-04 11:05:06,742 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-04 11:05:06,770 INFO /home/<USER>/.local/bin/bench schedule
2025-06-04 11:05:06,771 INFO /home/<USER>/.local/bin/bench watch
2025-06-04 11:44:48,006 INFO /home/<USER>/.local/bin/bench migrate
2025-06-04 12:00:02,020 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-04 13:20:33,266 INFO /home/<USER>/.local/bin/bench migrate
2025-06-04 15:31:31,885 INFO /home/<USER>/.local/bin/bench migrate
2025-06-04 18:00:01,339 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-05 09:32:48,937 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-05 09:32:54,698 INFO /home/<USER>/.local/bin/bench start
2025-06-05 09:32:55,322 INFO /home/<USER>/.local/bin/bench worker
2025-06-05 09:32:55,353 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-05 09:32:55,366 INFO /home/<USER>/.local/bin/bench schedule
2025-06-05 09:32:55,372 INFO /home/<USER>/.local/bin/bench watch
2025-06-05 09:43:28,648 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-05 09:43:33,597 INFO /home/<USER>/.local/bin/bench start
2025-06-05 09:43:33,876 INFO /home/<USER>/.local/bin/bench watch
2025-06-05 09:43:33,895 INFO /home/<USER>/.local/bin/bench schedule
2025-06-05 09:43:33,896 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-05 09:43:33,897 INFO /home/<USER>/.local/bin/bench worker
2025-06-05 12:00:02,673 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-05 13:07:32,704 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-05 13:07:37,944 INFO /home/<USER>/.local/bin/bench start
2025-06-05 13:07:38,482 INFO /home/<USER>/.local/bin/bench watch
2025-06-05 13:07:38,493 INFO /home/<USER>/.local/bin/bench worker
2025-06-05 13:07:38,533 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-05 13:07:38,541 INFO /home/<USER>/.local/bin/bench schedule
2025-06-05 13:12:29,697 INFO /home/<USER>/.local/bin/bench migrate
2025-06-05 18:00:01,360 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-06 12:00:01,659 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-06 12:44:45,977 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-06 12:44:50,899 INFO /home/<USER>/.local/bin/bench start
2025-06-06 12:44:51,523 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-06 12:44:51,530 INFO /home/<USER>/.local/bin/bench watch
2025-06-06 12:44:51,551 INFO /home/<USER>/.local/bin/bench schedule
2025-06-06 12:44:51,555 INFO /home/<USER>/.local/bin/bench worker
2025-06-06 12:45:45,301 INFO /home/<USER>/.local/bin/bench --site beetle restore /home/<USER>/Downloads/20250606_124231-beetle-av_frappe_cloud-database.sql.gz
2025-06-06 12:55:00,010 INFO /home/<USER>/.local/bin/bench --site beetle set-admin-password aakvatech
2025-06-06 15:10:55,056 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-06 15:11:02,082 INFO /home/<USER>/.local/bin/bench start
2025-06-06 15:11:02,916 INFO /home/<USER>/.local/bin/bench schedule
2025-06-06 15:11:02,920 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-06 15:11:02,935 INFO /home/<USER>/.local/bin/bench worker
2025-06-06 15:11:03,002 INFO /home/<USER>/.local/bin/bench watch
2025-06-06 18:00:01,893 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-09 11:02:29,600 INFO /home/<USER>/.local/bin/bench console
2025-06-09 12:00:01,539 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-09 15:07:15,748 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-09 15:07:23,288 INFO /home/<USER>/.local/bin/bench start
2025-06-09 15:07:23,839 INFO /home/<USER>/.local/bin/bench watch
2025-06-09 15:07:23,841 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-09 15:07:23,896 INFO /home/<USER>/.local/bin/bench schedule
2025-06-09 15:07:23,910 INFO /home/<USER>/.local/bin/bench worker
2025-06-09 18:00:02,111 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-10 09:18:38,328 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-10 09:18:44,873 INFO /home/<USER>/.local/bin/bench start
2025-06-10 09:18:45,554 INFO /home/<USER>/.local/bin/bench watch
2025-06-10 09:18:45,555 INFO /home/<USER>/.local/bin/bench worker
2025-06-10 09:18:45,588 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-10 09:18:45,589 INFO /home/<USER>/.local/bin/bench schedule
2025-06-10 12:00:02,469 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-10 18:00:01,289 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-10 18:27:13,872 INFO /home/<USER>/.local/bin/bench use rents
2025-06-10 18:27:16,874 INFO /home/<USER>/.local/bin/bench use rent
2025-06-10 18:27:22,088 INFO /home/<USER>/.local/bin/bench start
2025-06-10 18:27:22,595 INFO /home/<USER>/.local/bin/bench watch
2025-06-10 18:27:22,611 INFO /home/<USER>/.local/bin/bench worker
2025-06-10 18:27:22,611 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-10 18:27:22,618 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 09:24:23,964 INFO /home/<USER>/.local/bin/bench use rent
2025-06-11 09:24:33,014 INFO /home/<USER>/.local/bin/bench start
2025-06-11 09:24:33,544 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 09:24:33,569 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 09:24:33,593 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 09:24:33,598 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 12:00:03,058 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-11 12:50:29,208 INFO /home/<USER>/.local/bin/bench use rent
2025-06-11 12:50:32,020 INFO /home/<USER>/.local/bin/bench start
2025-06-11 12:50:32,559 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 12:50:32,561 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 12:50:32,563 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 12:50:32,565 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 14:06:07,280 INFO /home/<USER>/.local/bin/bench start
2025-06-11 14:06:07,595 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 14:06:07,627 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 14:06:07,631 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 14:06:07,633 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 14:28:25,560 INFO /home/<USER>/.local/bin/bench use rent
2025-06-11 14:28:28,076 INFO /home/<USER>/.local/bin/bench start
2025-06-11 14:28:28,556 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 14:28:28,560 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 14:28:28,568 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 14:28:28,587 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 15:06:28,251 INFO /home/<USER>/.local/bin/bench use certification
2025-06-11 15:06:31,020 INFO /home/<USER>/.local/bin/bench use certifications
2025-06-11 15:06:35,513 INFO /home/<USER>/.local/bin/bench start
2025-06-11 15:06:35,806 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 15:06:35,820 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 15:06:35,842 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 15:06:35,844 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 16:24:06,766 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-11 16:24:10,604 INFO /home/<USER>/.local/bin/bench start
2025-06-11 16:24:10,912 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 16:24:10,918 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 16:24:10,946 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 16:24:10,960 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 18:00:02,256 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-11 18:10:14,959 INFO /home/<USER>/.local/bin/bench use rent
2025-06-11 18:10:17,300 INFO /home/<USER>/.local/bin/bench start
2025-06-11 18:10:17,653 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 18:10:17,653 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 18:10:17,687 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 18:10:17,703 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 23:24:21,103 INFO /home/<USER>/.local/bin/bench use rent
2025-06-11 23:24:29,815 INFO /home/<USER>/.local/bin/bench start
2025-06-11 23:24:30,485 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 23:24:30,491 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 23:24:30,498 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 23:24:30,517 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 23:28:03,651 INFO /home/<USER>/.local/bin/bench start
2025-06-11 23:28:03,889 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 23:28:03,889 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 23:28:03,893 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 23:28:03,961 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 23:32:41,114 INFO /home/<USER>/.local/bin/bench start
2025-06-11 23:32:41,362 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 23:32:41,384 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 23:32:41,419 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 23:32:41,437 INFO /home/<USER>/.local/bin/bench schedule
2025-06-12 00:00:02,095 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-12 10:15:13,668 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-12 10:15:18,543 INFO /home/<USER>/.local/bin/bench start
2025-06-12 10:15:18,857 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-12 10:15:18,857 INFO /home/<USER>/.local/bin/bench schedule
2025-06-12 10:15:18,906 INFO /home/<USER>/.local/bin/bench watch
2025-06-12 10:15:18,910 INFO /home/<USER>/.local/bin/bench worker
2025-06-12 10:15:27,328 INFO /home/<USER>/.local/bin/bench migrate
2025-06-12 12:00:02,440 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-12 12:47:34,288 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/vfd-tz.git --branch version-15
2025-06-12 12:47:34,300 LOG Getting vfd-tz
2025-06-12 12:47:34,300 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/vfd-tz.git --branch version-15 --depth 1 --origin upstream
2025-06-12 12:47:36,280 WARNING /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/vfd-tz.git --branch version-15 executed with exit code 1
2025-06-12 12:47:36,633 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-12 12:47:52,471 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/vfd-tz.git --branch version-15
2025-06-12 12:47:52,483 LOG Getting vfd-tz
2025-06-12 12:47:52,483 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/vfd-tz.git --branch version-15 --depth 1 --origin upstream
2025-06-12 12:47:54,449 LOG Installing vfd_tz
2025-06-12 12:47:54,450 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_tz 
2025-06-12 12:47:57,920 DEBUG bench build --app vfd_tz
2025-06-12 12:47:58,059 INFO /home/<USER>/.local/bin/bench build --app vfd_tz
2025-06-12 12:48:00,522 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-12 12:48:00,852 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-12 12:57:05,688 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Aakvatech-Limited/PropMS.git --branch version-15
2025-06-12 12:57:05,699 LOG Getting PropMS
2025-06-12 12:57:05,699 DEBUG cd ./apps && git clone https://github.com/Aakvatech-Limited/PropMS.git --branch version-15 --depth 1 --origin upstream
2025-06-12 12:57:08,138 LOG Installing propms
2025-06-12 12:57:08,139 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/propms 
2025-06-12 12:57:10,422 DEBUG bench build --app propms
2025-06-12 12:57:10,540 INFO /home/<USER>/.local/bin/bench build --app propms
2025-06-12 12:57:12,412 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-12 12:57:12,712 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-12 13:26:15,670 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-12 13:26:19,496 INFO /home/<USER>/.local/bin/bench start
2025-06-12 13:26:19,792 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-12 13:26:19,794 INFO /home/<USER>/.local/bin/bench worker
2025-06-12 13:26:19,795 INFO /home/<USER>/.local/bin/bench watch
2025-06-12 13:26:19,799 INFO /home/<USER>/.local/bin/bench schedule
2025-06-12 16:00:10,943 INFO /home/<USER>/.local/bin/bench migrate
2025-06-12 16:03:48,653 INFO /home/<USER>/.local/bin/bench --site beetle mysql
2025-06-12 16:03:56,927 INFO /home/<USER>/.local/bin/bench --site beetle mysql
2025-06-12 16:04:05,583 INFO /home/<USER>/.local/bin/bench console
2025-06-12 16:05:11,421 INFO /home/<USER>/.local/bin/bench mariadb
2025-06-12 16:06:55,055 INFO /home/<USER>/.local/bin/bench migrate
2025-06-12 16:13:39,141 INFO /home/<USER>/.local/bin/bench --site all migrate
2025-06-12 16:15:40,835 INFO /home/<USER>/.local/bin/bench --site all migrate
2025-06-12 16:51:02,795 INFO /home/<USER>/.local/bin/bench --site localhost console -c print('default_withholding_receivable_account' in frappe.get_meta('Company').get_fieldnames())
2025-06-12 16:51:20,601 INFO /home/<USER>/.local/bin/bench --site localhost console
2025-06-12 16:51:50,105 INFO /home/<USER>/.local/bin/bench --site beetle console
2025-06-12 18:00:03,756 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-13 10:09:28,276 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-13 10:09:34,514 INFO /home/<USER>/.local/bin/bench start
2025-06-13 10:09:35,120 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 10:09:35,123 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 10:09:35,125 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 10:09:35,128 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 10:42:45,073 INFO /home/<USER>/.local/bin/bench use wasco
2025-06-13 10:42:48,289 INFO /home/<USER>/.local/bin/bench start
2025-06-13 10:42:48,689 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 10:42:48,689 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 10:42:48,697 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 10:42:48,727 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 11:04:37,588 INFO /home/<USER>/.local/bin/bench console
2025-06-13 12:00:01,705 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-13 12:31:57,536 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-13 12:32:00,702 INFO /home/<USER>/.local/bin/bench start
2025-06-13 12:32:01,527 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 12:32:01,527 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 12:32:01,557 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 12:32:01,573 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 12:38:24,314 INFO /home/<USER>/.local/bin/bench migrate
2025-06-13 14:31:09,874 INFO /home/<USER>/.local/bin/bench new-site gadget-mis
2025-06-13 14:33:01,863 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app erpnext
2025-06-13 14:34:41,326 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app hrms
2025-06-13 14:35:22,420 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app hrms
2025-06-13 14:35:46,799 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app csf_tz
2025-06-13 14:36:56,581 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app staff_loans
2025-06-13 14:37:17,734 INFO /home/<USER>/.local/bin/bench --site gadget-mis install-app fd_mis
2025-06-13 14:37:54,761 INFO /home/<USER>/.local/bin/bench --site gadget-mis restore /home/<USER>/Downloads/20250613_013010-gadgetshop-mis-av_frappe_cloud-database.sql.gz
2025-06-13 14:38:28,752 INFO /home/<USER>/.local/bin/bench --site gadget-mis restore /home/<USER>/Downloads/20250613_013010-gadgetshop-mis-av_frappe_cloud-database.sql.gz
2025-06-13 14:38:59,919 INFO /home/<USER>/.local/bin/bench start
2025-06-13 14:39:00,655 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 14:39:00,665 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 14:39:00,690 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 14:39:00,703 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 14:39:56,068 INFO /home/<USER>/.local/bin/bench use gadget-mis
2025-06-13 14:39:59,465 INFO /home/<USER>/.local/bin/bench start
2025-06-13 14:39:59,781 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 14:39:59,782 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 14:39:59,806 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 14:39:59,820 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 14:41:20,113 INFO /home/<USER>/.local/bin/bench mariadb
2025-06-13 14:42:38,151 INFO /home/<USER>/.local/bin/bench mariadb
2025-06-13 14:43:32,941 INFO /home/<USER>/.local/bin/bench migrate
2025-06-13 15:18:39,973 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-13 15:18:49,904 INFO /home/<USER>/.local/bin/bench start
2025-06-13 15:18:50,724 INFO /home/<USER>/.local/bin/bench worker
2025-06-13 15:18:50,728 INFO /home/<USER>/.local/bin/bench watch
2025-06-13 15:18:50,730 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-13 15:18:50,730 INFO /home/<USER>/.local/bin/bench schedule
2025-06-13 17:14:41,202 INFO /home/<USER>/.local/bin/bench migrate
2025-06-13 18:00:01,423 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-16 09:43:14,723 INFO /home/<USER>/.local/bin/bench use mamsumin
2025-06-16 09:43:19,344 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-16 09:43:23,303 INFO /home/<USER>/.local/bin/bench start
2025-06-16 09:43:23,784 INFO /home/<USER>/.local/bin/bench watch
2025-06-16 09:43:23,795 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-16 09:43:23,814 INFO /home/<USER>/.local/bin/bench schedule
2025-06-16 09:43:23,816 INFO /home/<USER>/.local/bin/bench worker
2025-06-16 09:54:16,118 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-16 09:54:19,479 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-16 09:54:21,372 INFO /home/<USER>/.local/bin/bench start
2025-06-16 09:54:21,654 INFO /home/<USER>/.local/bin/bench schedule
2025-06-16 09:54:21,656 INFO /home/<USER>/.local/bin/bench worker
2025-06-16 09:54:21,676 INFO /home/<USER>/.local/bin/bench watch
2025-06-16 09:54:21,677 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-16 12:00:02,332 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-16 15:04:50,257 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-16 15:04:52,784 INFO /home/<USER>/.local/bin/bench start
2025-06-16 15:04:53,082 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-16 15:04:53,103 INFO /home/<USER>/.local/bin/bench schedule
2025-06-16 15:04:53,108 INFO /home/<USER>/.local/bin/bench watch
2025-06-16 15:04:53,132 INFO /home/<USER>/.local/bin/bench worker
2025-06-16 15:08:31,822 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app lending
2025-06-16 15:08:31,826 WARNING /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app lending executed with exit code 2
2025-06-16 15:08:34,692 INFO A newer version of bench is available: 5.23.0 → 5.25.4
2025-06-16 15:09:08,056 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-16 15:10:14,003 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending
2025-06-16 15:10:44,825 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app lending --force
2025-06-16 15:16:58,824 INFO /home/<USER>/.local/bin/bench use rents
2025-06-16 15:17:01,721 INFO /home/<USER>/.local/bin/bench use rent
2025-06-16 15:17:04,272 INFO /home/<USER>/.local/bin/bench start
2025-06-16 15:17:04,536 INFO /home/<USER>/.local/bin/bench watch
2025-06-16 15:17:04,557 INFO /home/<USER>/.local/bin/bench worker
2025-06-16 15:17:04,573 INFO /home/<USER>/.local/bin/bench schedule
2025-06-16 15:17:04,576 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-16 18:00:01,569 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-17 09:09:33,218 INFO /home/<USER>/.local/bin/bench use rent
2025-06-17 09:09:52,532 INFO /home/<USER>/.local/bin/bench start
2025-06-17 09:09:53,310 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 09:09:53,320 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 09:09:53,331 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 09:09:53,360 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 12:00:02,118 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-17 16:18:25,423 INFO /home/<USER>/.local/bin/bench start
2025-06-17 16:18:25,839 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 16:18:25,841 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 16:18:25,841 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 16:18:25,850 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 17:29:42,513 INFO /home/<USER>/.local/bin/bench --site gadget-mis list-apps
2025-06-17 17:31:07,852 INFO /home/<USER>/.local/bin/bench new-site mis
2025-06-17 17:32:03,968 INFO /home/<USER>/.local/bin/bench --site mis install-app erpnext
2025-06-17 17:33:26,930 INFO /home/<USER>/.local/bin/bench --site mis install-app hrms
2025-06-17 17:34:03,348 INFO /home/<USER>/.local/bin/bench --site mis install-app hrms
2025-06-17 17:34:10,585 INFO /home/<USER>/.local/bin/bench --site mis install-app csf_tz
2025-06-17 17:35:06,044 INFO /home/<USER>/.local/bin/bench --site mis install-app fd_mis
2025-06-17 17:35:27,441 INFO /home/<USER>/.local/bin/bench --site mis install-app staff_loans
2025-06-17 17:36:06,269 INFO /home/<USER>/.local/bin/bench --site restore /home/<USER>/Downloads/20250617_023011-gadgetshop-mis-av_frappe_cloud-database.sql.gz
2025-06-17 17:36:31,613 INFO /home/<USER>/.local/bin/bench --site mis restore /home/<USER>/Downloads/20250617_023011-gadgetshop-mis-av_frappe_cloud-database.sql.gz
2025-06-17 17:38:10,256 INFO /home/<USER>/.local/bin/bench --site mis restore /home/<USER>/Downloads/20250617_023011-gadgetshop-mis-av_frappe_cloud-database.sql.gz
2025-06-17 17:38:30,473 INFO /home/<USER>/.local/bin/bench --site mis restore /home/<USER>/Downloads/20250617_023011-gadgetshop-mis-av_frappe_cloud-database.sql.gz --force
2025-06-17 17:38:54,136 INFO /home/<USER>/.local/bin/bench use mis
2025-06-17 17:38:57,606 INFO /home/<USER>/.local/bin/bench start
2025-06-17 17:38:58,016 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 17:38:58,016 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 17:38:58,038 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 17:38:58,044 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 17:39:27,039 INFO /home/<USER>/.local/bin/bench migrate
2025-06-17 17:45:01,979 INFO /home/<USER>/.local/bin/bench --site mis reinstall
2025-06-17 18:00:02,152 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-17 18:03:11,027 INFO /home/<USER>/.local/bin/bench use mis
2025-06-17 18:03:15,691 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:03:16,252 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 18:03:16,254 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 18:03:16,255 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 18:03:16,258 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 18:03:53,793 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:03:54,094 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 18:03:54,100 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 18:03:54,106 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 18:03:54,120 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 18:04:14,783 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-17 18:04:16,861 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:04:17,149 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 18:04:17,163 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 18:04:17,190 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 18:04:17,199 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 18:04:48,579 INFO /home/<USER>/.local/bin/bench build
2025-06-17 18:10:28,889 INFO /home/<USER>/.local/bin/bench use mis
2025-06-17 18:10:32,805 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:10:33,072 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 18:10:33,078 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 18:10:33,088 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 18:10:33,093 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 18:11:07,440 INFO /home/<USER>/.local/bin/bench --site mis set-maintenance-mode off
2025-06-17 18:11:53,312 INFO /home/<USER>/.local/bin/bench --site mis install-app erpnext
2025-06-17 18:12:18,383 INFO /home/<USER>/.local/bin/bench --site mis reinstall
2025-06-17 18:14:18,998 INFO /home/<USER>/.local/bin/bench --site mis install-app erpnext
2025-06-17 18:15:43,854 INFO /home/<USER>/.local/bin/bench --site mis install-app hrms
2025-06-17 18:16:37,894 INFO /home/<USER>/.local/bin/bench --site mis install-app csf_tz
2025-06-17 18:18:08,282 INFO /home/<USER>/.local/bin/bench --site mis install-app staff_loans
2025-06-17 18:18:18,298 INFO /home/<USER>/.local/bin/bench --site mis install-app fd_mis
2025-06-17 18:18:33,306 INFO /home/<USER>/.local/bin/bench --site mis list apps
2025-06-17 18:18:37,335 INFO /home/<USER>/.local/bin/bench --site mis list-apps
2025-06-17 18:19:24,255 INFO /home/<USER>/.local/bin/bench --site mis set-admin-password aakvatech
2025-06-17 18:22:23,079 INFO /home/<USER>/.local/bin/bench --site mis --force restore /home/<USER>/Downloads/20250617_023011-gadgetshop-mis-av_frappe_cloud-database.sql
2025-06-17 18:23:15,764 INFO /home/<USER>/.local/bin/bench build
2025-06-17 18:31:18,181 INFO /home/<USER>/.local/bin/bench mariadb
2025-06-17 18:31:30,025 INFO /home/<USER>/.local/bin/bench mariadb
2025-06-17 18:36:25,600 INFO /home/<USER>/.local/bin/bench --site mis mariadb
2025-06-17 18:50:28,749 INFO /home/<USER>/.local/bin/bench --site rent
2025-06-17 18:50:36,213 INFO /home/<USER>/.local/bin/bench use rent
2025-06-17 18:50:41,396 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:50:52,506 INFO /home/<USER>/.local/bin/bench use rent
2025-06-17 18:50:55,449 INFO /home/<USER>/.local/bin/bench start
2025-06-17 18:50:55,710 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 18:50:55,729 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 18:50:55,737 INFO /home/<USER>/.local/bin/bench schedule
2025-06-17 18:50:55,751 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 23:30:23,094 INFO /home/<USER>/.local/bin/bench use rent
2025-06-17 23:30:31,454 INFO /home/<USER>/.local/bin/bench start
2025-06-17 23:30:32,109 INFO /home/<USER>/.local/bin/bench worker
2025-06-17 23:30:32,110 INFO /home/<USER>/.local/bin/bench watch
2025-06-17 23:30:32,117 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-17 23:30:32,123 INFO /home/<USER>/.local/bin/bench schedule
2025-06-18 00:00:01,805 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-18 09:23:54,562 INFO /home/<USER>/.local/bin/bench use rent
2025-06-18 09:24:11,499 INFO /home/<USER>/.local/bin/bench start
2025-06-18 09:24:12,700 INFO /home/<USER>/.local/bin/bench schedule
2025-06-18 09:24:12,862 INFO /home/<USER>/.local/bin/bench watch
2025-06-18 09:24:12,879 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-18 09:24:12,951 INFO /home/<USER>/.local/bin/bench worker
2025-06-18 12:00:02,382 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-18 14:30:07,001 INFO /home/<USER>/.local/bin/bench use rent
2025-06-18 14:30:12,526 INFO /home/<USER>/.local/bin/bench start
2025-06-18 14:30:13,291 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-18 14:30:13,298 INFO /home/<USER>/.local/bin/bench worker
2025-06-18 14:30:13,347 INFO /home/<USER>/.local/bin/bench watch
2025-06-18 14:30:13,370 INFO /home/<USER>/.local/bin/bench schedule
2025-06-18 18:00:01,912 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-19 09:37:14,816 INFO /home/<USER>/.local/bin/bench use rent
2025-06-19 09:37:19,608 INFO /home/<USER>/.local/bin/bench start
2025-06-19 09:37:20,228 INFO /home/<USER>/.local/bin/bench watch
2025-06-19 09:37:20,236 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-19 09:37:20,237 INFO /home/<USER>/.local/bin/bench schedule
2025-06-19 09:37:20,258 INFO /home/<USER>/.local/bin/bench worker
2025-06-19 12:00:02,207 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-19 14:26:06,880 INFO /home/<USER>/.local/bin/bench use rent
2025-06-19 14:26:12,936 INFO /home/<USER>/.local/bin/bench start
2025-06-19 14:26:13,289 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-19 14:26:13,295 INFO /home/<USER>/.local/bin/bench schedule
2025-06-19 14:26:13,317 INFO /home/<USER>/.local/bin/bench watch
2025-06-19 14:26:13,319 INFO /home/<USER>/.local/bin/bench worker
2025-06-19 15:51:16,196 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-19 15:51:20,118 INFO /home/<USER>/.local/bin/bench start
2025-06-19 15:51:20,424 INFO /home/<USER>/.local/bin/bench worker
2025-06-19 15:51:20,433 INFO /home/<USER>/.local/bin/bench schedule
2025-06-19 15:51:20,436 INFO /home/<USER>/.local/bin/bench watch
2025-06-19 15:51:20,462 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-19 18:00:01,754 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-20 10:54:03,423 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-20 10:54:13,213 INFO /home/<USER>/.local/bin/bench start
2025-06-20 10:54:14,650 INFO /home/<USER>/.local/bin/bench worker
2025-06-20 10:54:14,650 INFO /home/<USER>/.local/bin/bench watch
2025-06-20 10:54:14,650 INFO /home/<USER>/.local/bin/bench schedule
2025-06-20 10:54:14,650 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-20 12:00:02,022 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-20 18:00:01,578 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-23 09:58:12,686 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-23 09:58:18,824 INFO /home/<USER>/.local/bin/bench start
2025-06-23 09:58:19,364 INFO /home/<USER>/.local/bin/bench worker
2025-06-23 09:58:19,364 INFO /home/<USER>/.local/bin/bench schedule
2025-06-23 09:58:19,410 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-23 09:58:19,415 INFO /home/<USER>/.local/bin/bench watch
2025-06-23 12:00:02,005 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-23 13:37:47,793 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-23 13:37:52,191 INFO /home/<USER>/.local/bin/bench start
2025-06-23 13:39:26,931 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-23 13:39:33,444 INFO /home/<USER>/.local/bin/bench start
2025-06-23 13:39:34,290 INFO /home/<USER>/.local/bin/bench worker
2025-06-23 13:39:34,321 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-23 13:39:34,498 INFO /home/<USER>/.local/bin/bench watch
2025-06-23 13:39:34,569 INFO /home/<USER>/.local/bin/bench schedule
2025-06-23 14:13:19,259 INFO /home/<USER>/.local/bin/bench console
2025-06-23 14:44:12,635 INFO /home/<USER>/.local/bin/bench console
2025-06-23 14:58:12,093 INFO /home/<USER>/.local/bin/bench console
2025-06-23 15:10:34,207 INFO /home/<USER>/.local/bin/bench console
2025-06-23 15:18:14,119 INFO /home/<USER>/.local/bin/bench console
2025-06-23 15:24:46,375 INFO /home/<USER>/.local/bin/bench console
2025-06-23 15:52:31,779 INFO /home/<USER>/.local/bin/bench console
2025-06-23 16:10:58,137 INFO /home/<USER>/.local/bin/bench migrate
2025-06-23 17:14:15,866 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-23 17:14:21,604 INFO /home/<USER>/.local/bin/bench start
2025-06-23 17:14:22,355 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-23 17:14:22,399 INFO /home/<USER>/.local/bin/bench schedule
2025-06-23 17:14:22,440 INFO /home/<USER>/.local/bin/bench worker
2025-06-23 17:14:22,490 INFO /home/<USER>/.local/bin/bench watch
2025-06-23 18:00:01,925 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-24 09:17:28,450 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-24 09:17:34,503 INFO /home/<USER>/.local/bin/bench start
2025-06-24 09:17:35,095 INFO /home/<USER>/.local/bin/bench schedule
2025-06-24 09:17:35,106 INFO /home/<USER>/.local/bin/bench worker
2025-06-24 09:17:35,145 INFO /home/<USER>/.local/bin/bench watch
2025-06-24 09:17:35,145 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-24 12:00:01,661 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-24 18:00:02,152 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-24 23:35:06,146 INFO /home/<USER>/.local/bin/bench use rent
2025-06-24 23:35:45,520 INFO /home/<USER>/.local/bin/bench start
2025-06-24 23:35:47,868 INFO /home/<USER>/.local/bin/bench worker
2025-06-24 23:35:47,884 INFO /home/<USER>/.local/bin/bench schedule
2025-06-24 23:35:47,900 INFO /home/<USER>/.local/bin/bench watch
2025-06-24 23:35:47,909 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-25 00:00:01,663 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-25 09:26:42,318 INFO /home/<USER>/.local/bin/bench use beetle
2025-06-25 09:26:52,409 INFO /home/<USER>/.local/bin/bench start
2025-06-25 09:26:53,221 INFO /home/<USER>/.local/bin/bench worker
2025-06-25 09:26:53,237 INFO /home/<USER>/.local/bin/bench watch
2025-06-25 09:26:53,252 INFO /home/<USER>/.local/bin/bench schedule
2025-06-25 09:26:53,405 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-25 09:29:16,052 INFO /home/<USER>/.local/bin/bench migrate
2025-06-25 09:53:40,893 INFO /home/<USER>/.local/bin/bench migrate
2025-06-25 09:54:45,851 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-25 09:54:49,569 INFO /home/<USER>/.local/bin/bench start
2025-06-25 09:54:50,267 INFO /home/<USER>/.local/bin/bench watch
2025-06-25 09:54:50,275 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-25 09:54:50,279 INFO /home/<USER>/.local/bin/bench schedule
2025-06-25 09:54:50,290 INFO /home/<USER>/.local/bin/bench worker
2025-06-25 09:59:34,373 INFO /home/<USER>/.local/bin/bench migrate
2025-06-25 10:05:02,100 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz reinstall
2025-06-25 10:09:10,333 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-aaps
2025-06-25 10:09:16,024 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-25 10:11:05,321 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-from-installed-apps lending
2025-06-25 10:11:14,577 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-from-installed-apps core
2025-06-25 10:11:20,026 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-25 10:11:47,570 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app csf_tz
2025-06-25 10:12:54,175 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-25 10:13:08,612 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app biometric_client
2025-06-25 10:13:14,229 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-06-25 10:13:42,508 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz restore /home/<USER>/Downloads/20250625_000004-alphaassociates_co_tz-database.sql.gz
2025-06-25 10:30:10,288 INFO /home/<USER>/.local/bin/bench new-site home
2025-06-25 10:31:24,752 INFO /home/<USER>/.local/bin/bench --site home install-app erpnext
2025-06-25 10:33:46,030 INFO /home/<USER>/.local/bin/bench --site home install-app hrms
2025-06-25 10:37:14,670 INFO /home/<USER>/.local/bin/bench --site home install-app payware
2025-06-25 10:37:26,442 INFO /home/<USER>/.local/bin/bench --site home install-app csf_tz
2025-06-25 10:38:45,908 INFO /home/<USER>/.local/bin/bench --site home install-app biometric_client
2025-06-25 10:42:14,476 INFO /home/<USER>/.local/bin/bench --site home restore /home/<USER>/Downloads/20250625_000004-alphaassociates_co_tz-database.sql.gz
2025-06-25 10:42:49,344 INFO /home/<USER>/.local/bin/bench use home
2025-06-25 10:43:00,448 INFO /home/<USER>/.local/bin/bench start
2025-06-25 10:43:00,901 INFO /home/<USER>/.local/bin/bench schedule
2025-06-25 10:43:00,918 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-25 10:43:00,938 INFO /home/<USER>/.local/bin/bench watch
2025-06-25 10:43:00,947 INFO /home/<USER>/.local/bin/bench worker
2025-06-25 10:43:29,803 INFO /home/<USER>/.local/bin/bench use home
2025-06-25 10:43:54,938 INFO /home/<USER>/.local/bin/bench --site home reinstall
2025-06-25 10:44:02,190 INFO /home/<USER>/.local/bin/bench --site home reinstall
2025-06-25 10:44:34,367 INFO /home/<USER>/.local/bin/bench --site home install-app frappe version-15
2025-06-25 10:46:32,704 INFO /home/<USER>/.local/bin/bench use home
2025-06-25 10:47:57,297 INFO /home/<USER>/.local/bin/bench update
2025-06-25 10:47:59,600 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-25 10:48:10,297 WARNING /home/<USER>/.local/bin/bench update executed with exit code 1
2025-06-25 10:48:11,415 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:48:54,944 INFO /home/<USER>/.local/bin/bench --site home install-app hrms
2025-06-25 10:49:39,279 INFO /home/<USER>/.local/bin/bench clear-cache
2025-06-25 10:49:40,092 INFO /home/<USER>/.local/bin/bench restart
2025-06-25 10:49:40,103 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-25 10:49:40,412 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:50:47,283 INFO /home/<USER>/.local/bin/bench update --reset
2025-06-25 10:50:49,382 WARNING /home/<USER>/.local/bin/bench update --reset executed with exit code 1
2025-06-25 10:50:49,680 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:52:50,507 INFO /home/<USER>/.local/bin/bench remove-app erpnext
2025-06-25 10:52:50,660 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site all list-apps --format json
2025-06-25 10:52:51,471 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site hr list-apps
2025-06-25 10:52:52,126 WARNING /home/<USER>/.local/bin/bench remove-app erpnext executed with exit code 1
2025-06-25 10:52:52,891 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:52:53,089 INFO /home/<USER>/.local/bin/bench get-app erpnext --branch version-15
2025-06-25 10:52:55,492 INFO App moved from apps/erpnext to archived/apps/erpnext-2025-06-25
2025-06-25 10:52:55,502 LOG Getting erpnext
2025-06-25 10:52:55,503 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-06-25 10:53:21,608 LOG Installing erpnext
2025-06-25 10:53:21,608 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext 
2025-06-25 10:53:33,651 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && yarn install --check-files
2025-06-25 10:53:35,708 DEBUG bench build --app erpnext
2025-06-25 10:53:35,924 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app erpnext
2025-06-25 10:53:37,172 WARNING bench build --app erpnext executed with exit code 1
2025-06-25 10:53:37,173 WARNING /home/<USER>/.local/bin/bench get-app erpnext --branch version-15 executed with exit code 1
2025-06-25 10:53:37,615 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:53:37,905 INFO /home/<USER>/.local/bin/bench install-app erpnext
2025-06-25 10:54:03,282 INFO /home/<USER>/.local/bin/bench build --app erpnext
2025-06-25 10:54:07,974 INFO /home/<USER>/.local/bin/bench build
2025-06-25 10:56:56,178 INFO /home/<USER>/.local/bin/bench new-site new
2025-06-25 10:58:02,473 INFO /home/<USER>/.local/bin/bench update --patch
2025-06-25 10:58:04,326 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-25 10:58:15,053 WARNING /home/<USER>/.local/bin/bench update --patch executed with exit code 1
2025-06-25 10:58:15,790 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:59:02,544 INFO /home/<USER>/.local/bin/bench update --patch
2025-06-25 10:59:04,881 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-25 10:59:15,572 WARNING /home/<USER>/.local/bin/bench update --patch executed with exit code 1
2025-06-25 10:59:15,895 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 10:59:43,972 INFO /home/<USER>/.local/bin/bench update
2025-06-25 10:59:45,379 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-25 10:59:56,162 WARNING /home/<USER>/.local/bin/bench update executed with exit code 1
2025-06-25 10:59:56,512 INFO A newer version of bench is available: 5.23.0 → 5.25.7
2025-06-25 11:03:56,111 INFO /home/<USER>/.local/bin/bench new-site home
2025-06-25 11:04:16,056 INFO /home/<USER>/.local/bin/bench use home
2025-06-25 11:04:21,775 INFO /home/<USER>/.local/bin/bench start
2025-06-25 11:04:22,073 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-25 11:04:22,077 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 11:04:22,104 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 11:04:22,118 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 11:04:55,101 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site home list-apps
2025-06-25 11:05:09,873 INFO /home/<USER>/.local/bin/bench use masumin
2025-06-25 11:05:13,503 INFO /home/<USER>/.local/bin/bench start
2025-06-25 11:05:13,788 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 11:05:13,829 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-25 11:05:13,835 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 11:05:13,844 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 11:05:44,684 INFO /home/<USER>/.local/bin/bench new-site special
2025-06-25 11:06:51,426 INFO /home/<USER>/.local/bin/bench --site special install-app erpnext
2025-06-25 11:08:04,375 INFO /home/<USER>/.local/bin/bench --site special install-app hrms
2025-06-25 11:08:44,000 INFO /home/<USER>/.local/bin/bench --site special install-app hrms
2025-06-25 11:08:55,674 INFO /home/<USER>/.local/bin/bench --site special install-app csf_tz
2025-06-25 11:09:54,731 INFO /home/<USER>/.local/bin/bench --site special install-app payware
2025-06-25 11:10:09,940 INFO /home/<USER>/.local/bin/bench --site special install-app biometric_client
2025-06-25 11:10:25,357 INFO /home/<USER>/.local/bin/bench --site special list-apps
2025-06-25 11:11:04,505 INFO /home/<USER>/.local/bin/bench --site special restore /home/<USER>/Downloads/20250625_000004-alphaassociates_co_tz-database.sql.gz
2025-06-25 11:13:00,205 INFO /home/<USER>/.local/bin/bench use special
2025-06-25 11:13:04,029 INFO /home/<USER>/.local/bin/bench start
2025-06-25 11:13:04,354 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-25 11:13:04,354 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 11:13:04,371 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 11:13:04,373 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 11:13:38,342 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special set-maintenance-mode off
2025-06-25 11:14:50,013 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site remove-from-installed-apps lending
2025-06-25 11:15:00,250 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special remove-from-installed-apps lending
2025-06-25 11:15:31,983 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special remove-app lending
2025-06-25 11:15:31,986 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special remove-app lending executed with exit code 2
2025-06-25 11:16:19,859 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special uninstall-app lending
2025-06-25 11:16:45,016 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-25 11:21:56,172 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special restore /home/<USER>/Downloads/20250102_130900-alphaassociates_co_tz-database.sql.gz
2025-06-25 11:24:52,952 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special uninstall-app lending
2025-06-25 11:26:27,920 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special restore /home/<USER>/Downloads/20241022_154543-alpha14-av_frappe_cloud-database.sql.gz
2025-06-25 11:29:52,267 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special remove-from-installed-apps alpha_tz
2025-06-25 11:30:20,160 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site special remove-from-installed-apps indicator_color
2025-06-25 11:30:40,920 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-25 11:40:35,883 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-06-25 11:40:39,562 INFO /home/<USER>/.local/bin/bench start
2025-06-25 11:40:39,881 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-25 11:40:39,886 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 11:40:39,893 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 11:40:39,923 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 11:41:55,408 INFO /home/<USER>/.local/bin/bench --site special reinstall
2025-06-25 12:00:01,739 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-25 14:28:35,493 INFO /home/<USER>/.local/bin/bench use tervis
2025-06-25 14:28:40,310 INFO /home/<USER>/.local/bin/bench start
2025-06-25 14:28:40,712 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 14:28:40,716 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 14:28:40,723 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-25 14:28:40,728 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 14:33:48,592 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build
2025-06-25 14:42:47,959 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use tervis
2025-06-25 14:43:05,802 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-25 14:43:06,484 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-25 14:43:06,517 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-25 14:43:06,533 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-25 14:43:06,545 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-26 10:27:50,688 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use nephro1
2025-06-26 10:27:57,921 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-26 10:27:58,388 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-26 10:27:58,390 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-26 10:27:58,397 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-26 10:27:58,404 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-26 10:34:38,339 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20250626_000002-nephron1_com-database.sql.gz
2025-06-26 10:41:47,125 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20241101_000002-nephron1_com-database.sql.gz
2025-06-26 10:45:58,207 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 migrate
2025-06-26 10:47:04,050 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 build
2025-06-26 10:48:02,305 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app frappe --branch version-15
2025-06-26 10:48:04,119 INFO App moved from apps/frappe to archived/apps/frappe-2025-06-26
2025-06-26 10:48:04,121 LOG Getting frappe
2025-06-26 10:48:04,121 DEBUG cd ./apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-26 10:49:30,651 LOG Installing frappe
2025-06-26 10:49:30,653 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-06-26 10:50:34,458 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-06-26 10:50:39,876 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-06-26 10:51:24,895 DEBUG bench build --app frappe
2025-06-26 10:51:25,267 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app frappe
2025-06-26 10:53:08,156 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20241101_000002-nephron1_com-database.sql.gz
2025-06-26 11:01:15,446 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20250626_000002-nephron1_com-database.sql.gz
2025-06-26 11:02:34,497 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app frappe --branch version-15
2025-06-26 11:02:36,277 INFO App moved from apps/frappe to archived/apps/frappe-2025-06-26_1
2025-06-26 11:02:36,279 LOG Getting frappe
2025-06-26 11:02:36,279 DEBUG cd ./apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-26 11:03:47,112 LOG Installing frappe
2025-06-26 11:03:47,114 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-06-26 11:03:52,897 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-06-26 11:03:56,532 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-06-26 11:04:19,035 DEBUG bench build --app frappe
2025-06-26 11:04:19,409 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app frappe
2025-06-26 11:09:01,217 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20250626_000002-nephron1_com-database.sql.gz
2025-06-26 11:11:02,808 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20250626_000002-nephron1_com-database.sql.gz
2025-06-26 11:24:38,375 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 set-config db_name nephro1
2025-06-26 11:24:59,092 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 set-config db_password aakvatech
2025-06-26 11:25:15,486 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site nephro1 restore /home/<USER>/Downloads/20250626_000002-nephron1_com-database.sql.gz
2025-06-26 12:00:02,491 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-26 18:00:02,221 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-26 18:04:02,358 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use certifications
2025-06-26 18:04:06,568 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-26 18:04:07,118 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-26 18:04:07,126 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-26 18:04:07,127 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-26 18:04:07,133 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-27 12:00:02,082 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-27 12:03:07,287 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use beetle
2025-06-27 12:03:14,595 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-27 12:03:15,087 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-27 12:03:15,088 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-27 12:03:15,124 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-27 12:03:15,126 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-27 12:12:41,978 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app frappe version-15-hotfix
2025-06-27 12:12:43,440 WARNING /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app frappe version-15-hotfix executed with exit code 1
2025-06-27 12:13:15,178 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app frappe --branch version-15-hotfix
2025-06-27 12:13:17,055 INFO App moved from apps/frappe to archived/apps/frappe-2025-06-27
2025-06-27 12:13:17,065 LOG Getting frappe
2025-06-27 12:13:17,065 DEBUG cd ./apps && git clone https://github.com/frappe/frappe.git --branch version-15-hotfix --depth 1 --origin upstream
2025-06-27 12:13:31,093 LOG Installing frappe
2025-06-27 12:13:31,095 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-06-27 12:13:38,950 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-06-27 12:13:42,540 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-06-27 12:14:44,329 DEBUG bench build --app frappe
2025-06-27 12:14:47,431 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app frappe
2025-06-27 12:43:00,003 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use gadget
2025-06-27 12:43:03,716 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-27 12:43:03,966 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-27 12:43:03,969 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-27 12:43:04,004 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-27 12:43:04,016 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-27 16:01:50,732 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use gadget
2025-06-27 16:01:52,955 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-27 16:01:53,200 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-27 16:01:53,217 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-27 16:01:53,249 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-27 16:01:53,249 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-27 16:02:15,934 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-27 16:34:45,584 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-06-27 16:34:48,804 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-27 16:34:49,076 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-27 16:34:49,102 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-27 16:34:49,112 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-27 16:34:49,116 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-27 16:35:18,353 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-27 18:00:02,045 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-29 18:00:01,994 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-30 10:01:38,036 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-06-30 10:01:46,093 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-30 10:01:46,499 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-30 10:01:46,501 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-06-30 10:01:46,512 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-30 10:01:46,563 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-30 10:02:08,962 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-30 10:02:09,378 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8001
2025-06-30 10:02:09,378 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-30 10:02:09,379 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-30 10:02:09,409 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-30 10:02:16,565 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-06-30 12:00:02,201 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-30 12:57:54,426 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-06-30 12:58:01,043 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-30 12:58:01,450 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-30 12:58:01,463 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8001
2025-06-30 12:58:01,496 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-30 12:58:01,513 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-30 16:52:58,970 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-06-30 16:53:02,634 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-06-30 16:53:02,968 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8001
2025-06-30 16:53:02,971 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-06-30 16:53:03,014 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-06-30 16:53:03,014 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-06-30 18:00:01,336 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-01 10:47:16,165 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use alphaassociates.co.tz
2025-07-01 10:47:26,801 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-01 10:47:27,223 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-01 10:47:27,226 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8001
2025-07-01 10:47:27,236 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-01 10:47:27,247 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-01 10:47:54,284 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-01 10:47:54,569 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-01 10:47:54,600 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-01 10:47:54,627 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-01 10:47:54,631 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-01 10:50:15,167 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz restore /home/<USER>/Downloads/20241022_154543-alpha14-av_frappe_cloud-database.sql.gz
2025-07-01 10:53:29,592 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-01 10:53:58,775 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz remove-from-installed-apps alpha_tz
2025-07-01 10:54:09,684 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-01 10:54:50,889 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --help
2025-07-01 10:58:18,391 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz remove-from-installed-apps alpha_tz
2025-07-01 10:58:52,406 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench all migrate
2025-07-01 10:58:52,406 WARNING superuser privileges required for this command
2025-07-01 10:59:05,772 INFO /usr/local/bin/bench all migrate
2025-07-01 11:01:55,806 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench frappe --help
2025-07-01 11:02:02,713 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --help
2025-07-01 12:00:02,178 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-01 18:00:01,553 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-02 12:00:02,328 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-02 16:10:03,607 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use libms
2025-07-02 16:10:14,306 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use library ms
2025-07-02 16:10:25,686 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use libraryms
2025-07-02 16:10:29,155 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-02 16:10:29,613 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-02 16:10:29,644 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-02 16:10:29,650 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-02 16:10:29,656 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-02 18:00:02,156 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-03 00:00:01,683 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-03 12:00:02,125 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-03 18:00:02,027 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-04 08:57:55,264 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site attendance
2025-07-04 09:01:49,841 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site attendance install-app erpnext
2025-07-04 09:08:32,421 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site attendance install-app hrms
2025-07-04 09:09:12,214 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site attendance install-app csf_tz
2025-07-04 09:10:20,049 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site attendance install-app biometric_client
2025-07-04 09:16:00,571 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use attendance
2025-07-04 09:16:05,353 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 09:16:05,664 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-04 09:16:05,666 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 09:16:05,707 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 09:16:05,720 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 09:16:11,612 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-04 09:16:48,105 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site attendance set-maintenance-mode off
2025-07-04 12:00:04,957 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-04 12:14:47,624 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use attendance
2025-07-04 12:14:51,515 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 12:14:51,877 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 12:14:51,884 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-04 12:14:51,923 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 12:14:51,923 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 13:20:00,571 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use libraryms
2025-07-04 13:20:02,902 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 13:20:03,219 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 13:20:03,225 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-04 13:20:03,229 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 13:20:03,240 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 13:20:22,770 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-04 13:28:39,486 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/aakvatech/fleet_ms.git --branch version-15
2025-07-04 13:28:39,499 LOG Getting fleet_ms
2025-07-04 13:28:39,500 DEBUG cd ./apps && git clone https://github.com/aakvatech/fleet_ms.git --branch version-15 --depth 1 --origin upstream
2025-07-04 13:28:41,238 LOG Installing vsd_fleet_ms
2025-07-04 13:28:41,239 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms 
2025-07-04 13:28:48,709 DEBUG bench build --app vsd_fleet_ms
2025-07-04 13:28:48,881 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app vsd_fleet_ms
2025-07-04 13:28:52,034 INFO A newer version of bench is available: 5.25.7 → 5.25.9
2025-07-04 13:33:19,536 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 13:33:19,853 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 13:33:19,855 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 13:33:19,859 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 13:33:19,867 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-04 13:33:23,966 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-04 13:34:49,466 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-04 13:43:40,992 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-04 18:00:01,515 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-04 18:28:45,680 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-07-04 18:28:53,149 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-04 18:28:53,550 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-04 18:28:53,555 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-04 18:28:53,590 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-04 18:28:53,591 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-08 09:20:59,121 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use libraryms
2025-07-08 09:21:04,024 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-08 09:21:04,376 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-08 09:21:04,382 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-08 09:21:04,423 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-08 09:21:04,427 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-08 11:39:23,660 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 11:43:18,302 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-08 12:00:02,150 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-08 18:00:01,828 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-09 09:34:38,155 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site neel
2025-07-09 09:35:59,872 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel install-app erpnext
2025-07-09 09:37:28,095 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel install-app hrms
2025-07-09 09:38:16,099 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel install-app csf_tz
2025-07-09 09:39:13,436 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel install-app payware
2025-07-09 09:39:28,191 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel install-app staff_loans
2025-07-09 09:39:36,696 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use neel
2025-07-09 09:39:40,778 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-09 09:39:41,147 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-09 09:39:41,150 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-09 09:39:41,176 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-09 09:39:41,187 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-09 09:40:09,027 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 09:40:41,141 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel set-maintenance-mode off
2025-07-09 12:00:02,112 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-09 17:44:52,557 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use neel
2025-07-09 17:44:56,042 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-09 17:44:56,325 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-09 17:44:56,327 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-09 17:44:56,329 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-09 17:44:56,346 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-09 17:46:45,270 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-09 17:57:04,766 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-09 17:57:05,170 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-09 17:57:05,172 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-09 17:57:05,192 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-09 17:57:05,201 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-09 17:58:11,691 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel execute csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles
2025-07-09 18:00:01,747 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-10 12:00:01,749 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-10 18:00:01,958 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-11 00:00:01,823 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-11 12:00:01,787 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-11 16:31:24,882 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use attendance
2025-07-11 16:31:29,903 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-11 16:31:30,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-11 16:31:30,454 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-11 16:31:30,484 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-11 16:31:30,484 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-11 17:05:16,185 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use attendance
2025-07-11 17:05:22,438 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-11 17:05:23,180 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-11 17:05:23,189 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-11 17:05:23,261 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-11 17:05:23,281 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-11 17:32:03,296 INFO /home/<USER>/.local/bin/bench migrate
2025-07-11 17:35:05,898 INFO /home/<USER>/.local/bin/bench --site execute biometric_client.doctype.biometric_data_staging.enqueue_process_biometric_logs
2025-07-11 17:35:23,475 INFO /home/<USER>/.local/bin/bench --site attendance execute biometric_client.doctype.biometric_data_staging.enqueue_process_biometric_logs
2025-07-11 17:35:40,201 INFO /home/<USER>/.local/bin/bench --site attendance execute biometric_client.biometric_client.doctype.biometric_data_staging.enqueue_process_biometric_logs
2025-07-11 17:36:18,076 INFO /home/<USER>/.local/bin/bench --site attendance execute biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.enqueue_process_biometric_logs
2025-07-11 17:40:51,452 INFO /home/<USER>/.local/bin/bench migrate
2025-07-11 17:58:01,908 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use attendance
2025-07-11 17:58:03,946 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-11 17:58:04,256 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-11 17:58:04,258 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-11 17:58:04,263 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-11 17:58:04,266 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-11 17:58:26,811 INFO /home/<USER>/.local/bin/bench migrate
2025-07-11 18:00:02,149 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-14 12:00:02,203 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-14 18:00:01,594 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-15 00:00:02,607 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-15 12:00:02,123 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-15 12:13:58,234 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use masumin
2025-07-15 12:14:01,565 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-15 12:14:01,992 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-15 12:14:01,995 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-15 12:14:02,001 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-15 12:14:02,003 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-15 15:17:27,123 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench new-site avhelp
2025-07-15 15:18:37,353 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench get-app https://github.com/frappe/wiki.git
2025-07-15 15:18:37,366 LOG Getting wiki
2025-07-15 15:18:37,367 DEBUG cd ./apps && git clone https://github.com/frappe/wiki.git  --depth 1 --origin upstream
2025-07-15 15:18:41,044 LOG Installing wiki
2025-07-15 15:18:41,045 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-07-15 15:18:44,969 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-07-15 15:19:00,425 DEBUG bench build --app wiki
2025-07-15 15:19:00,580 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench build --app wiki
2025-07-15 15:19:05,497 INFO A newer version of bench is available: 5.25.7 → 5.25.9
2025-07-15 15:19:18,773 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp install-app helpdesk
2025-07-15 15:19:28,535 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp install-app helpdesk
2025-07-15 15:19:35,181 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp install-app wiki
2025-07-15 15:19:40,817 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp install-app wiki
2025-07-15 15:19:50,618 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use avhelp
2025-07-15 15:19:54,430 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-15 15:19:54,765 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-15 15:19:54,783 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-15 15:19:54,815 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-15 15:19:54,821 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-15 15:20:09,444 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-15 15:20:44,643 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp set-maintenance-mode off
2025-07-15 15:21:35,139 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp restore /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-files.tar
2025-07-15 15:27:33,183 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp restore /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-database.sql.gz
2025-07-15 15:32:01,413 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp restore /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-database.sql.gz --with-public-files /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-files.tar --with-private-files /home/<USER>/Downloads 20250715_151304-support-15-av_frappe_cloud-private-files.tar
2025-07-15 15:32:17,512 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site avhelp restore /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-database.sql.gz --with-public-files /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-files.tar --with-private-files /home/<USER>/Downloads/20250715_151304-support-15-av_frappe_cloud-private-files.tar
2025-07-15 18:00:01,365 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-15 23:49:00,206 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-15 23:49:25,790 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-15 23:49:26,291 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-15 23:49:26,296 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-15 23:49:26,327 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-15 23:49:26,338 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-15 23:51:02,253 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-16 00:00:03,728 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-16 06:00:02,196 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-16 09:24:14,022 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-16 09:24:26,631 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 09:24:27,265 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 09:24:27,267 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 09:24:27,269 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 09:24:27,308 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 09:25:38,894 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-16 09:47:25,086 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 09:47:25,439 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 09:47:25,447 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 09:47:25,448 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 09:47:25,454 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 11:35:03,823 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 11:35:04,550 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 11:35:04,550 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 11:35:04,553 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 11:35:04,555 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 12:00:03,021 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-16 14:07:19,377 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-16 14:07:26,174 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 14:07:26,621 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 14:07:26,627 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 14:07:26,628 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 14:07:26,642 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 15:20:14,430 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-16 15:20:22,913 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 15:20:23,604 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 15:20:23,725 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 15:20:23,841 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 15:20:23,880 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 18:00:01,639 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-16 18:08:33,142 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-16 18:08:36,371 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 18:08:36,658 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 18:08:36,659 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 18:08:36,700 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 18:08:36,713 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 18:48:23,955 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 18:48:24,317 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 18:48:24,326 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 18:48:24,355 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 18:48:24,359 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-16 23:44:23,612 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-16 23:44:29,701 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-16 23:44:30,354 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-16 23:44:30,358 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-16 23:44:30,371 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-16 23:44:30,397 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-17 00:00:02,149 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-17 12:00:02,197 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-17 18:55:35,447 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-17 18:55:47,898 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-17 18:55:48,365 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-17 18:55:48,368 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-17 18:55:48,381 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-17 18:55:48,395 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-18 00:00:01,739 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-18 06:00:02,463 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-18 12:00:01,925 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-18 18:00:02,324 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-18 18:16:09,445 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-18 18:16:13,093 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-18 18:16:13,577 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-18 18:16:13,578 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-18 18:16:13,590 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-18 18:16:13,594 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-19 07:58:33,720 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-19 07:58:41,985 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-19 07:58:42,265 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-19 07:58:42,286 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-19 07:58:42,293 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-19 07:58:42,302 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-21 09:03:53,903 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-21 09:03:57,710 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-21 09:03:58,023 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-21 09:03:58,026 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-21 09:03:58,027 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-21 09:03:58,034 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-21 10:07:36,426 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel
2025-07-21 10:07:44,833 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel list-apps
2025-07-21 10:08:19,421 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel restore /home/<USER>/Downloads/20250714_145558-neelkanth14_frappe_cloud-database.sql.gz
2025-07-21 12:00:02,305 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-21 16:37:54,386 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-21 16:37:56,904 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-21 16:37:57,200 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-21 16:37:57,211 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-21 16:37:57,238 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-21 16:37:57,242 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-21 17:56:48,919 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use clearing
2025-07-21 17:57:03,263 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-21 17:57:04,036 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-21 17:57:04,148 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-21 17:57:04,233 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-21 17:57:04,259 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-21 18:00:01,852 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-22 08:35:42,045 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench use neel
2025-07-22 08:36:37,500 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench start
2025-07-22 08:36:40,535 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench serve --port 8000
2025-07-22 08:36:40,542 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench schedule
2025-07-22 08:36:40,542 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench watch
2025-07-22 08:36:40,553 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench worker
2025-07-22 08:38:22,186 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 08:43:45,699 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site neel set-admin-password aakvatech
2025-07-22 08:45:25,040 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 08:46:23,000 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 08:59:04,421 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 09:03:11,489 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 09:04:12,478 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 10:09:56,333 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 10:21:56,658 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 10:57:44,218 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-22 12:00:02,030 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-22 12:35:24,206 INFO /home/<USER>/.local/bin/bench use neel
2025-07-22 12:35:30,124 INFO /home/<USER>/.local/bin/bench start
2025-07-22 12:35:31,069 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-22 12:35:31,084 INFO /home/<USER>/.local/bin/bench schedule
2025-07-22 12:35:31,111 INFO /home/<USER>/.local/bin/bench worker
2025-07-22 12:35:31,121 INFO /home/<USER>/.local/bin/bench watch
2025-07-22 13:00:41,713 INFO /home/<USER>/.local/bin/bench use avhelp
2025-07-22 13:00:45,342 INFO /home/<USER>/.local/bin/bench start
2025-07-22 13:00:45,824 INFO /home/<USER>/.local/bin/bench watch
2025-07-22 13:00:45,835 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-22 13:00:45,885 INFO /home/<USER>/.local/bin/bench worker
2025-07-22 13:00:45,890 INFO /home/<USER>/.local/bin/bench schedule
2025-07-22 14:39:33,971 INFO /home/<USER>/.local/bin/bench use neel
2025-07-22 14:39:36,392 INFO /home/<USER>/.local/bin/bench start
2025-07-22 14:39:36,723 INFO /home/<USER>/.local/bin/bench watch
2025-07-22 14:39:36,727 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-22 14:39:36,771 INFO /home/<USER>/.local/bin/bench schedule
2025-07-22 14:39:36,772 INFO /home/<USER>/.local/bin/bench worker
2025-07-22 18:00:01,843 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-22 18:03:24,491 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-22 18:03:32,480 INFO /home/<USER>/.local/bin/bench start
2025-07-22 18:03:33,134 INFO /home/<USER>/.local/bin/bench watch
2025-07-22 18:03:33,141 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-22 18:03:33,145 INFO /home/<USER>/.local/bin/bench schedule
2025-07-22 18:03:33,195 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 08:51:46,282 INFO /home/<USER>/.local/bin/bench use neel
2025-07-23 08:51:52,763 INFO /home/<USER>/.local/bin/bench start
2025-07-23 08:51:53,550 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 08:51:53,560 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 08:51:53,561 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 08:51:53,571 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 09:35:32,082 INFO /home/<USER>/.local/bin/bench use certification
2025-07-23 09:35:35,217 INFO /home/<USER>/.local/bin/bench use certifications
2025-07-23 09:35:37,927 INFO /home/<USER>/.local/bin/bench start
2025-07-23 09:35:38,247 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 09:35:38,249 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 09:35:38,292 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 09:35:38,292 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 09:35:54,153 INFO /home/<USER>/.local/bin/bench migrate
2025-07-23 09:37:58,317 INFO /home/<USER>/.local/bin/bench migrate
2025-07-23 10:21:41,919 INFO /home/<USER>/.local/bin/bench start
2025-07-23 10:21:42,431 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 10:21:42,436 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 10:21:42,444 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 10:21:42,449 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 10:22:12,864 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-23 10:22:15,950 INFO /home/<USER>/.local/bin/bench start
2025-07-23 10:22:16,220 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 10:22:16,230 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 10:22:16,280 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 10:22:16,291 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 11:14:06,699 INFO /home/<USER>/.local/bin/bench use neel
2025-07-23 11:14:09,230 INFO /home/<USER>/.local/bin/bench start
2025-07-23 11:14:09,766 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 11:14:09,772 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 11:14:09,773 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 11:14:09,795 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 12:00:01,768 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-23 14:33:34,283 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-23 14:33:39,105 INFO /home/<USER>/.local/bin/bench start
2025-07-23 14:33:39,554 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 14:33:39,555 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 14:33:39,557 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 14:33:39,561 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 15:07:13,661 INFO /home/<USER>/.local/bin/bench use gadget
2025-07-23 15:07:17,818 INFO /home/<USER>/.local/bin/bench start
2025-07-23 15:07:18,311 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 15:07:18,314 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 15:07:18,361 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 15:07:18,380 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 15:15:29,650 INFO /home/<USER>/.local/bin/bench use masumin
2025-07-23 15:15:32,319 INFO /home/<USER>/.local/bin/bench start
2025-07-23 15:15:32,773 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 15:15:32,785 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 15:15:32,827 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 15:15:32,845 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 15:19:38,100 INFO /home/<USER>/.local/bin/bench use masumin
2025-07-23 15:19:46,367 INFO /home/<USER>/.local/bin/bench start
2025-07-23 15:19:47,165 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 15:19:47,213 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 15:19:47,269 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 15:19:47,293 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 15:22:15,309 INFO /home/<USER>/.local/bin/bench migrate
2025-07-23 15:22:54,222 INFO /home/<USER>/.local/bin/bench migrate
2025-07-23 15:24:53,635 INFO /home/<USER>/.local/bin/bench migrate
2025-07-23 15:38:59,173 INFO /home/<USER>/.local/bin/bench start
2025-07-23 15:38:59,580 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 15:38:59,580 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 15:38:59,581 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 15:38:59,592 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 16:05:01,160 INFO /home/<USER>/.local/bin/bench start
2025-07-23 16:05:01,477 INFO /home/<USER>/.local/bin/bench watch
2025-07-23 16:05:01,478 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-23 16:05:01,491 INFO /home/<USER>/.local/bin/bench worker
2025-07-23 16:05:01,500 INFO /home/<USER>/.local/bin/bench schedule
2025-07-23 16:18:13,234 INFO /home/<USER>/.local/bin/bench install requirements
2025-07-23 16:18:13,234 WARNING superuser privileges required for this command
2025-07-23 16:18:23,423 INFO /usr/local/bin/bench install requirements
2025-07-23 16:18:23,430 WARNING /usr/local/bin/bench install requirements executed with exit code 2
2025-07-23 16:18:24,406 INFO A newer version of bench is available: 5.22.9 → 5.25.9
2025-07-23 16:18:42,346 INFO /usr/local/bin/bench install requirement
2025-07-23 16:18:42,351 WARNING /usr/local/bin/bench install requirement executed with exit code 2
2025-07-23 16:18:42,642 INFO A newer version of bench is available: 5.22.9 → 5.25.9
2025-07-23 16:19:11,975 INFO /home/<USER>/.local/bin/bench setup requirements
2025-07-23 16:19:11,985 DEBUG /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-07-23 16:19:14,956 LOG Installing frappe
2025-07-23 16:19:14,957 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe 
2025-07-23 16:19:18,072 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-07-23 16:19:21,998 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/frappe && yarn install --check-files
2025-07-23 16:19:24,351 LOG Installing staff_loans
2025-07-23 16:19:24,351 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/staff_loans 
2025-07-23 16:19:28,728 LOG Installing vsd_fleet_ms
2025-07-23 16:19:28,728 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vsd_fleet_ms 
2025-07-23 16:19:34,472 LOG Installing twilio_integration
2025-07-23 16:19:34,472 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/twilio_integration 
2025-07-23 16:19:39,628 LOG Installing hrms
2025-07-23 16:19:39,628 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hrms 
2025-07-23 16:19:42,408 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/hrms && yarn install --check-files
2025-07-23 16:20:18,655 LOG Installing lending
2025-07-23 16:20:18,655 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/lending 
2025-07-23 16:20:24,070 LOG Installing biometric_client
2025-07-23 16:20:24,070 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/biometric_client 
2025-07-23 16:20:28,109 LOG Installing erpbiometric_sync
2025-07-23 16:20:28,109 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpbiometric_sync 
2025-07-23 16:20:32,296 LOG Installing webshop
2025-07-23 16:20:32,296 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/webshop 
2025-07-23 16:20:36,376 LOG Installing vfd_providers
2025-07-23 16:20:36,376 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_providers 
2025-07-23 16:20:44,044 LOG Installing tenaciousfreightmaster
2025-07-23 16:20:44,045 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenaciousfreightmaster 
2025-07-23 16:20:48,533 LOG Installing airplane_mode
2025-07-23 16:20:48,533 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/airplane_mode 
2025-07-23 16:20:53,539 LOG Installing utility_management
2025-07-23 16:20:53,539 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/utility_management 
2025-07-23 16:20:56,896 LOG Installing csf_tz
2025-07-23 16:20:56,896 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/csf_tz 
2025-07-23 16:21:11,479 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/csf_tz && yarn install --check-files
2025-07-23 16:21:11,968 LOG Installing tenacious_integration
2025-07-23 16:21:11,968 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacious_integration 
2025-07-23 16:21:14,893 LOG Installing hms_tz
2025-07-23 16:21:14,894 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/hms_tz 
2025-07-23 16:21:23,475 LOG Installing velocetec
2025-07-23 16:21:23,475 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/velocetec 
2025-07-23 16:21:26,514 LOG Installing payware
2025-07-23 16:21:26,514 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payware 
2025-07-23 16:21:32,320 LOG Installing rents
2025-07-23 16:21:32,320 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/rents 
2025-07-23 16:21:36,037 LOG Installing payments
2025-07-23 16:21:36,037 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/payments 
2025-07-23 16:21:38,999 LOG Installing rental_management_gateway
2025-07-23 16:21:38,999 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/rental_management_gateway 
2025-07-23 16:21:42,145 LOG Installing thps
2025-07-23 16:21:42,146 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/thps 
2025-07-23 16:21:45,429 LOG Installing fd_mis
2025-07-23 16:21:45,429 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/fd_mis 
2025-07-23 16:21:49,190 LOG Installing wiki
2025-07-23 16:21:49,190 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/wiki 
2025-07-23 16:21:52,673 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/wiki && yarn install --check-files
2025-07-23 16:21:53,614 LOG Installing erpnext_telegram_integration
2025-07-23 16:21:53,614 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext_telegram_integration 
2025-07-23 16:22:01,067 LOG Installing servicems
2025-07-23 16:22:01,068 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/servicems 
2025-07-23 16:22:33,071 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/servicems && yarn install --check-files
2025-07-23 16:22:35,234 LOG Installing tenacioustravel
2025-07-23 16:22:35,235 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacioustravel 
2025-07-23 16:22:39,343 LOG Installing clearing
2025-07-23 16:22:39,344 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/clearing 
2025-07-23 16:22:43,548 LOG Installing propms
2025-07-23 16:22:43,548 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/propms 
2025-07-23 16:22:52,774 LOG Installing healthcare
2025-07-23 16:22:52,774 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/healthcare 
2025-07-23 16:22:56,991 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -r ./apps/healthcare/dev-requirements.txt
2025-07-23 16:22:59,130 LOG Installing vfd_tz
2025-07-23 16:22:59,131 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/vfd_tz 
2025-07-23 16:23:08,388 LOG Installing helpdesk
2025-07-23 16:23:08,389 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/helpdesk 
2025-07-23 16:23:13,788 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/helpdesk && yarn install --check-files
2025-07-23 16:23:28,681 LOG Installing frappe_whatsapp
2025-07-23 16:23:28,681 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/frappe_whatsapp 
2025-07-23 16:23:32,641 LOG Installing fd
2025-07-23 16:23:32,641 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/fd 
2025-07-23 16:23:36,687 LOG Installing erpnext
2025-07-23 16:23:36,687 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/erpnext 
2025-07-23 16:23:40,454 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/erpnext && yarn install --check-files
2025-07-23 16:23:40,944 LOG Installing tenacious_hr
2025-07-23 16:23:40,945 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/tenacious_hr 
2025-07-23 16:23:45,214 LOG Installing flexible_budget
2025-07-23 16:23:45,215 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/flexible_budget 
2025-07-23 16:23:49,670 LOG Installing library_management
2025-07-23 16:23:49,671 DEBUG cd /home/<USER>/Desktop/frappe-bench && /home/<USER>/Desktop/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/Desktop/frappe-bench/apps/library_management 
2025-07-23 16:24:09,776 DEBUG cd /home/<USER>/Desktop/frappe-bench/apps/library_management && yarn install --check-files
2025-07-23 16:24:14,886 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-23 16:30:47,216 INFO /home/<USER>/.local/bin/bench console
2025-07-23 16:32:47,919 INFO /home/<USER>/.local/bin/bench console
2025-07-23 16:37:33,928 INFO /home/<USER>/.local/bin/bench migrate
2025-07-24 09:43:59,947 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-24 09:44:06,135 INFO /home/<USER>/.local/bin/bench start
2025-07-24 09:44:06,785 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-24 09:44:06,789 INFO /home/<USER>/.local/bin/bench watch
2025-07-24 09:44:06,798 INFO /home/<USER>/.local/bin/bench schedule
2025-07-24 09:44:06,812 INFO /home/<USER>/.local/bin/bench worker
2025-07-24 09:45:33,265 INFO /home/<USER>/.local/bin/bench use neel
2025-07-24 09:45:35,455 INFO /home/<USER>/.local/bin/bench start
2025-07-24 09:45:35,789 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-24 09:45:35,799 INFO /home/<USER>/.local/bin/bench watch
2025-07-24 09:45:35,804 INFO /home/<USER>/.local/bin/bench worker
2025-07-24 09:45:35,807 INFO /home/<USER>/.local/bin/bench schedule
2025-07-24 12:00:01,429 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-24 15:09:00,604 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-24 15:09:03,133 INFO /home/<USER>/.local/bin/bench start
2025-07-24 15:09:03,691 INFO /home/<USER>/.local/bin/bench schedule
2025-07-24 15:09:03,692 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-24 15:09:03,708 INFO /home/<USER>/.local/bin/bench worker
2025-07-24 15:09:03,723 INFO /home/<USER>/.local/bin/bench watch
2025-07-24 16:39:12,345 INFO /home/<USER>/.local/bin/bench restart
2025-07-24 16:39:12,406 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-07-24 16:39:12,829 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-24 16:39:48,216 INFO /home/<USER>/.local/bin/bench start
2025-07-24 16:39:48,718 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-24 16:39:48,746 INFO /home/<USER>/.local/bin/bench worker
2025-07-24 16:39:48,746 INFO /home/<USER>/.local/bin/bench watch
2025-07-24 16:39:48,750 INFO /home/<USER>/.local/bin/bench schedule
2025-07-24 18:00:02,161 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-25 10:16:43,816 INFO /home/<USER>/.local/bin/bench use helpdesk
2025-07-25 10:16:50,169 INFO /home/<USER>/.local/bin/bench start
2025-07-25 10:16:50,793 INFO /home/<USER>/.local/bin/bench watch
2025-07-25 10:16:50,813 INFO /home/<USER>/.local/bin/bench schedule
2025-07-25 10:16:50,815 INFO /home/<USER>/.local/bin/bench worker
2025-07-25 10:16:50,816 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-25 10:17:13,353 INFO /home/<USER>/.local/bin/bench migrate
2025-07-25 10:17:33,528 INFO /home/<USER>/.local/bin/bench start
2025-07-25 10:17:33,843 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-25 10:17:33,845 INFO /home/<USER>/.local/bin/bench watch
2025-07-25 10:17:33,854 INFO /home/<USER>/.local/bin/bench worker
2025-07-25 10:17:33,858 INFO /home/<USER>/.local/bin/bench schedule
2025-07-25 10:20:11,108 INFO /home/<USER>/.local/bin/bench use thps.or.tz
2025-07-25 10:20:13,789 INFO /home/<USER>/.local/bin/bench start
2025-07-25 10:20:14,111 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-25 10:20:14,123 INFO /home/<USER>/.local/bin/bench watch
2025-07-25 10:20:14,151 INFO /home/<USER>/.local/bin/bench schedule
2025-07-25 10:20:14,162 INFO /home/<USER>/.local/bin/bench worker
2025-07-25 10:20:31,073 INFO /home/<USER>/.local/bin/bench migrate
2025-07-25 10:20:37,742 INFO /home/<USER>/.local/bin/bench start
2025-07-25 10:20:38,031 INFO /home/<USER>/.local/bin/bench watch
2025-07-25 10:20:38,033 INFO /home/<USER>/.local/bin/bench schedule
2025-07-25 10:20:38,040 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-25 10:20:38,061 INFO /home/<USER>/.local/bin/bench worker
2025-07-25 12:00:01,376 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-25 12:18:28,467 INFO /home/<USER>/.local/bin/bench migrate
2025-07-25 14:41:15,788 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-25 14:41:19,895 INFO /home/<USER>/.local/bin/bench start
2025-07-25 14:41:20,502 INFO /home/<USER>/.local/bin/bench schedule
2025-07-25 14:41:20,508 INFO /home/<USER>/.local/bin/bench watch
2025-07-25 14:41:20,553 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-25 14:41:20,563 INFO /home/<USER>/.local/bin/bench worker
2025-07-25 18:00:01,589 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-26 10:40:48,505 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-26 10:40:53,622 INFO /home/<USER>/.local/bin/bench start
2025-07-26 10:40:54,698 INFO /home/<USER>/.local/bin/bench worker
2025-07-26 10:40:54,705 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-26 10:40:54,705 INFO /home/<USER>/.local/bin/bench watch
2025-07-26 10:40:54,719 INFO /home/<USER>/.local/bin/bench schedule
2025-07-26 12:00:02,223 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-26 19:49:42,817 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-27 12:00:02,427 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-28 00:00:01,682 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-28 06:00:01,388 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-28 18:00:01,697 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-28 20:28:00,754 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-28 20:28:07,936 INFO /home/<USER>/.local/bin/bench start
2025-07-28 20:28:08,833 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-28 20:28:08,878 INFO /home/<USER>/.local/bin/bench watch
2025-07-28 20:28:08,884 INFO /home/<USER>/.local/bin/bench schedule
2025-07-28 20:28:08,884 INFO /home/<USER>/.local/bin/bench worker
2025-07-28 20:52:51,044 INFO /home/<USER>/.local/bin/bench start
2025-07-28 20:52:52,066 INFO /home/<USER>/.local/bin/bench schedule
2025-07-28 20:52:52,068 INFO /home/<USER>/.local/bin/bench watch
2025-07-28 20:52:52,077 INFO /home/<USER>/.local/bin/bench worker
2025-07-28 20:52:52,078 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-28 23:34:51,808 INFO /home/<USER>/.local/bin/bench start
2025-07-28 23:34:53,315 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-28 23:34:53,317 INFO /home/<USER>/.local/bin/bench watch
2025-07-28 23:34:53,321 INFO /home/<USER>/.local/bin/bench schedule
2025-07-28 23:34:53,346 INFO /home/<USER>/.local/bin/bench worker
2025-07-29 09:27:16,657 INFO /home/<USER>/.local/bin/bench use clearing
2025-07-29 09:27:27,026 INFO /home/<USER>/.local/bin/bench start
2025-07-29 09:27:27,774 INFO /home/<USER>/.local/bin/bench watch
2025-07-29 09:27:27,777 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-29 09:27:27,778 INFO /home/<USER>/.local/bin/bench schedule
2025-07-29 09:27:27,794 INFO /home/<USER>/.local/bin/bench worker
2025-07-29 09:41:01,598 INFO /home/<USER>/.local/bin/bench use avhelp
2025-07-29 09:41:06,766 INFO /home/<USER>/.local/bin/bench start
2025-07-29 09:41:07,234 INFO /home/<USER>/.local/bin/bench worker
2025-07-29 09:41:07,240 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-29 09:41:07,248 INFO /home/<USER>/.local/bin/bench watch
2025-07-29 09:41:07,250 INFO /home/<USER>/.local/bin/bench schedule
2025-07-29 09:52:29,528 INFO /home/<USER>/.local/bin/bench migrate
2025-07-29 09:52:51,915 INFO /home/<USER>/.local/bin/bench start
2025-07-29 09:52:52,881 INFO /home/<USER>/.local/bin/bench watch
2025-07-29 09:52:52,896 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-29 09:52:52,908 INFO /home/<USER>/.local/bin/bench worker
2025-07-29 09:52:52,912 INFO /home/<USER>/.local/bin/bench schedule
2025-07-29 09:52:57,896 INFO /home/<USER>/.local/bin/bench migrate
2025-07-29 09:53:22,152 INFO /home/<USER>/.local/bin/bench --site avhelp build
2025-07-29 12:00:01,844 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-29 18:00:01,765 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-30 12:00:02,483 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-30 18:00:01,315 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-31 09:47:32,359 INFO /home/<USER>/.local/bin/bench use neel
2025-07-31 09:47:41,538 INFO /home/<USER>/.local/bin/bench start
2025-07-31 09:47:42,254 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 09:47:42,256 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 09:47:42,257 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 09:47:42,258 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 09:48:35,689 INFO /home/<USER>/.local/bin/bench migrate
2025-07-31 12:00:01,403 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-31 12:07:37,762 INFO /home/<USER>/.local/bin/bench use masumin
2025-07-31 12:07:40,232 INFO /home/<USER>/.local/bin/bench start
2025-07-31 12:07:40,646 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 12:07:40,654 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 12:07:40,685 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 12:07:40,686 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 14:50:45,240 INFO /home/<USER>/.local/bin/bench use alphaassociates.co.tz
2025-07-31 14:50:51,355 INFO /home/<USER>/.local/bin/bench start
2025-07-31 14:50:52,151 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 14:50:52,151 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 14:50:52,198 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 14:50:52,198 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 14:52:20,999 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz remove-from-installed-apps alpha_tz
2025-07-31 14:52:31,114 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-31 14:52:43,728 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate --force
2025-07-31 14:52:50,510 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate --f
2025-07-31 14:52:58,449 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-31 14:55:35,620 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench migrate
2025-07-31 14:56:36,075 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz migrate
2025-07-31 14:58:29,892 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz migrate
2025-07-31 14:58:58,073 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz migrate
2025-07-31 15:00:54,100 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz list-apps
2025-07-31 15:01:41,606 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz install-app alpha_tz
2025-07-31 15:01:52,105 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz uninstall-app alpha_tz
2025-07-31 15:02:17,049 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app alpha_tz
2025-07-31 15:02:17,053 WARNING /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove-app alpha_tz executed with exit code 2
2025-07-31 15:02:17,514 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-31 15:02:24,751 INFO /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove alpha_tz
2025-07-31 15:02:24,754 WARNING /home/<USER>/.local/bin/bench --site alphaassociates.co.tz remove alpha_tz executed with exit code 2
2025-07-31 15:02:25,091 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-31 15:02:44,398 INFO /home/<USER>/.local/bin/bench start
2025-07-31 15:02:44,899 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 15:02:44,903 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 15:02:44,922 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 15:02:44,942 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 15:02:47,938 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench clear-cache
2025-07-31 15:02:48,792 INFO /home/<USER>/Desktop/frappe-bench/env/bin/bench --site alphaassociates.co.tz migrate
2025-07-31 15:09:13,235 INFO /home/<USER>/.local/bin/bench new-site eaf
2025-07-31 15:10:18,934 INFO /home/<USER>/.local/bin/bench --site eaf install-app erpnext
2025-07-31 15:20:41,375 INFO /home/<USER>/.local/bin/bench --site eaf install-app hrms
2025-07-31 15:21:12,177 INFO /home/<USER>/.local/bin/bench --site eaf install-app hrms
2025-07-31 15:21:17,964 INFO /home/<USER>/.local/bin/bench --site eaf install-app csf_tz
2025-07-31 15:21:25,064 INFO /home/<USER>/.local/bin/bench --site eaf install-app csf_tz
2025-07-31 15:22:12,415 INFO /home/<USER>/.local/bin/bench --site eaf list-apps
2025-07-31 15:22:58,881 INFO /home/<USER>/.local/bin/bench use attendance
2025-07-31 15:23:04,862 INFO /home/<USER>/.local/bin/bench start
2025-07-31 15:23:05,133 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 15:23:05,165 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 15:23:05,169 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 15:23:05,183 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 16:11:57,780 INFO /home/<USER>/.local/bin/bench push upstream empch
2025-07-31 16:37:00,349 INFO /home/<USER>/.local/bin/bench use masumin
2025-07-31 16:37:04,286 INFO /home/<USER>/.local/bin/bench start
2025-07-31 16:37:30,000 INFO /home/<USER>/.local/bin/bench restart
2025-07-31 16:37:30,012 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-07-31 16:37:30,523 INFO A newer version of bench is available: 5.23.0 → 5.25.9
2025-07-31 16:38:59,149 INFO /home/<USER>/.local/bin/bench use masumin
2025-07-31 16:39:05,691 INFO /home/<USER>/.local/bin/bench start
2025-07-31 16:39:06,487 INFO /home/<USER>/.local/bin/bench schedule
2025-07-31 16:39:06,496 INFO /home/<USER>/.local/bin/bench watch
2025-07-31 16:39:06,507 INFO /home/<USER>/.local/bin/bench worker
2025-07-31 16:39:06,509 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-07-31 18:00:01,762 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-01 09:10:31,942 INFO /home/<USER>/.local/bin/bench use attendance
2025-08-01 09:10:41,892 INFO /home/<USER>/.local/bin/bench start
2025-08-01 09:10:42,993 INFO /home/<USER>/.local/bin/bench watch
2025-08-01 09:10:42,998 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-01 09:10:43,001 INFO /home/<USER>/.local/bin/bench worker
2025-08-01 09:10:43,022 INFO /home/<USER>/.local/bin/bench schedule
2025-08-01 11:02:56,184 INFO /home/<USER>/.local/bin/bench use masumin
2025-08-01 11:03:00,807 INFO /home/<USER>/.local/bin/bench start
2025-08-01 11:03:01,203 INFO /home/<USER>/.local/bin/bench watch
2025-08-01 11:03:01,218 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-01 11:03:01,239 INFO /home/<USER>/.local/bin/bench worker
2025-08-01 11:03:01,247 INFO /home/<USER>/.local/bin/bench schedule
2025-08-01 12:00:02,329 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-01 13:20:18,618 INFO /home/<USER>/.local/bin/bench use masumin
2025-08-01 13:20:22,698 INFO /home/<USER>/.local/bin/bench start
2025-08-01 13:20:23,080 INFO /home/<USER>/.local/bin/bench worker
2025-08-01 13:20:23,082 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-01 13:20:23,085 INFO /home/<USER>/.local/bin/bench schedule
2025-08-01 13:20:23,087 INFO /home/<USER>/.local/bin/bench watch
2025-08-01 18:00:01,300 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-01 18:08:41,751 INFO /home/<USER>/.local/bin/bench use masumin
2025-08-01 18:08:49,272 INFO /home/<USER>/.local/bin/bench start
2025-08-01 18:08:50,021 INFO /home/<USER>/.local/bin/bench schedule
2025-08-01 18:08:50,085 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-01 18:08:50,085 INFO /home/<USER>/.local/bin/bench worker
2025-08-01 18:08:50,199 INFO /home/<USER>/.local/bin/bench watch
2025-08-04 10:02:26,795 INFO /home/<USER>/.local/bin/bench use masumin
2025-08-04 10:02:30,804 INFO /home/<USER>/.local/bin/bench start
2025-08-04 10:02:31,351 INFO /home/<USER>/.local/bin/bench worker
2025-08-04 10:02:31,375 INFO /home/<USER>/.local/bin/bench watch
2025-08-04 10:02:31,383 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-04 10:02:31,414 INFO /home/<USER>/.local/bin/bench schedule
2025-08-04 11:10:34,089 INFO /home/<USER>/.local/bin/bench use neel
2025-08-04 11:10:37,410 INFO /home/<USER>/.local/bin/bench start
2025-08-04 11:10:37,835 INFO /home/<USER>/.local/bin/bench schedule
2025-08-04 11:10:37,836 INFO /home/<USER>/.local/bin/bench worker
2025-08-04 11:10:37,842 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-08-04 11:10:37,847 INFO /home/<USER>/.local/bin/bench watch
2025-08-04 12:00:01,544 INFO /usr/local/bin/bench --verbose --site all backup
